import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import Button from '@/components/ui/Button';
import { Palette, Monitor, Sun, Moon, Layout, Type } from 'lucide-react';

const AppearanceSettings: React.FC = () => {
  const queryClient = useQueryClient();

  // Fetch appearance preferences
  const { data: preferences, isLoading } = useQuery({
    queryKey: ['appearance-preferences'],
    queryFn: async () => {
      // Mock API call
      return {
        theme: 'system', // 'light', 'dark', 'system'
        layout: 'default', // 'default', 'compact', 'spacious'
        font_size: 'medium', // 'small', 'medium', 'large'
        sidebar_collapsed: false,
        show_animations: true,
        high_contrast: false,
        color_scheme: 'blue', // 'blue', 'green', 'purple', 'orange'
      };
    },
  });

  const [localPreferences, setLocalPreferences] = useState<any>(null);

  React.useEffect(() => {
    if (preferences && !localPreferences) {
      setLocalPreferences(preferences);
    }
  }, [preferences, localPreferences]);

  // Update preferences mutation
  const updatePreferencesMutation = useMutation({
    mutationFn: async (data: any) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['appearance-preferences'] });
    },
  });

  const handleChange = (field: string, value: any) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = () => {
    updatePreferencesMutation.mutate(localPreferences);
  };

  if (isLoading || !localPreferences) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
            <div className="h-20 bg-surface-tertiary rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  const themes = [
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon },
    { value: 'system', label: 'System', icon: Monitor },
  ];

  const layouts = [
    { value: 'default', label: 'Default', description: 'Standard layout with balanced spacing' },
    { value: 'compact', label: 'Compact', description: 'Tighter spacing for more content' },
    { value: 'spacious', label: 'Spacious', description: 'More breathing room between elements' },
  ];

  const fontSizes = [
    { value: 'small', label: 'Small' },
    { value: 'medium', label: 'Medium' },
    { value: 'large', label: 'Large' },
  ];

  const colorSchemes = [
    { value: 'blue', label: 'Blue', color: '#3B82F6' },
    { value: 'green', label: 'Green', color: '#10B981' },
    { value: 'purple', label: 'Purple', color: '#8B5CF6' },
    { value: 'orange', label: 'Orange', color: '#F59E0B' },
  ];

  return (
    <div className="space-y-8">
      {/* Theme */}
      <div>
        <div className="flex items-center mb-4">
          <Palette className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Theme</h4>
        </div>
        <div className="grid grid-cols-3 gap-3">
          {themes.map((theme) => {
            const Icon = theme.icon;
            return (
              <button
                key={theme.value}
                onClick={() => handleChange('theme', theme.value)}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  localPreferences.theme === theme.value
                    ? 'border-primary bg-primary/5'
                    : 'border-surface-tertiary hover:border-surface-quaternary'
                }`}
              >
                <Icon className="w-6 h-6 mx-auto mb-2 text-text-primary" />
                <p className="text-sm font-medium text-text-primary">{theme.label}</p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Color Scheme */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Color Scheme</h4>
        <div className="grid grid-cols-4 gap-3">
          {colorSchemes.map((scheme) => (
            <button
              key={scheme.value}
              onClick={() => handleChange('color_scheme', scheme.value)}
              className={`p-4 rounded-lg border-2 transition-colors ${
                localPreferences.color_scheme === scheme.value
                  ? 'border-primary bg-primary/5'
                  : 'border-surface-tertiary hover:border-surface-quaternary'
              }`}
            >
              <div
                className="w-8 h-8 rounded-full mx-auto mb-2"
                style={{ backgroundColor: scheme.color }}
              />
              <p className="text-sm font-medium text-text-primary">{scheme.label}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Layout */}
      <div>
        <div className="flex items-center mb-4">
          <Layout className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Layout</h4>
        </div>
        <div className="space-y-3">
          {layouts.map((layout) => (
            <label
              key={layout.value}
              className={`flex items-start p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                localPreferences.layout === layout.value
                  ? 'border-primary bg-primary/5'
                  : 'border-surface-tertiary hover:border-surface-quaternary'
              }`}
            >
              <input
                type="radio"
                name="layout"
                value={layout.value}
                checked={localPreferences.layout === layout.value}
                onChange={(e) => handleChange('layout', e.target.value)}
                className="mt-1 text-primary focus:ring-primary"
              />
              <div className="ml-3">
                <p className="font-medium text-text-primary">{layout.label}</p>
                <p className="text-sm text-text-secondary">{layout.description}</p>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Font Size */}
      <div>
        <div className="flex items-center mb-4">
          <Type className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Font Size</h4>
        </div>
        <div className="grid grid-cols-3 gap-3">
          {fontSizes.map((size) => (
            <button
              key={size.value}
              onClick={() => handleChange('font_size', size.value)}
              className={`p-4 rounded-lg border-2 transition-colors ${
                localPreferences.font_size === size.value
                  ? 'border-primary bg-primary/5'
                  : 'border-surface-tertiary hover:border-surface-quaternary'
              }`}
            >
              <p
                className={`font-medium text-text-primary ${
                  size.value === 'small' ? 'text-sm' :
                  size.value === 'large' ? 'text-lg' : 'text-base'
                }`}
              >
                Aa
              </p>
              <p className="text-sm text-text-secondary mt-1">{size.label}</p>
            </button>
          ))}
        </div>
      </div>

      {/* Additional Options */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Additional Options</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
            <div>
              <p className="font-medium text-text-primary">Collapse Sidebar by Default</p>
              <p className="text-sm text-text-secondary">Start with a collapsed sidebar for more space</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.sidebar_collapsed}
                onChange={(e) => handleChange('sidebar_collapsed', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
            <div>
              <p className="font-medium text-text-primary">Show Animations</p>
              <p className="text-sm text-text-secondary">Enable smooth transitions and animations</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.show_animations}
                onChange={(e) => handleChange('show_animations', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
            <div>
              <p className="font-medium text-text-primary">High Contrast Mode</p>
              <p className="text-sm text-text-secondary">Increase contrast for better accessibility</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.high_contrast}
                onChange={(e) => handleChange('high_contrast', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Preview */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Preview</h4>
        <div className="p-6 bg-surface-secondary rounded-lg border border-surface-tertiary">
          <div className="flex items-center justify-between mb-4">
            <h5 className="text-lg font-semibold text-text-primary">Sample Dashboard</h5>
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
          </div>
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="p-3 bg-surface-tertiary rounded">
              <p className="text-sm text-text-secondary">Total Balance</p>
              <p className="text-lg font-bold text-text-primary">$12,345.67</p>
            </div>
            <div className="p-3 bg-surface-tertiary rounded">
              <p className="text-sm text-text-secondary">Daily P&L</p>
              <p className="text-lg font-bold text-green-600">+$234.56</p>
            </div>
            <div className="p-3 bg-surface-tertiary rounded">
              <p className="text-sm text-text-secondary">Active Positions</p>
              <p className="text-lg font-bold text-text-primary">8</p>
            </div>
          </div>
          <div className="h-32 bg-surface-tertiary rounded flex items-center justify-center">
            <p className="text-text-muted">Chart Preview</p>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={updatePreferencesMutation.isPending}
          className="bg-primary hover:bg-primary-dark"
        >
          {updatePreferencesMutation.isPending ? 'Saving...' : 'Save Appearance'}
        </Button>
      </div>
    </div>
  );
};

export default AppearanceSettings;
