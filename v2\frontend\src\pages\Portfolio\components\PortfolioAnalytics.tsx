import React from 'react';
import Card from '@/components/ui/Card';
import { <PERSON>hn<PERSON>, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface PortfolioAnalyticsProps {
  analytics: {
    performance: {
      total_return: number;
      total_return_percentage: number;
      annualized_return: number;
      volatility: number;
      sharpe_ratio: number;
      max_drawdown: number;
      winning_trades: number;
      losing_trades: number;
    };
    risk_metrics: {
      volatility: number;
      beta: number;
      alpha: number;
      correlation: number;
    };
    allocation: {
      token_symbol: string;
      percentage: number;
      value: number;
    }[];
  } | null;
  isLoading: boolean;
}

const PortfolioAnalytics: React.FC<PortfolioAnalyticsProps> = ({
  analytics,
  isLoading,
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[1, 2].map((i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
                <div className="h-64 bg-surface-tertiary rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold text-text-primary mb-4">No Analytics Data</h3>
        <p className="text-text-muted">
          Analytics data is not available for this portfolio yet.
        </p>
      </Card>
    );
  }

  // Prepare allocation chart data
  const allocationData = {
    labels: analytics.allocation.map(item => item.token_symbol),
    datasets: [
      {
        data: analytics.allocation.map(item => item.percentage),
        backgroundColor: [
          '#3B82F6', // Blue
          '#10B981', // Green
          '#F59E0B', // Yellow
          '#EF4444', // Red
          '#8B5CF6', // Purple
          '#F97316', // Orange
          '#06B6D4', // Cyan
          '#84CC16', // Lime
        ],
        borderWidth: 2,
        borderColor: '#1F2937',
      },
    ],
  };

  const allocationOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed || 0;
            const allocation = analytics.allocation[context.dataIndex];
            return `${label}: ${value.toFixed(1)}% (${formatCurrency(allocation.value)})`;
          },
        },
      },
    },
  };

  // Mock performance chart data
  const performanceData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Portfolio Return',
        data: [0, 2.5, 1.8, 4.2, 6.1, analytics.performance.total_return_percentage],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Benchmark',
        data: [0, 1.2, 0.8, 2.1, 3.5, 4.2],
        borderColor: 'rgb(156, 163, 175)',
        backgroundColor: 'rgba(156, 163, 175, 0.1)',
        tension: 0.4,
      },
    ],
  };

  const performanceOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Performance vs Benchmark',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            return formatPercentage(value);
          },
        },
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Total Return</div>
          <div
            className={`text-xl font-bold ${
              analytics.performance.total_return >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatCurrency(analytics.performance.total_return)}
          </div>
          <div
            className={`text-sm ${
              analytics.performance.total_return_percentage >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatPercentage(analytics.performance.total_return_percentage)}
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Annualized Return</div>
          <div
            className={`text-xl font-bold ${
              analytics.performance.annualized_return >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatPercentage(analytics.performance.annualized_return)}
          </div>
          <div className="text-sm text-text-muted">Yearly return rate</div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Sharpe Ratio</div>
          <div className="text-xl font-bold text-text-primary">
            {analytics.performance.sharpe_ratio.toFixed(2)}
          </div>
          <div className="text-sm text-text-muted">Risk-adjusted return</div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Max Drawdown</div>
          <div className="text-xl font-bold text-red-600">
            {formatPercentage(analytics.performance.max_drawdown)}
          </div>
          <div className="text-sm text-text-muted">Largest decline</div>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Asset Allocation */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Asset Allocation</h3>
          <div className="h-64">
            <Doughnut data={allocationData} options={allocationOptions} />
          </div>
        </Card>

        {/* Performance Chart */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Performance Trend</h3>
          <div className="h-64">
            <Line data={performanceData} options={performanceOptions} />
          </div>
        </Card>
      </div>

      {/* Risk Metrics */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Risk Metrics</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <div className="text-sm text-text-secondary mb-1">Volatility</div>
            <div className="text-xl font-bold text-text-primary">
              {formatPercentage(analytics.risk_metrics.volatility)}
            </div>
            <div className="text-sm text-text-muted">Price volatility</div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Beta</div>
            <div className="text-xl font-bold text-text-primary">
              {analytics.risk_metrics.beta.toFixed(2)}
            </div>
            <div className="text-sm text-text-muted">Market sensitivity</div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Alpha</div>
            <div
              className={`text-xl font-bold ${
                analytics.risk_metrics.alpha >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {analytics.risk_metrics.alpha.toFixed(2)}
            </div>
            <div className="text-sm text-text-muted">Excess return</div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Correlation</div>
            <div className="text-xl font-bold text-text-primary">
              {analytics.risk_metrics.correlation.toFixed(2)}
            </div>
            <div className="text-sm text-text-muted">Market correlation</div>
          </div>
        </div>
      </Card>

      {/* Trade Statistics */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Trade Statistics</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-sm text-text-secondary mb-1">Winning Trades</div>
            <div className="text-xl font-bold text-green-600">
              {analytics.performance.winning_trades}
            </div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Losing Trades</div>
            <div className="text-xl font-bold text-red-600">
              {analytics.performance.losing_trades}
            </div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Win Rate</div>
            <div className="text-xl font-bold text-text-primary">
              {formatPercentage(
                (analytics.performance.winning_trades / 
                (analytics.performance.winning_trades + analytics.performance.losing_trades)) * 100
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Allocation Breakdown */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Allocation Breakdown</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-surface-tertiary">
                <th className="text-left py-2 text-sm font-medium text-text-secondary">Token</th>
                <th className="text-right py-2 text-sm font-medium text-text-secondary">Allocation</th>
                <th className="text-right py-2 text-sm font-medium text-text-secondary">Value</th>
              </tr>
            </thead>
            <tbody>
              {analytics.allocation.map((item, index) => (
                <tr key={index} className="border-b border-surface-tertiary">
                  <td className="py-3 text-text-primary font-medium">{item.token_symbol}</td>
                  <td className="py-3 text-right text-text-primary">
                    {formatPercentage(item.percentage)}
                  </td>
                  <td className="py-3 text-right text-text-primary">
                    {formatCurrency(item.value)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default PortfolioAnalytics;
