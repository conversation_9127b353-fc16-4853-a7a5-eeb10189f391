# ===========================================
# 📊 PROMETHEUS CONFIGURATION
# ===========================================
# Monitoring configuration for TokenTracker V2

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'tokentracker-v2'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets: []

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # TokenTracker V2 Application Metrics
  - job_name: 'tokentracker-v2-app'
    static_configs:
      - targets: ['app:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # MongoDB Exporter (if available)
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb:27017']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Redis Exporter (if available)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Node Exporter for system metrics (if available)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s

  # Docker container metrics (if available)
  - job_name: 'docker'
    static_configs:
      - targets: ['docker-exporter:9323']
    scrape_interval: 30s
    scrape_timeout: 10s

# Note: Storage and web configurations are handled via command-line arguments in docker-compose.yml
