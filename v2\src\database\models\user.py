"""
👤 User Database Model

Beanie ODM model for user management following DATABASE_PATTERNS.md
with proper indexing, validation, and security.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from beanie import Document, Indexed
from pydantic import Field, validator, EmailStr
from pymongo import IndexModel, ASCENDING, DESCENDING

from .base import BaseDocument
from ...shared.types import UserRole


class User(BaseDocument):
    """
    👤 User Model
    
    Stores user information, preferences, and settings
    following V2 architecture patterns.
    """
    
    # 🎯 Core User Data
    email: Indexed(EmailStr, unique=True) = Field(..., description="User email address")
    username: Indexed(str, unique=True) = Field(..., description="Unique username")
    full_name: Optional[str] = Field(None, description="User full name")
    
    # 🔐 Authentication
    password_hash: str = Field(..., description="Hashed password")
    role: UserRole = Field(default=UserRole.USER, description="User role (admin, user, etc.)")
    is_verified: bool = Field(default=False, description="Email verification status")
    verification_token: Optional[str] = Field(None, description="Email verification token")
    login_attempts: int = Field(default=0, description="Number of failed login attempts")
    locked_until: Optional[datetime] = Field(None, description="Account lock expiration time")
    last_login: Optional[datetime] = Field(None, description="Last successful login timestamp")
    
    # 🎛️ User Preferences
    timezone: str = Field(default="UTC", description="User timezone")
    language: str = Field(default="en", description="Preferred language")
    currency: str = Field(default="USD", description="Preferred currency")
    
    # 📱 Notification Settings
    notification_preferences: Dict[str, bool] = Field(
        default_factory=lambda: {
            "email_signals": True,
            "email_trades": True,
            "telegram_signals": False,
            "telegram_trades": False,
            "push_notifications": True
        },
        description="Notification preferences"
    )
    
    # 💼 Trading Settings
    default_portfolio_settings: Dict[str, Any] = Field(
        default_factory=lambda: {
            "initial_balance": 10000,
            "max_position_size": 1000,
            "stop_loss_percent": 0.05,
            "take_profit_percent": 0.15,
            "risk_tolerance": "moderate"
        },
        description="Default portfolio settings"
    )
    
    # 📊 User Statistics
    total_portfolios: int = Field(default=0, description="Total portfolios created")
    total_trades: int = Field(default=0, description="Total trades executed")
    total_pnl: float = Field(default=0.0, description="Total P&L across portfolios")
    
    # ⏰ Activity Tracking
    last_login_at: Optional[datetime] = Field(None, description="Last login timestamp")
    last_activity_at: Optional[datetime] = Field(None, description="Last activity timestamp")
    login_count: int = Field(default=0, description="Total login count")
    
    # 🔗 External Integrations
    telegram_user_id: Optional[str] = Field(None, description="Telegram user ID")
    discord_user_id: Optional[str] = Field(None, description="Discord user ID")
    api_keys: Dict[str, str] = Field(default_factory=dict, description="Encrypted API keys")
    
    @validator("username")
    def validate_username(cls, v):
        """Validate username format"""
        if not v or len(v) < 3:
            raise ValueError("Username must be at least 3 characters")
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Username can only contain letters, numbers, hyphens, and underscores")
        return v.lower()
    
    @validator("timezone")
    def validate_timezone(cls, v):
        """Validate timezone format"""
        # Basic validation - in production, use pytz for proper validation
        if not v:
            return "UTC"
        return v
    
    def update_activity(self) -> None:
        """Update last activity timestamp"""
        self.last_activity_at = datetime.utcnow()
    
    def record_login(self) -> None:
        """Record user login"""
        self.last_login_at = datetime.utcnow()
        self.login_count += 1
        self.update_activity()
    
    class Settings:
        name = "users"
        indexes = [
            # Unique indexes
            IndexModel([("email", ASCENDING)], unique=True),
            IndexModel([("username", ASCENDING)], unique=True),
            
            # Query indexes
            IndexModel([("is_active", ASCENDING)]),
            IndexModel([("is_verified", ASCENDING)]),
            IndexModel([("created_at", DESCENDING)]),
            IndexModel([("last_login_at", DESCENDING)]),
            
            # External integration indexes
            IndexModel([("telegram_user_id", ASCENDING)]),
            IndexModel([("discord_user_id", ASCENDING)]),
            
            # Performance indexes
            IndexModel([("total_pnl", DESCENDING)]),
            IndexModel([("total_trades", DESCENDING)])
        ]
