import React from 'react';
import { TrendingUp, TrendingDown, DollarSign, Activity } from 'lucide-react';
import { MarketOverview as MarketOverviewType } from '@/types';
import Card from '@/components/ui/Card';

interface MarketOverviewProps {
  data: MarketOverviewType;
}

/**
 * Market overview component showing market statistics and trending tokens
 */
const MarketOverview: React.FC<MarketOverviewProps> = ({ data }) => {
  const formatCurrency = (value: number, compact = false) => {
    if (compact && value >= 1e9) {
      return `$${(value / 1e9).toFixed(1)}B`;
    } else if (compact && value >= 1e6) {
      return `$${(value / 1e6).toFixed(1)}M`;
    } else if (compact && value >= 1e3) {
      return `$${(value / 1e3).toFixed(1)}K`;
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const getFearGreedColor = (index: number) => {
    if (index >= 75) return 'text-accent-green';
    if (index >= 50) return 'text-accent-yellow';
    if (index >= 25) return 'text-accent-red';
    return 'text-accent-red';
  };

  const getFearGreedLabel = (index: number) => {
    if (index >= 75) return 'Extreme Greed';
    if (index >= 55) return 'Greed';
    if (index >= 45) return 'Neutral';
    if (index >= 25) return 'Fear';
    return 'Extreme Fear';
  };

  return (
    <Card className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-text-primary mb-1">
          Market Overview
        </h2>
        <p className="text-text-muted text-sm">
          Current market conditions and trends
        </p>
      </div>

      {/* Market Statistics */}
      <div className="space-y-4 mb-6">
        <div className="flex items-center justify-between p-3 bg-bg-primary rounded-lg">
          <div className="flex items-center">
            <div className="p-2 bg-accent-blue/10 rounded-lg mr-3">
              <DollarSign className="w-4 h-4 text-accent-blue" />
            </div>
            <div>
              <p className="text-text-muted text-sm">Total Market Cap</p>
              <p className="text-text-primary font-semibold">
                {formatCurrency(data.total_market_cap, true)}
              </p>
            </div>
          </div>
          <div className={`flex items-center ${data.market_cap_change >= 0 ? 'text-accent-green' : 'text-accent-red'}`}>
            {data.market_cap_change >= 0 ? (
              <TrendingUp className="w-4 h-4 mr-1" />
            ) : (
              <TrendingDown className="w-4 h-4 mr-1" />
            )}
            <span className="text-sm font-medium">
              {formatPercentage(data.market_cap_change)}
            </span>
          </div>
        </div>

        <div className="flex items-center justify-between p-3 bg-bg-primary rounded-lg">
          <div className="flex items-center">
            <div className="p-2 bg-accent-yellow/10 rounded-lg mr-3">
              <Activity className="w-4 h-4 text-accent-yellow" />
            </div>
            <div>
              <p className="text-text-muted text-sm">Bitcoin Dominance</p>
              <p className="text-text-primary font-semibold">
                {data.bitcoin_dominance.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between p-3 bg-bg-primary rounded-lg">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg mr-3 ${getFearGreedColor(data.fear_greed_index).replace('text-', 'bg-').replace('accent-', 'accent-')}/10`}>
              <Activity className={`w-4 h-4 ${getFearGreedColor(data.fear_greed_index)}`} />
            </div>
            <div>
              <p className="text-text-muted text-sm">Fear & Greed Index</p>
              <p className="text-text-primary font-semibold">
                {data.fear_greed_index}
              </p>
            </div>
          </div>
          <span className={`text-sm font-medium ${getFearGreedColor(data.fear_greed_index)}`}>
            {getFearGreedLabel(data.fear_greed_index)}
          </span>
        </div>
      </div>

      {/* Trending Tokens */}
      <div>
        <h3 className="text-lg font-medium text-text-primary mb-4">
          Trending Tokens
        </h3>
        <div className="space-y-3">
          {data.trending_tokens.slice(0, 5).map((token) => (
            <div key={token.address} className="flex items-center justify-between p-3 bg-bg-primary rounded-lg hover:bg-bg-tertiary transition-colors">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-accent-blue to-accent-green rounded-full flex items-center justify-center mr-3">
                  <span className="text-white text-xs font-bold">
                    {token.symbol.charAt(0)}
                  </span>
                </div>
                <div>
                  <p className="text-text-primary font-medium text-sm">
                    {token.symbol}
                  </p>
                  <p className="text-text-muted text-xs">
                    {token.name}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-text-primary font-medium text-sm">
                  {formatCurrency(token.price)}
                </p>
                <div className={`flex items-center justify-end ${token.price_change_24h >= 0 ? 'text-accent-green' : 'text-accent-red'}`}>
                  {token.price_change_24h >= 0 ? (
                    <TrendingUp className="w-3 h-3 mr-1" />
                  ) : (
                    <TrendingDown className="w-3 h-3 mr-1" />
                  )}
                  <span className="text-xs font-medium">
                    {formatPercentage(token.price_change_24h)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

export default MarketOverview;
