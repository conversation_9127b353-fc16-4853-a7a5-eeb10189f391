import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

interface CreatePortfolioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (portfolioData: {
    name: string;
    description?: string;
    initial_balance: number;
    risk_level: 'low' | 'medium' | 'high';
  }) => void;
  isLoading: boolean;
}

const CreatePortfolioModal: React.FC<CreatePortfolioModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    initial_balance: 10000,
    risk_level: 'medium' as 'low' | 'medium' | 'high',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Portfolio name is required';
    }
    
    if (formData.initial_balance <= 0) {
      newErrors.initial_balance = 'Initial balance must be greater than 0';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setErrors({});
    onSubmit({
      name: formData.name.trim(),
      description: formData.description.trim() || undefined,
      initial_balance: formData.initial_balance,
      risk_level: formData.risk_level,
    });
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        name: '',
        description: '',
        initial_balance: 10000,
        risk_level: 'medium',
      });
      setErrors({});
      onClose();
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Create New Portfolio">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-text-primary mb-2">
            Portfolio Name *
          </label>
          <Input
            id="name"
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter portfolio name"
            className={errors.name ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-text-primary mb-2">
            Description
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Enter portfolio description (optional)"
            rows={3}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={isLoading}
          />
        </div>

        <div>
          <label htmlFor="initial_balance" className="block text-sm font-medium text-text-primary mb-2">
            Initial Balance (USD) *
          </label>
          <Input
            id="initial_balance"
            type="number"
            value={formData.initial_balance}
            onChange={(e) => handleInputChange('initial_balance', parseFloat(e.target.value) || 0)}
            placeholder="Enter initial balance"
            min="0"
            step="0.01"
            className={errors.initial_balance ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {errors.initial_balance && (
            <p className="mt-1 text-sm text-red-600">{errors.initial_balance}</p>
          )}
        </div>

        <div>
          <label htmlFor="risk_level" className="block text-sm font-medium text-text-primary mb-2">
            Risk Level *
          </label>
          <select
            id="risk_level"
            value={formData.risk_level}
            onChange={(e) => handleInputChange('risk_level', e.target.value)}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={isLoading}
          >
            <option value="low">Low Risk - Conservative approach with stable returns</option>
            <option value="medium">Medium Risk - Balanced approach with moderate returns</option>
            <option value="high">High Risk - Aggressive approach with high potential returns</option>
          </select>
        </div>

        <div className="bg-surface-tertiary p-4 rounded-lg">
          <h4 className="text-sm font-medium text-text-primary mb-2">Risk Level Guidelines</h4>
          <div className="space-y-2 text-sm text-text-secondary">
            <div className="flex items-start space-x-2">
              <span className="inline-block w-2 h-2 rounded-full bg-green-500 mt-2"></span>
              <div>
                <span className="font-medium text-green-600">Low Risk:</span> Focus on stable tokens, lower volatility, conservative position sizing
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="inline-block w-2 h-2 rounded-full bg-yellow-500 mt-2"></span>
              <div>
                <span className="font-medium text-yellow-600">Medium Risk:</span> Balanced mix of stable and growth tokens, moderate position sizing
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="inline-block w-2 h-2 rounded-full bg-red-500 mt-2"></span>
              <div>
                <span className="font-medium text-red-600">High Risk:</span> Focus on high-growth potential tokens, larger position sizes, higher volatility
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-primary hover:bg-primary-dark"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </div>
            ) : (
              'Create Portfolio'
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default CreatePortfolioModal;
