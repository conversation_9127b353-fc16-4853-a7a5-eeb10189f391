import React, { useState } from 'react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { Position } from '@/types';

interface PositionsListProps {
  positions: Position[];
  isLoading: boolean;
}

const PositionsList: React.FC<PositionsListProps> = ({ positions, isLoading }) => {
  const [sortBy, setSortBy] = useState<'symbol' | 'value' | 'pnl' | 'percentage'>('value');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterType, setFilterType] = useState<'all' | 'long' | 'short'>('all');

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const sortedAndFilteredPositions = React.useMemo(() => {
    let filtered = positions;
    
    if (filterType !== 'all') {
      filtered = positions.filter(position => position.position_type === filterType);
    }

    return filtered.sort((a, b) => {
      let aValue: number;
      let bValue: number;

      switch (sortBy) {
        case 'symbol':
          return sortOrder === 'asc' 
            ? a.token_symbol.localeCompare(b.token_symbol)
            : b.token_symbol.localeCompare(a.token_symbol);
        case 'value':
          aValue = a.market_value;
          bValue = b.market_value;
          break;
        case 'pnl':
          aValue = a.unrealized_pnl;
          bValue = b.unrealized_pnl;
          break;
        case 'percentage':
          aValue = a.unrealized_pnl_percentage;
          bValue = b.unrealized_pnl_percentage;
          break;
        default:
          return 0;
      }

      return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
    });
  }, [positions, sortBy, sortOrder, filterType]);

  const totalValue = positions.reduce((sum, position) => sum + position.market_value, 0);
  const totalPnL = positions.reduce((sum, position) => sum + position.unrealized_pnl, 0);
  const totalPnLPercentage = totalValue > 0 ? (totalPnL / (totalValue - totalPnL)) * 100 : 0;

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-surface-tertiary rounded"></div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (positions.length === 0) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold text-text-primary mb-4">No Positions</h3>
        <p className="text-text-muted mb-4">
          This portfolio doesn't have any active positions yet.
        </p>
        <Button className="bg-primary hover:bg-primary-dark">
          Start Trading
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Positions Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-sm text-text-secondary mb-1">Total Value</div>
            <div className="text-xl font-bold text-text-primary">
              {formatCurrency(totalValue)}
            </div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Unrealized P&L</div>
            <div
              className={`text-xl font-bold ${
                totalPnL >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {formatCurrency(totalPnL)}
            </div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Unrealized P&L %</div>
            <div
              className={`text-xl font-bold ${
                totalPnLPercentage >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {formatPercentage(totalPnLPercentage)}
            </div>
          </div>
        </div>
      </Card>

      {/* Filters and Sorting */}
      <Card className="p-4">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div>
              <label className="text-sm text-text-secondary mr-2">Filter:</label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as any)}
                className="px-3 py-1 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
              >
                <option value="all">All Positions</option>
                <option value="long">Long Positions</option>
                <option value="short">Short Positions</option>
              </select>
            </div>
            <div>
              <label className="text-sm text-text-secondary mr-2">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-1 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
              >
                <option value="value">Market Value</option>
                <option value="symbol">Symbol</option>
                <option value="pnl">P&L Amount</option>
                <option value="percentage">P&L Percentage</option>
              </select>
            </div>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="px-3 py-1 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm hover:bg-surface-tertiary transition-colors"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
          <div className="text-sm text-text-secondary">
            {sortedAndFilteredPositions.length} position{sortedAndFilteredPositions.length !== 1 ? 's' : ''}
          </div>
        </div>
      </Card>

      {/* Positions List */}
      <Card className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-surface-tertiary">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Token
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Avg Price
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Current Price
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Market Value
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Unrealized P&L
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Entry Date
                </th>
              </tr>
            </thead>
            <tbody className="bg-surface-primary divide-y divide-surface-tertiary">
              {sortedAndFilteredPositions.map((position) => (
                <tr key={position.id} className="hover:bg-surface-secondary transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-text-primary">
                        {position.token_symbol}
                      </div>
                      <div className="text-sm text-text-muted truncate max-w-32">
                        {position.token_name}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        position.position_type === 'long'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {position.position_type.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-primary">
                    {position.quantity.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-primary">
                    {formatCurrency(position.average_price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-primary">
                    {formatCurrency(position.current_price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-text-primary">
                    {formatCurrency(position.market_value)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                    <div
                      className={`font-medium ${
                        position.unrealized_pnl >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {formatCurrency(position.unrealized_pnl)}
                    </div>
                    <div
                      className={`text-xs ${
                        position.unrealized_pnl_percentage >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {formatPercentage(position.unrealized_pnl_percentage)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-muted">
                    {formatDate(position.entry_date)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default PositionsList;
