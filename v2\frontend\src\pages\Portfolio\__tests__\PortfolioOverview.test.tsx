import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { render } from '@/test/utils';
import {
  mockSuccessfulQuery,
  mockLoadingQuery,
  mockErrorQuery,
  resetAllMocks,
} from '@/test/mocks';
import {
  createMockPortfolio,
  createMockDashboardAnalytics,
} from '@/test/factories';
import PortfolioOverview from '../components/PortfolioOverview';

// Mock the hooks
jest.mock('@tanstack/react-query');

describe('PortfolioOverview', () => {
  beforeEach(() => {
    resetAllMocks();
  });

  describe('Loading State', () => {
    it('should display loading skeleton when data is loading', () => {
      mockLoadingQuery();

      render(<PortfolioOverview />);

      // Check for loading skeletons
      const skeletons = screen.getAllByTestId(/skeleton/i);
      expect(skeletons.length).toBeGreaterThan(0);
    });
  });

  describe('Success State', () => {
    it('should display portfolio overview when data loads successfully', async () => {
      const mockPortfolio = createMockPortfolio({
        total_value: 25000,
        total_pnl: 5000,
        total_pnl_percentage: 25,
        daily_pnl: 250,
        daily_pnl_percentage: 1.2,
      });

      const mockAnalytics = createMockDashboardAnalytics();

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        // Check for portfolio value display
        expect(screen.getByText('$25,000.00')).toBeInTheDocument();
        expect(screen.getByText('$5,000.00')).toBeInTheDocument();
        expect(screen.getByText('+25.00%')).toBeInTheDocument();
      });
    });

    it('should display performance metrics correctly', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        summary: {
          total_portfolios: 3,
          total_value: 50000,
          total_pnl: 10000,
          total_pnl_percentage: 25,
          daily_pnl: 500,
          daily_pnl_percentage: 1.5,
          active_signals: 12,
          executed_trades_today: 8,
        },
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByText('3')).toBeInTheDocument(); // total portfolios
        expect(screen.getByText('12')).toBeInTheDocument(); // active signals
        expect(screen.getByText('8')).toBeInTheDocument(); // executed trades
      });
    });

    it('should display positive PnL with green color', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        summary: {
          daily_pnl: 250,
          daily_pnl_percentage: 1.2,
        },
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        const pnlElement = screen.getByText('+$250.00');
        expect(pnlElement).toBeInTheDocument();
        expect(pnlElement).toHaveClass('text-green-600');
      });
    });

    it('should display negative PnL with red color', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        summary: {
          daily_pnl: -150,
          daily_pnl_percentage: -0.8,
        },
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        const pnlElement = screen.getByText('-$150.00');
        expect(pnlElement).toBeInTheDocument();
        expect(pnlElement).toHaveClass('text-red-600');
      });
    });
  });

  describe('Error State', () => {
    it('should display error message when data loading fails', async () => {
      const mockError = new Error('Failed to fetch portfolio data');
      mockErrorQuery(mockError);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByText(/error loading portfolio data/i)).toBeInTheDocument();
        expect(screen.getByText(/failed to fetch portfolio data/i)).toBeInTheDocument();
      });
    });

    it('should display retry button on error', async () => {
      const mockError = new Error('Network error');
      mockErrorQuery(mockError);

      render(<PortfolioOverview />);

      await waitFor(() => {
        const retryButton = screen.getByRole('button', { name: /try again/i });
        expect(retryButton).toBeInTheDocument();
      });
    });
  });

  describe('Chart Display', () => {
    it('should render performance chart when data is available', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        performance_chart: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
          datasets: [
            {
              label: 'Portfolio Value',
              data: [20000, 22000, 21000, 25000, 27000],
            },
          ],
        },
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByTestId('line-chart')).toBeInTheDocument();
      });
    });

    it('should not render chart when no data is available', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        performance_chart: null,
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.queryByTestId('line-chart')).not.toBeInTheDocument();
      });
    });
  });

  describe('Top Performers Section', () => {
    it('should display top performing assets', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        top_performers: [
          { symbol: 'SOL', pnl_percentage: 45.2 },
          { symbol: 'ETH', pnl_percentage: 32.1 },
          { symbol: 'BTC', pnl_percentage: 18.5 },
        ],
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByText('SOL')).toBeInTheDocument();
        expect(screen.getByText('ETH')).toBeInTheDocument();
        expect(screen.getByText('BTC')).toBeInTheDocument();
        expect(screen.getByText('+45.20%')).toBeInTheDocument();
        expect(screen.getByText('+32.10%')).toBeInTheDocument();
        expect(screen.getByText('+18.50%')).toBeInTheDocument();
      });
    });

    it('should handle empty top performers list', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        top_performers: [],
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByText(/no top performers/i)).toBeInTheDocument();
      });
    });
  });

  describe('Recent Trades Section', () => {
    it('should display recent trades', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        recent_trades: [
          {
            id: 'trade-1',
            token_symbol: 'SOL',
            trade_type: 'buy',
            quantity: 10,
            price: 100,
            executed_at: '2024-07-24T12:00:00Z',
          },
          {
            id: 'trade-2',
            token_symbol: 'ETH',
            trade_type: 'sell',
            quantity: 5,
            price: 2000,
            executed_at: '2024-07-24T11:00:00Z',
          },
        ],
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByText('SOL')).toBeInTheDocument();
        expect(screen.getByText('ETH')).toBeInTheDocument();
        expect(screen.getByText('Buy')).toBeInTheDocument();
        expect(screen.getByText('Sell')).toBeInTheDocument();
      });
    });

    it('should handle empty recent trades list', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        recent_trades: [],
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByText(/no recent trades/i)).toBeInTheDocument();
      });
    });
  });

  describe('Responsive Design', () => {
    it('should adapt layout for mobile screens', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const mockAnalytics = createMockDashboardAnalytics();
      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        const container = screen.getByTestId('portfolio-overview');
        expect(container).toHaveClass('space-y-6');
      });
    });
  });

  describe('Data Formatting', () => {
    it('should format currency values correctly', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        summary: {
          total_value: 1234567.89,
          total_pnl: 123456.78,
        },
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByText('$1,234,567.89')).toBeInTheDocument();
        expect(screen.getByText('$123,456.78')).toBeInTheDocument();
      });
    });

    it('should format percentage values correctly', async () => {
      const mockAnalytics = createMockDashboardAnalytics({
        summary: {
          total_pnl_percentage: 25.456,
          daily_pnl_percentage: -1.234,
        },
      });

      mockSuccessfulQuery(mockAnalytics);

      render(<PortfolioOverview />);

      await waitFor(() => {
        expect(screen.getByText('+25.46%')).toBeInTheDocument();
        expect(screen.getByText('-1.23%')).toBeInTheDocument();
      });
    });
  });
});
