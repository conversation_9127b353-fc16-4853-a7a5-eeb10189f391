import React, { useRef, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { TimeSeriesData } from '@/types';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface LineChartProps {
  data: TimeSeriesData[];
  title?: string;
  height?: number;
  showGrid?: boolean;
  showPoints?: boolean;
  fill?: boolean;
  color?: string;
  backgroundColor?: string;
  formatValue?: (value: number) => string;
  formatLabel?: (label: string) => string;
}

/**
 * Line chart component for time series data
 */
const LineChart: React.FC<LineChartProps> = ({
  data,
  title,
  height = 400,
  showGrid = true,
  showPoints = false,
  fill = false,
  color = '#3b82f6',
  backgroundColor = 'rgba(59, 130, 246, 0.1)',
  formatValue = (value) => value.toLocaleString(),
  formatLabel = (label) => label,
}) => {
  const chartRef = useRef<ChartJS<'line'>>(null);

  // Prepare chart data
  const chartData = {
    labels: data.map(item => formatLabel(item.timestamp)),
    datasets: [
      {
        label: title || 'Value',
        data: data.map(item => item.value),
        borderColor: color,
        backgroundColor: fill ? backgroundColor : 'transparent',
        borderWidth: 2,
        fill,
        pointRadius: showPoints ? 4 : 0,
        pointHoverRadius: 6,
        pointBackgroundColor: color,
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        tension: 0.4,
      },
    ],
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: !!title,
        labels: {
          color: '#cbd5e1',
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: '#1e293b',
        titleColor: '#f8fafc',
        bodyColor: '#cbd5e1',
        borderColor: '#475569',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: (context: any) => {
            return `${context.dataset.label}: ${formatValue(context.parsed.y)}`;
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: showGrid,
          color: '#334155',
        },
        ticks: {
          color: '#64748b',
          font: {
            size: 11,
          },
          maxTicksLimit: 8,
        },
      },
      y: {
        display: true,
        grid: {
          display: showGrid,
          color: '#334155',
        },
        ticks: {
          color: '#64748b',
          font: {
            size: 11,
          },
          callback: (value: any) => formatValue(value),
        },
      },
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
    elements: {
      point: {
        hoverBackgroundColor: color,
      },
    },
  };

  // Update chart when data changes
  useEffect(() => {
    const chart = chartRef.current;
    if (chart) {
      chart.update('none');
    }
  }, [data]);

  return (
    <div className="w-full" style={{ height }}>
      <Line ref={chartRef} data={chartData} options={options} />
    </div>
  );
};

export default LineChart;
