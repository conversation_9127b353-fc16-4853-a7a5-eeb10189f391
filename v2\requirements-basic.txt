# 🚀 TokenTracker V2 - Basic Dependencies for Quick Start
# Only essential dependencies to get the application running

# 🌐 Web Framework & API
fastapi==0.115.0
uvicorn==0.32.0
pydantic==2.10.0

# 🗄️ Database & Caching
pymongo==4.9.0
motor==3.6.0
redis==5.2.0

# 🌐 HTTP & API Clients
httpx==0.28.0
requests==2.31.0

# 🔐 Security & Authentication
PyJWT==2.10.0
python-multipart==0.0.12

# 📝 Logging & Monitoring
structlog==24.4.0

# 🔧 Development & Utilities
python-dotenv==1.0.1

# 🔄 Async & Concurrency
aiofiles==24.1.0
