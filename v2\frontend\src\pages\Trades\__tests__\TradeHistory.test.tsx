import React from 'react';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '@/test/utils';
import {
  mockSuccessfulQuery,
  mockLoadingQuery,
  mockErrorQuery,
  mockSuccessfulMutation,
  resetAllMocks,
} from '@/test/mocks';
import {
  createMockTrades,
  createMockPaginatedResponse,
} from '@/test/factories';
import TradeHistory from '../components/TradeHistory';

jest.mock('@tanstack/react-query');

describe('TradeHistory', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    resetAllMocks();
  });

  describe('Loading State', () => {
    it('should display loading skeleton when trades are loading', () => {
      mockLoadingQuery();

      render(<TradeHistory />);

      const skeletons = screen.getAllByTestId(/skeleton/i);
      expect(skeletons.length).toBeGreaterThan(0);
    });
  });

  describe('Success State', () => {
    it('should display trades table when data loads successfully', async () => {
      const mockTrades = createMockTrades(3);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        // Check table headers
        expect(screen.getByText('Date')).toBeInTheDocument();
        expect(screen.getByText('Asset')).toBeInTheDocument();
        expect(screen.getByText('Type')).toBeInTheDocument();
        expect(screen.getByText('Quantity')).toBeInTheDocument();
        expect(screen.getByText('Price')).toBeInTheDocument();
        expect(screen.getByText('Total')).toBeInTheDocument();
        expect(screen.getByText('P&L')).toBeInTheDocument();
        expect(screen.getByText('Status')).toBeInTheDocument();

        // Check trade data
        mockTrades.forEach(trade => {
          expect(screen.getByText(trade.token_symbol)).toBeInTheDocument();
          expect(screen.getByText(trade.trade_type.toUpperCase())).toBeInTheDocument();
        });
      });
    });

    it('should display trade details correctly', async () => {
      const mockTrades = createMockTrades(1);
      const trade = mockTrades[0];
      const mockResponse = createMockPaginatedResponse(mockTrades);
      
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText(trade.token_symbol)).toBeInTheDocument();
        expect(screen.getByText(trade.trade_type.toUpperCase())).toBeInTheDocument();
        expect(screen.getByText(trade.quantity.toString())).toBeInTheDocument();
        expect(screen.getByText(`$${trade.price.toFixed(2)}`)).toBeInTheDocument();
        expect(screen.getByText(`$${trade.total_value.toLocaleString()}`)).toBeInTheDocument();
        expect(screen.getByText(trade.status.toUpperCase())).toBeInTheDocument();
      });
    });

    it('should display buy trades with green styling', async () => {
      const mockTrades = createMockTrades(1);
      mockTrades[0].trade_type = 'buy';
      const mockResponse = createMockPaginatedResponse(mockTrades);
      
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        const buyTrade = screen.getByText('BUY');
        expect(buyTrade).toBeInTheDocument();
        expect(buyTrade.closest('span')).toHaveClass('bg-green-100', 'text-green-800');
      });
    });

    it('should display sell trades with red styling', async () => {
      const mockTrades = createMockTrades(1);
      mockTrades[0].trade_type = 'sell';
      const mockResponse = createMockPaginatedResponse(mockTrades);
      
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        const sellTrade = screen.getByText('SELL');
        expect(sellTrade).toBeInTheDocument();
        expect(sellTrade.closest('span')).toHaveClass('bg-red-100', 'text-red-800');
      });
    });

    it('should display positive P&L with green color', async () => {
      const mockTrades = createMockTrades(1);
      mockTrades[0].pnl = 100;
      mockTrades[0].pnl_percentage = 10;
      const mockResponse = createMockPaginatedResponse(mockTrades);
      
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        const pnlElement = screen.getByText('+$100.00');
        expect(pnlElement).toBeInTheDocument();
        expect(pnlElement.closest('div')).toHaveClass('text-green-600');
      });
    });

    it('should display negative P&L with red color', async () => {
      const mockTrades = createMockTrades(1);
      mockTrades[0].pnl = -50;
      mockTrades[0].pnl_percentage = -5;
      const mockResponse = createMockPaginatedResponse(mockTrades);
      
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        const pnlElement = screen.getByText('-$50.00');
        expect(pnlElement).toBeInTheDocument();
        expect(pnlElement.closest('div')).toHaveClass('text-red-600');
      });
    });
  });

  describe('Error State', () => {
    it('should display error message when trades loading fails', async () => {
      const mockError = new Error('Failed to fetch trades');
      mockErrorQuery(mockError);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText(/error loading trades/i)).toBeInTheDocument();
        expect(screen.getByText(/failed to fetch trades/i)).toBeInTheDocument();
      });
    });

    it('should display retry button on error', async () => {
      const mockError = new Error('Network error');
      mockErrorQuery(mockError);

      render(<TradeHistory />);

      await waitFor(() => {
        const retryButton = screen.getByRole('button', { name: /try again/i });
        expect(retryButton).toBeInTheDocument();
      });
    });
  });

  describe('Empty State', () => {
    it('should display empty state when no trades exist', async () => {
      const mockResponse = createMockPaginatedResponse([]);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText(/no trades found/i)).toBeInTheDocument();
        expect(screen.getByText(/start trading to see your history here/i)).toBeInTheDocument();
      });
    });

    it('should show create trade button in empty state', async () => {
      const mockResponse = createMockPaginatedResponse([]);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        const createButton = screen.getByRole('button', { name: /create trade/i });
        expect(createButton).toBeInTheDocument();
      });
    });
  });

  describe('Filtering and Sorting', () => {
    it('should filter trades by search term', async () => {
      const mockTrades = [
        { ...createMockTrades(1)[0], token_symbol: 'SOL', token_name: 'Solana' },
        { ...createMockTrades(1)[0], id: 'trade-2', token_symbol: 'ETH', token_name: 'Ethereum' },
        { ...createMockTrades(1)[0], id: 'trade-3', token_symbol: 'BTC', token_name: 'Bitcoin' },
      ];
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText('SOL')).toBeInTheDocument();
        expect(screen.getByText('ETH')).toBeInTheDocument();
        expect(screen.getByText('BTC')).toBeInTheDocument();
      });

      // Search for ETH
      const searchInput = screen.getByPlaceholderText(/search trades/i);
      await user.type(searchInput, 'ETH');

      await waitFor(() => {
        expect(screen.getByText('ETH')).toBeInTheDocument();
        expect(screen.queryByText('SOL')).not.toBeInTheDocument();
        expect(screen.queryByText('BTC')).not.toBeInTheDocument();
      });
    });

    it('should filter trades by type', async () => {
      const mockTrades = [
        { ...createMockTrades(1)[0], trade_type: 'buy' as const },
        { ...createMockTrades(1)[0], id: 'trade-2', trade_type: 'sell' as const },
      ];
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText('BUY')).toBeInTheDocument();
        expect(screen.getByText('SELL')).toBeInTheDocument();
      });

      // Filter by buy trades
      const buyFilter = screen.getByRole('button', { name: /buy/i });
      await user.click(buyFilter);

      await waitFor(() => {
        expect(screen.getByText('BUY')).toBeInTheDocument();
        expect(screen.queryByText('SELL')).not.toBeInTheDocument();
      });
    });

    it('should filter trades by status', async () => {
      const mockTrades = [
        { ...createMockTrades(1)[0], status: 'executed' as const },
        { ...createMockTrades(1)[0], id: 'trade-2', status: 'pending' as const },
        { ...createMockTrades(1)[0], id: 'trade-3', status: 'cancelled' as const },
      ];
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText('EXECUTED')).toBeInTheDocument();
        expect(screen.getByText('PENDING')).toBeInTheDocument();
        expect(screen.getByText('CANCELLED')).toBeInTheDocument();
      });

      // Filter by executed trades
      const executedFilter = screen.getByRole('button', { name: /executed/i });
      await user.click(executedFilter);

      await waitFor(() => {
        expect(screen.getByText('EXECUTED')).toBeInTheDocument();
        expect(screen.queryByText('PENDING')).not.toBeInTheDocument();
        expect(screen.queryByText('CANCELLED')).not.toBeInTheDocument();
      });
    });

    it('should sort trades by different columns', async () => {
      const mockTrades = createMockTrades(3);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText('Date')).toBeInTheDocument();
      });

      // Click on date column to sort
      const dateHeader = screen.getByText('Date');
      await user.click(dateHeader);

      // Verify sorting indicator appears
      expect(dateHeader.closest('th')).toContainHTML('mock-icon');
    });
  });

  describe('Date Range Filtering', () => {
    it('should filter trades by date range', async () => {
      const mockTrades = createMockTrades(3);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText('Date Range')).toBeInTheDocument();
      });

      // Set date range
      const fromDate = screen.getByLabelText(/from date/i);
      const toDate = screen.getByLabelText(/to date/i);

      await user.type(fromDate, '2024-07-01');
      await user.type(toDate, '2024-07-31');

      const applyButton = screen.getByRole('button', { name: /apply/i });
      await user.click(applyButton);

      // Should trigger filtered query
    });
  });

  describe('Trade Actions', () => {
    it('should show action buttons for each trade', async () => {
      const mockTrades = createMockTrades(1);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /view details/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /delete/i })).toBeInTheDocument();
      });
    });

    it('should handle trade deletion', async () => {
      const mockTrades = createMockTrades(1);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);
      
      const mockMutation = mockSuccessfulMutation();
      const deleteMock = jest.fn();
      mockMutation.mutate = deleteMock;

      // Mock window.confirm
      window.confirm = jest.fn().mockReturnValue(true);

      render(<TradeHistory />);

      await waitFor(() => {
        const deleteButton = screen.getByRole('button', { name: /delete/i });
        expect(deleteButton).toBeInTheDocument();
      });

      const deleteButton = screen.getByRole('button', { name: /delete/i });
      await user.click(deleteButton);

      expect(window.confirm).toHaveBeenCalledWith(
        expect.stringContaining('Are you sure you want to delete this trade?')
      );
      expect(deleteMock).toHaveBeenCalledWith(mockTrades[0].id);
    });

    it('should not delete trade if user cancels confirmation', async () => {
      const mockTrades = createMockTrades(1);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);
      
      const mockMutation = mockSuccessfulMutation();
      const deleteMock = jest.fn();
      mockMutation.mutate = deleteMock;

      // Mock window.confirm to return false
      window.confirm = jest.fn().mockReturnValue(false);

      render(<TradeHistory />);

      await waitFor(() => {
        const deleteButton = screen.getByRole('button', { name: /delete/i });
        expect(deleteButton).toBeInTheDocument();
      });

      const deleteButton = screen.getByRole('button', { name: /delete/i });
      await user.click(deleteButton);

      expect(window.confirm).toHaveBeenCalled();
      expect(deleteMock).not.toHaveBeenCalled();
    });
  });

  describe('Export Functionality', () => {
    it('should show export button', async () => {
      const mockTrades = createMockTrades(3);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /export/i })).toBeInTheDocument();
      });
    });

    it('should handle export action', async () => {
      const mockTrades = createMockTrades(3);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);
      
      const mockMutation = mockSuccessfulMutation();
      const exportMock = jest.fn();
      mockMutation.mutate = exportMock;

      render(<TradeHistory />);

      await waitFor(() => {
        const exportButton = screen.getByRole('button', { name: /export/i });
        expect(exportButton).toBeInTheDocument();
      });

      const exportButton = screen.getByRole('button', { name: /export/i });
      await user.click(exportButton);

      expect(exportMock).toHaveBeenCalled();
    });
  });

  describe('Pagination', () => {
    it('should display pagination when there are multiple pages', async () => {
      const mockTrades = createMockTrades(25); // More than one page
      const mockResponse = createMockPaginatedResponse(mockTrades.slice(0, 20), {
        total: 25,
        page: 1,
        page_size: 20,
        total_pages: 2,
        has_next: true,
      });
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        expect(screen.getByText('1')).toBeInTheDocument(); // Current page
        expect(screen.getByText('2')).toBeInTheDocument(); // Next page
        expect(screen.getByRole('button', { name: /next/i })).toBeInTheDocument();
      });
    });

    it('should handle page navigation', async () => {
      const mockTrades = createMockTrades(25);
      const mockResponse = createMockPaginatedResponse(mockTrades.slice(0, 20), {
        total: 25,
        page: 1,
        page_size: 20,
        total_pages: 2,
        has_next: true,
      });
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        const nextButton = screen.getByRole('button', { name: /next/i });
        expect(nextButton).toBeInTheDocument();
      });

      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);

      // Should trigger a new query for page 2
    });
  });

  describe('Responsive Design', () => {
    it('should adapt table for mobile screens', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const mockTrades = createMockTrades(3);
      const mockResponse = createMockPaginatedResponse(mockTrades);
      mockSuccessfulQuery(mockResponse);

      render(<TradeHistory />);

      await waitFor(() => {
        // On mobile, table should be scrollable or show cards
        const container = screen.getByTestId('trades-container');
        expect(container).toBeInTheDocument();
      });
    });
  });
});
