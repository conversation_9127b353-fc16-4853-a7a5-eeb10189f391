import React from 'react';
import { clsx } from 'clsx';
import { Loader2 } from 'lucide-react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  children: React.ReactNode;
}

/**
 * Reusable button component with multiple variants and sizes
 */
const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  leftIcon,
  rightIcon,
  children,
  className,
  disabled,
  ...props
}) => {
  const baseClasses = [
    'inline-flex items-center justify-center font-medium rounded-lg',
    'transition-all duration-200 ease-in-out',
    'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-bg-primary',
    'disabled:opacity-50 disabled:cursor-not-allowed',
  ];

  const variantClasses = {
    primary: [
      'bg-accent-blue text-white',
      'hover:bg-accent-blue/90 active:bg-accent-blue/80',
      'focus:ring-accent-blue',
    ],
    secondary: [
      'bg-bg-secondary text-text-primary border border-border',
      'hover:bg-bg-tertiary active:bg-bg-tertiary/80',
      'focus:ring-primary-500',
    ],
    outline: [
      'bg-transparent text-text-primary border border-border',
      'hover:bg-bg-secondary active:bg-bg-tertiary',
      'focus:ring-primary-500',
    ],
    ghost: [
      'bg-transparent text-text-secondary',
      'hover:bg-bg-secondary hover:text-text-primary',
      'active:bg-bg-tertiary',
      'focus:ring-primary-500',
    ],
    danger: [
      'bg-accent-red text-white',
      'hover:bg-accent-red/90 active:bg-accent-red/80',
      'focus:ring-accent-red',
    ],
  };

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm gap-1.5',
    md: 'px-4 py-2 text-sm gap-2',
    lg: 'px-6 py-3 text-base gap-2',
  };

  const classes = clsx(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    className
  );

  return (
    <button
      className={classes}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? (
        <Loader2 className="w-4 h-4 animate-spin" />
      ) : (
        leftIcon && <span className="flex-shrink-0">{leftIcon}</span>
      )}
      
      <span>{children}</span>
      
      {!loading && rightIcon && (
        <span className="flex-shrink-0">{rightIcon}</span>
      )}
    </button>
  );
};

export default Button;
