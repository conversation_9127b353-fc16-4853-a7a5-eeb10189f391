import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';

interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (params: {
    format: 'csv' | 'json' | 'pdf';
    timeframe?: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL';
    portfolio_id?: string;
  }) => void;
}

const ExportModal: React.FC<ExportModalProps> = ({
  isOpen,
  onClose,
  onExport,
}) => {
  const [exportParams, setExportParams] = useState({
    format: 'csv' as 'csv' | 'json' | 'pdf',
    timeframe: '1M' as '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL',
    portfolio_id: '',
  });

  const handleExport = () => {
    onExport({
      format: exportParams.format,
      timeframe: exportParams.timeframe,
      ...(exportParams.portfolio_id && { portfolio_id: exportParams.portfolio_id }),
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Export Trade Data">
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            Export Format
          </label>
          <select
            value={exportParams.format}
            onChange={(e) => setExportParams(prev => ({ ...prev, format: e.target.value as any }))}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
          >
            <option value="csv">CSV - Spreadsheet format</option>
            <option value="json">JSON - Data format</option>
            <option value="pdf">PDF - Report format</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            Time Range
          </label>
          <select
            value={exportParams.timeframe}
            onChange={(e) => setExportParams(prev => ({ ...prev, timeframe: e.target.value as any }))}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
          >
            <option value="1D">Last 24 Hours</option>
            <option value="1W">Last Week</option>
            <option value="1M">Last Month</option>
            <option value="3M">Last 3 Months</option>
            <option value="6M">Last 6 Months</option>
            <option value="1Y">Last Year</option>
            <option value="ALL">All Time</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-text-primary mb-2">
            Portfolio ID (Optional)
          </label>
          <input
            type="text"
            value={exportParams.portfolio_id}
            onChange={(e) => setExportParams(prev => ({ ...prev, portfolio_id: e.target.value }))}
            placeholder="Leave empty for all portfolios"
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
          />
        </div>

        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleExport} className="bg-primary hover:bg-primary-dark">
            Export Data
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ExportModal;
