import React from 'react';
import Card from '@/components/ui/Card';
import { Bar, Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface SignalPerformanceProps {
  performanceData: {
    total_signals: number;
    successful_signals: number;
    success_rate: number;
    average_return: number;
    best_signal: {
      signal_id: string;
      return_percentage: number;
      token_symbol: string;
    };
    worst_signal: {
      signal_id: string;
      return_percentage: number;
      token_symbol: string;
    };
    performance_by_type: {
      signal_type: 'buy' | 'sell' | 'hold';
      count: number;
      success_rate: number;
      average_return: number;
    }[];
  } | null;
  isLoading: boolean;
}

const SignalPerformance: React.FC<SignalPerformanceProps> = ({
  performanceData,
  isLoading,
}) => {
  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[1, 2].map((i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
                <div className="h-64 bg-surface-tertiary rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!performanceData) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold text-text-primary mb-4">No Performance Data</h3>
        <p className="text-text-muted">
          Performance data is not available yet. Generate some signals to see performance metrics.
        </p>
      </Card>
    );
  }

  // Performance by type chart data
  const performanceByTypeData = {
    labels: performanceData.performance_by_type.map(item => item.signal_type.toUpperCase()),
    datasets: [
      {
        label: 'Success Rate (%)',
        data: performanceData.performance_by_type.map(item => item.success_rate * 100),
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 1,
      },
      {
        label: 'Average Return (%)',
        data: performanceData.performance_by_type.map(item => item.average_return),
        backgroundColor: 'rgba(16, 185, 129, 0.8)',
        borderColor: 'rgba(16, 185, 129, 1)',
        borderWidth: 1,
      },
    ],
  };

  const performanceByTypeOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Performance by Signal Type',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  // Signal distribution chart
  const signalDistributionData = {
    labels: performanceData.performance_by_type.map(item => item.signal_type.toUpperCase()),
    datasets: [
      {
        data: performanceData.performance_by_type.map(item => item.count),
        backgroundColor: [
          '#10B981', // Green for buy
          '#EF4444', // Red for sell
          '#F59E0B', // Yellow for hold
        ],
        borderWidth: 2,
        borderColor: '#1F2937',
      },
    ],
  };

  const signalDistributionOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Signal Distribution',
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed || 0;
            const total = performanceData.total_signals;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value} (${percentage}%)`;
          },
        },
      },
    },
  };

  // Mock monthly performance data
  const monthlyPerformanceData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Monthly Return (%)',
        data: [2.5, 1.8, 4.2, -1.1, 3.7, 2.9],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
      {
        label: 'Cumulative Return (%)',
        data: [2.5, 4.3, 8.5, 7.4, 11.1, 14.0],
        borderColor: 'rgb(16, 185, 129)',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        tension: 0.4,
      },
    ],
  };

  const monthlyPerformanceOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Monthly Performance Trend',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Total Signals</div>
          <div className="text-2xl font-bold text-text-primary">
            {performanceData.total_signals}
          </div>
          <div className="text-sm text-text-muted">
            {performanceData.successful_signals} successful
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Success Rate</div>
          <div className="text-2xl font-bold text-green-600">
            {(performanceData.success_rate * 100).toFixed(1)}%
          </div>
          <div className="text-sm text-text-muted">Signal accuracy</div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Average Return</div>
          <div
            className={`text-2xl font-bold ${
              performanceData.average_return >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatPercentage(performanceData.average_return)}
          </div>
          <div className="text-sm text-text-muted">Per signal</div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Best Signal</div>
          <div className="text-2xl font-bold text-green-600">
            {formatPercentage(performanceData.best_signal.return_percentage)}
          </div>
          <div className="text-sm text-text-muted">
            {performanceData.best_signal.token_symbol}
          </div>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance by Type */}
        <Card className="p-6">
          <div className="h-64">
            <Bar data={performanceByTypeData} options={performanceByTypeOptions} />
          </div>
        </Card>

        {/* Signal Distribution */}
        <Card className="p-6">
          <div className="h-64">
            <Doughnut data={signalDistributionData} options={signalDistributionOptions} />
          </div>
        </Card>
      </div>

      {/* Monthly Performance Trend */}
      <Card className="p-6">
        <div className="h-64">
          <Line data={monthlyPerformanceData} options={monthlyPerformanceOptions} />
        </div>
      </Card>

      {/* Best and Worst Signals */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Best Signal</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Token:</span>
              <span className="font-medium text-text-primary">
                {performanceData.best_signal.token_symbol}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Return:</span>
              <span className="font-bold text-green-600">
                {formatPercentage(performanceData.best_signal.return_percentage)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Signal ID:</span>
              <span className="font-mono text-sm text-text-muted">
                {performanceData.best_signal.signal_id.slice(0, 8)}...
              </span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Worst Signal</h3>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Token:</span>
              <span className="font-medium text-text-primary">
                {performanceData.worst_signal.token_symbol}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Return:</span>
              <span className="font-bold text-red-600">
                {formatPercentage(performanceData.worst_signal.return_percentage)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-text-secondary">Signal ID:</span>
              <span className="font-mono text-sm text-text-muted">
                {performanceData.worst_signal.signal_id.slice(0, 8)}...
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Performance by Type Table */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Performance Breakdown</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-surface-tertiary">
                <th className="text-left py-2 text-sm font-medium text-text-secondary">Signal Type</th>
                <th className="text-right py-2 text-sm font-medium text-text-secondary">Count</th>
                <th className="text-right py-2 text-sm font-medium text-text-secondary">Success Rate</th>
                <th className="text-right py-2 text-sm font-medium text-text-secondary">Avg Return</th>
              </tr>
            </thead>
            <tbody>
              {performanceData.performance_by_type.map((item, index) => (
                <tr key={index} className="border-b border-surface-tertiary">
                  <td className="py-3 text-text-primary font-medium">
                    {item.signal_type.toUpperCase()}
                  </td>
                  <td className="py-3 text-right text-text-primary">{item.count}</td>
                  <td className="py-3 text-right text-text-primary">
                    {(item.success_rate * 100).toFixed(1)}%
                  </td>
                  <td
                    className={`py-3 text-right font-medium ${
                      item.average_return >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {formatPercentage(item.average_return)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default SignalPerformance;
