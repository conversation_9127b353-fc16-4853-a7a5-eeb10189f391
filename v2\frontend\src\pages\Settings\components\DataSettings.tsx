import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import Button from '@/components/ui/Button';
import { Database, Download, Trash2, Shield, AlertTriangle } from 'lucide-react';

const DataSettings: React.FC = () => {
  const [exportFormat, setExportFormat] = useState('json');
  const [deleteConfirmation, setDeleteConfirmation] = useState('');

  // Export data mutation
  const exportDataMutation = useMutation({
    mutationFn: async (format: string) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      return { download_url: `https://example.com/export.${format}` };
    },
    onSuccess: (data) => {
      // In a real app, this would trigger a download
      window.open(data.download_url, '_blank');
    },
  });

  // Delete account mutation
  const deleteAccountMutation = useMutation({
    mutationFn: async () => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      return { success: true };
    },
    onSuccess: () => {
      alert('Account deletion initiated. You will receive an email confirmation.');
    },
  });

  const handleExportData = () => {
    exportDataMutation.mutate(exportFormat);
  };

  const handleDeleteAccount = () => {
    if (deleteConfirmation !== 'DELETE') {
      alert('Please type "DELETE" to confirm account deletion');
      return;
    }
    
    if (window.confirm('Are you absolutely sure? This action cannot be undone.')) {
      deleteAccountMutation.mutate();
    }
  };

  return (
    <div className="space-y-8">
      {/* Data Export */}
      <div>
        <div className="flex items-center mb-4">
          <Download className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Export Data</h4>
        </div>
        <div className="p-6 bg-surface-secondary rounded-lg">
          <p className="text-text-secondary mb-4">
            Download a copy of all your data including portfolios, trades, and settings.
          </p>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Export Format
              </label>
              <select
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value)}
                className="w-full max-w-xs px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-primary text-text-primary"
              >
                <option value="json">JSON - Machine readable</option>
                <option value="csv">CSV - Spreadsheet format</option>
                <option value="pdf">PDF - Human readable report</option>
              </select>
            </div>
            <Button
              onClick={handleExportData}
              disabled={exportDataMutation.isPending}
              className="bg-primary hover:bg-primary-dark"
            >
              {exportDataMutation.isPending ? 'Preparing Export...' : 'Export Data'}
            </Button>
          </div>
        </div>
      </div>

      {/* Data Backup */}
      <div>
        <div className="flex items-center mb-4">
          <Database className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Data Backup</h4>
        </div>
        <div className="p-6 bg-surface-secondary rounded-lg">
          <p className="text-text-secondary mb-4">
            Your data is automatically backed up daily. You can also create manual backups.
          </p>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 bg-surface-tertiary rounded-lg">
                <h5 className="font-medium text-text-primary mb-2">Last Backup</h5>
                <p className="text-sm text-text-secondary">July 24, 2024 at 3:00 AM UTC</p>
                <p className="text-xs text-text-muted mt-1">Automatic backup</p>
              </div>
              <div className="p-4 bg-surface-tertiary rounded-lg">
                <h5 className="font-medium text-text-primary mb-2">Backup Frequency</h5>
                <p className="text-sm text-text-secondary">Daily at 3:00 AM UTC</p>
                <p className="text-xs text-text-muted mt-1">Cannot be changed</p>
              </div>
            </div>
            <Button variant="outline">
              Create Manual Backup
            </Button>
          </div>
        </div>
      </div>

      {/* Privacy Settings */}
      <div>
        <div className="flex items-center mb-4">
          <Shield className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Privacy Settings</h4>
        </div>
        <div className="space-y-4">
          <div className="p-4 bg-surface-secondary rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div>
                <p className="font-medium text-text-primary">Analytics & Usage Data</p>
                <p className="text-sm text-text-secondary">
                  Help improve the platform by sharing anonymous usage data
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  defaultChecked={true}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          </div>

          <div className="p-4 bg-surface-secondary rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div>
                <p className="font-medium text-text-primary">Marketing Communications</p>
                <p className="text-sm text-text-secondary">
                  Receive emails about new features and platform updates
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  defaultChecked={false}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          </div>

          <div className="p-4 bg-surface-secondary rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div>
                <p className="font-medium text-text-primary">Data Processing</p>
                <p className="text-sm text-text-secondary">
                  Allow processing of trading data for personalized insights
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  defaultChecked={true}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Data Retention */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Data Retention</h4>
        <div className="p-6 bg-surface-secondary rounded-lg">
          <p className="text-text-secondary mb-4">
            Configure how long different types of data are retained in your account.
          </p>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Trade History
                </label>
                <select className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-primary text-text-primary">
                  <option value="forever">Keep Forever</option>
                  <option value="5years">5 Years</option>
                  <option value="3years">3 Years</option>
                  <option value="1year">1 Year</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Signal History
                </label>
                <select className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-primary text-text-primary">
                  <option value="forever">Keep Forever</option>
                  <option value="2years">2 Years</option>
                  <option value="1year">1 Year</option>
                  <option value="6months">6 Months</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Danger Zone */}
      <div>
        <div className="flex items-center mb-4">
          <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
          <h4 className="text-lg font-medium text-red-600">Danger Zone</h4>
        </div>
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
          <h5 className="font-medium text-red-800 mb-2">Delete Account</h5>
          <p className="text-sm text-red-700 mb-4">
            Permanently delete your account and all associated data. This action cannot be undone.
          </p>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-red-800 mb-2">
                Type "DELETE" to confirm
              </label>
              <input
                type="text"
                value={deleteConfirmation}
                onChange={(e) => setDeleteConfirmation(e.target.value)}
                placeholder="DELETE"
                className="w-full max-w-xs px-3 py-2 border border-red-300 rounded-lg bg-white text-red-900"
              />
            </div>
            <Button
              onClick={handleDeleteAccount}
              disabled={deleteConfirmation !== 'DELETE' || deleteAccountMutation.isPending}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {deleteAccountMutation.isPending ? 'Deleting Account...' : 'Delete Account'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataSettings;
