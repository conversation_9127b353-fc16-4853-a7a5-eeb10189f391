import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { TrendingUp, Shield, DollarSign, Clock, AlertTriangle, Target } from 'lucide-react';

const TradingSettings: React.FC = () => {
  const queryClient = useQueryClient();

  // Fetch trading preferences
  const { data: preferences, isLoading } = useQuery({
    queryKey: ['trading-preferences'],
    queryFn: async () => {
      // Mock API call
      return {
        risk_management: {
          max_position_size_percent: 10,
          max_daily_loss_percent: 5,
          max_portfolio_risk_percent: 20,
          stop_loss_percent: 5,
          take_profit_percent: 15,
        },
        execution_settings: {
          slippage_tolerance_percent: 1,
          auto_execute_signals: false,
          require_confirmation: true,
          max_execution_delay_seconds: 30,
        },
        signal_preferences: {
          min_confidence_threshold: 70,
          preferred_timeframes: ['1H', '4H', '1D'],
          exclude_low_volume_tokens: true,
          min_market_cap: 1000000,
        },
        portfolio_settings: {
          auto_rebalance: false,
          rebalance_threshold_percent: 5,
          preferred_base_currency: 'USD',
          compound_profits: true,
        },
      };
    },
  });

  const [localPreferences, setLocalPreferences] = useState<any>(null);

  React.useEffect(() => {
    if (preferences && !localPreferences) {
      setLocalPreferences(preferences);
    }
  }, [preferences, localPreferences]);

  // Update preferences mutation
  const updatePreferencesMutation = useMutation({
    mutationFn: async (data: any) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trading-preferences'] });
    },
  });

  const handleInputChange = (section: string, field: string, value: any) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleToggle = (section: string, field: string) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: !prev[section][field],
      },
    }));
  };

  const handleArrayToggle = (section: string, field: string, value: string) => {
    setLocalPreferences((prev: any) => {
      const currentArray = prev[section][field];
      const newArray = currentArray.includes(value)
        ? currentArray.filter((item: string) => item !== value)
        : [...currentArray, value];
      
      return {
        ...prev,
        [section]: {
          ...prev[section],
          [field]: newArray,
        },
      };
    });
  };

  const handleSave = () => {
    updatePreferencesMutation.mutate(localPreferences);
  };

  if (isLoading || !localPreferences) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((j) => (
                <div key={j} className="h-12 bg-surface-tertiary rounded"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  const timeframes = [
    { value: '1H', label: '1 Hour' },
    { value: '4H', label: '4 Hours' },
    { value: '1D', label: '1 Day' },
    { value: '1W', label: '1 Week' },
  ];

  return (
    <div className="space-y-8">
      {/* Risk Management */}
      <div>
        <div className="flex items-center mb-4">
          <Shield className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Risk Management</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Max Position Size (% of portfolio)
            </label>
            <Input
              type="number"
              value={localPreferences.risk_management.max_position_size_percent}
              onChange={(e) => handleInputChange('risk_management', 'max_position_size_percent', parseFloat(e.target.value))}
              min="1"
              max="100"
              step="0.1"
            />
            <p className="text-xs text-text-muted mt-1">
              Maximum percentage of portfolio to allocate to a single position
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Max Daily Loss (% of portfolio)
            </label>
            <Input
              type="number"
              value={localPreferences.risk_management.max_daily_loss_percent}
              onChange={(e) => handleInputChange('risk_management', 'max_daily_loss_percent', parseFloat(e.target.value))}
              min="0.1"
              max="50"
              step="0.1"
            />
            <p className="text-xs text-text-muted mt-1">
              Stop trading if daily losses exceed this percentage
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Max Portfolio Risk (%)
            </label>
            <Input
              type="number"
              value={localPreferences.risk_management.max_portfolio_risk_percent}
              onChange={(e) => handleInputChange('risk_management', 'max_portfolio_risk_percent', parseFloat(e.target.value))}
              min="1"
              max="100"
              step="0.1"
            />
            <p className="text-xs text-text-muted mt-1">
              Maximum total risk exposure across all positions
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Default Stop Loss (%)
            </label>
            <Input
              type="number"
              value={localPreferences.risk_management.stop_loss_percent}
              onChange={(e) => handleInputChange('risk_management', 'stop_loss_percent', parseFloat(e.target.value))}
              min="0.1"
              max="50"
              step="0.1"
            />
            <p className="text-xs text-text-muted mt-1">
              Default stop loss percentage for new positions
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Default Take Profit (%)
            </label>
            <Input
              type="number"
              value={localPreferences.risk_management.take_profit_percent}
              onChange={(e) => handleInputChange('risk_management', 'take_profit_percent', parseFloat(e.target.value))}
              min="0.1"
              max="1000"
              step="0.1"
            />
            <p className="text-xs text-text-muted mt-1">
              Default take profit percentage for new positions
            </p>
          </div>
        </div>
      </div>

      {/* Execution Settings */}
      <div>
        <div className="flex items-center mb-4">
          <Target className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Execution Settings</h4>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Slippage Tolerance (%)
              </label>
              <Input
                type="number"
                value={localPreferences.execution_settings.slippage_tolerance_percent}
                onChange={(e) => handleInputChange('execution_settings', 'slippage_tolerance_percent', parseFloat(e.target.value))}
                min="0.1"
                max="10"
                step="0.1"
              />
              <p className="text-xs text-text-muted mt-1">
                Maximum acceptable price slippage for trade execution
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Max Execution Delay (seconds)
              </label>
              <Input
                type="number"
                value={localPreferences.execution_settings.max_execution_delay_seconds}
                onChange={(e) => handleInputChange('execution_settings', 'max_execution_delay_seconds', parseInt(e.target.value))}
                min="1"
                max="300"
              />
              <p className="text-xs text-text-muted mt-1">
                Cancel trade if execution takes longer than this
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
              <div>
                <p className="font-medium text-text-primary">Auto-Execute Signals</p>
                <p className="text-sm text-text-secondary">
                  Automatically execute trading signals without manual confirmation
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localPreferences.execution_settings.auto_execute_signals}
                  onChange={() => handleToggle('execution_settings', 'auto_execute_signals')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
              <div>
                <p className="font-medium text-text-primary">Require Confirmation</p>
                <p className="text-sm text-text-secondary">
                  Show confirmation dialog before executing trades
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localPreferences.execution_settings.require_confirmation}
                  onChange={() => handleToggle('execution_settings', 'require_confirmation')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Signal Preferences */}
      <div>
        <div className="flex items-center mb-4">
          <TrendingUp className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Signal Preferences</h4>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Min Confidence Threshold (%)
              </label>
              <Input
                type="number"
                value={localPreferences.signal_preferences.min_confidence_threshold}
                onChange={(e) => handleInputChange('signal_preferences', 'min_confidence_threshold', parseFloat(e.target.value))}
                min="1"
                max="100"
                step="1"
              />
              <p className="text-xs text-text-muted mt-1">
                Only show signals with confidence above this threshold
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Min Market Cap (USD)
              </label>
              <Input
                type="number"
                value={localPreferences.signal_preferences.min_market_cap}
                onChange={(e) => handleInputChange('signal_preferences', 'min_market_cap', parseInt(e.target.value))}
                min="0"
                step="1000"
              />
              <p className="text-xs text-text-muted mt-1">
                Only generate signals for tokens above this market cap
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-secondary mb-3">
              Preferred Timeframes
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {timeframes.map((timeframe) => (
                <label key={timeframe.value} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.signal_preferences.preferred_timeframes.includes(timeframe.value)}
                    onChange={() => handleArrayToggle('signal_preferences', 'preferred_timeframes', timeframe.value)}
                    className="rounded border-surface-tertiary text-primary focus:ring-primary"
                  />
                  <span className="text-sm text-text-primary">{timeframe.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
            <div>
              <p className="font-medium text-text-primary">Exclude Low Volume Tokens</p>
              <p className="text-sm text-text-secondary">
                Filter out tokens with low trading volume
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.signal_preferences.exclude_low_volume_tokens}
                onChange={() => handleToggle('signal_preferences', 'exclude_low_volume_tokens')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Portfolio Settings */}
      <div>
        <div className="flex items-center mb-4">
          <DollarSign className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Portfolio Settings</h4>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Rebalance Threshold (%)
              </label>
              <Input
                type="number"
                value={localPreferences.portfolio_settings.rebalance_threshold_percent}
                onChange={(e) => handleInputChange('portfolio_settings', 'rebalance_threshold_percent', parseFloat(e.target.value))}
                min="1"
                max="50"
                step="0.1"
              />
              <p className="text-xs text-text-muted mt-1">
                Trigger rebalancing when allocation deviates by this percentage
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Base Currency
              </label>
              <select
                value={localPreferences.portfolio_settings.preferred_base_currency}
                onChange={(e) => handleInputChange('portfolio_settings', 'preferred_base_currency', e.target.value)}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
              >
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="BTC">BTC</option>
                <option value="ETH">ETH</option>
              </select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
              <div>
                <p className="font-medium text-text-primary">Auto-Rebalance</p>
                <p className="text-sm text-text-secondary">
                  Automatically rebalance portfolio when thresholds are exceeded
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localPreferences.portfolio_settings.auto_rebalance}
                  onChange={() => handleToggle('portfolio_settings', 'auto_rebalance')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
              <div>
                <p className="font-medium text-text-primary">Compound Profits</p>
                <p className="text-sm text-text-secondary">
                  Reinvest profits automatically into the portfolio
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={localPreferences.portfolio_settings.compound_profits}
                  onChange={() => handleToggle('portfolio_settings', 'compound_profits')}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={updatePreferencesMutation.isPending}
          className="bg-primary hover:bg-primary-dark"
        >
          {updatePreferencesMutation.isPending ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>
    </div>
  );
};

export default TradingSettings;
