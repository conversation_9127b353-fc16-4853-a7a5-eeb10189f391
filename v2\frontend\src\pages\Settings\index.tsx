import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { User, Shield, Bell, Palette, Database, Key, Globe, Zap } from 'lucide-react';
import ProfileSettings from './components/ProfileSettings';
import SecuritySettings from './components/SecuritySettings';
import NotificationSettings from './components/NotificationSettings';
import TradingSettings from './components/TradingSettings';
import APISettings from './components/APISettings';
import AppearanceSettings from './components/AppearanceSettings';
import DataSettings from './components/DataSettings';
import AdvancedSettings from './components/AdvancedSettings';

/**
 * Settings page component
 */
const Settings: React.FC = () => {
  const [activeSection, setActiveSection] = useState<string>('profile');

  const settingsSections = [
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      description: 'Personal information and account details',
    },
    {
      id: 'security',
      label: 'Security',
      icon: Shield,
      description: 'Password, 2FA, and security preferences',
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: Bell,
      description: 'Email, push, and alert preferences',
    },
    {
      id: 'trading',
      label: 'Trading',
      icon: Zap,
      description: 'Trading parameters and risk settings',
    },
    {
      id: 'api',
      label: 'API Keys',
      icon: Key,
      description: 'Manage API keys and integrations',
    },
    {
      id: 'appearance',
      label: 'Appearance',
      icon: Palette,
      description: 'Theme, layout, and display preferences',
    },
    {
      id: 'data',
      label: 'Data & Privacy',
      icon: Database,
      description: 'Data export, backup, and privacy settings',
    },
    {
      id: 'advanced',
      label: 'Advanced',
      icon: Globe,
      description: 'Advanced configuration and developer options',
    },
  ];

  const renderSettingsContent = () => {
    switch (activeSection) {
      case 'profile':
        return <ProfileSettings />;
      case 'security':
        return <SecuritySettings />;
      case 'notifications':
        return <NotificationSettings />;
      case 'trading':
        return <TradingSettings />;
      case 'api':
        return <APISettings />;
      case 'appearance':
        return <AppearanceSettings />;
      case 'data':
        return <DataSettings />;
      case 'advanced':
        return <AdvancedSettings />;
      default:
        return <ProfileSettings />;
    }
  };

  const currentSection = settingsSections.find(section => section.id === activeSection);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-text-primary">Settings</h1>
        <p className="text-text-secondary mt-1">
          Manage your account settings and preferences.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <Card className="p-4">
            <h2 className="text-lg font-semibold text-text-primary mb-4">Settings</h2>
            <nav className="space-y-1">
              {settingsSections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center px-3 py-2 text-left rounded-lg transition-colors ${
                      activeSection === section.id
                        ? 'bg-primary text-white'
                        : 'text-text-secondary hover:bg-surface-secondary hover:text-text-primary'
                    }`}
                  >
                    <Icon className="w-4 h-4 mr-3" />
                    <span className="font-medium">{section.label}</span>
                  </button>
                );
              })}
            </nav>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3">
          <Card className="p-6">
            {/* Section Header */}
            <div className="mb-6">
              <div className="flex items-center mb-2">
                {currentSection && (
                  <>
                    <currentSection.icon className="w-6 h-6 text-primary mr-3" />
                    <h2 className="text-2xl font-bold text-text-primary">
                      {currentSection.label}
                    </h2>
                  </>
                )}
              </div>
              {currentSection && (
                <p className="text-text-secondary">{currentSection.description}</p>
              )}
            </div>

            {/* Settings Content */}
            <div className="space-y-6">
              {renderSettingsContent()}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Settings;
