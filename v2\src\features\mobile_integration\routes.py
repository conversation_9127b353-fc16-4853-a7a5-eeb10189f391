"""
Mobile Integration Routes

FastAPI routes for mobile integration providing push notifications,
SMS services, mobile-optimized APIs, and PWA functionality.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from fastapi.responses import Response, JSONResponse, PlainTextResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer

import structlog

from src.features.security.auth_manager import AuthManager
from src.features.mobile_integration.push_notification_service import (
    PushNotificationService, PushMessage, PushPlatform
)
from src.features.mobile_integration.sms_service import SMSService
from src.features.mobile_integration.mobile_api_service import (
    MobileAPIService, MobileDataFormat
)
from src.features.mobile_integration.pwa_service import PWAService
from src.shared.types import NotificationPriority

logger = structlog.get_logger(__name__)
security = HTTPBearer()

# Create router
router = APIRouter(prefix="/api/v1/mobile", tags=["Mobile Integration"])


# Dependency providers
def get_push_notification_service():
    """Get PushNotificationService instance"""
    return PushNotificationService()


def get_sms_service():
    """Get SMSService instance"""
    return SMSService()


def get_mobile_api_service():
    """Get MobileAPIService instance"""
    return MobileAPIService()


def get_pwa_service():
    """Get PWAService instance"""
    return PWAService()


# Dependency to get current user
async def get_current_user(token: str = Depends(security)) -> str:
    """Get current user from JWT token"""
    auth_manager = AuthManager()  # This should be injected in production
    user_id = await auth_manager.verify_token(token.credentials)
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid authentication token")
    return user_id


# Push notification endpoints
@router.post("/push/register")
async def register_push_device(
    device_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    push_service: PushNotificationService = Depends(get_push_notification_service)
):
    """
    Register a device for push notifications.
    
    Args:
        device_data: Device registration data
        
    Returns:
        Registration result
    """
    try:
        success = await push_service.register_device(
            user_id=current_user,
            token=device_data["token"],
            platform=PushPlatform(device_data["platform"]),
            device_info=device_data.get("device_info")
        )
        
        if success:
            return {"status": "success", "message": "Device registered for push notifications"}
        else:
            raise HTTPException(status_code=400, detail="Failed to register device")
            
    except Exception as e:
        logger.error("Failed to register push device", user_id=current_user, error=str(e))
        raise


@router.post("/push/send")
async def send_push_notification(
    notification_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    push_service: PushNotificationService = Depends(get_push_notification_service)
):
    """
    Send push notification to user's devices.
    
    Args:
        notification_data: Notification data
        
    Returns:
        Send result
    """
    try:
        message = PushMessage(
            title=notification_data["title"],
            body=notification_data["body"],
            data=notification_data.get("data"),
            image_url=notification_data.get("image_url"),
            action_url=notification_data.get("action_url"),
            priority=NotificationPriority(notification_data.get("priority", "MEDIUM"))
        )
        
        platforms = None
        if notification_data.get("platforms"):
            platforms = [PushPlatform(p) for p in notification_data["platforms"]]
        
        results = await push_service.send_notification(current_user, message, platforms)
        
        return {
            "status": "success",
            "results": results,
            "message": "Push notification sent"
        }
        
    except Exception as e:
        logger.error("Failed to send push notification", user_id=current_user, error=str(e))
        raise


@router.delete("/push/unregister/{platform}")
async def unregister_push_device(
    platform: str = Path(...),
    current_user: str = Depends(get_current_user),
    push_service: PushNotificationService = Depends(get_push_notification_service)
):
    """
    Unregister a device from push notifications.
    
    Args:
        platform: Device platform
        
    Returns:
        Unregistration result
    """
    try:
        success = await push_service.unregister_device(
            user_id=current_user,
            platform=PushPlatform(platform)
        )
        
        if success:
            return {"status": "success", "message": "Device unregistered"}
        else:
            raise HTTPException(status_code=400, detail="Failed to unregister device")
            
    except Exception as e:
        logger.error("Failed to unregister push device", user_id=current_user, error=str(e))
        raise


@router.get("/push/stats")
async def get_push_notification_stats(
    current_user: str = Depends(get_current_user),
    push_service: PushNotificationService = Depends(get_push_notification_service)
):
    """
    Get push notification statistics.
    
    Returns:
        Notification statistics
    """
    try:
        stats = await push_service.get_notification_stats(current_user)
        return stats
        
    except Exception as e:
        logger.error("Failed to get push stats", user_id=current_user, error=str(e))
        raise


# SMS endpoints
@router.post("/sms/send")
async def send_sms(
    sms_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    sms_service: SMSService = Depends(get_sms_service)
):
    """
    Send SMS notification.
    
    Args:
        sms_data: SMS data
        
    Returns:
        SMS send result
    """
    try:
        result = await sms_service.send_sms(
            user_id=current_user,
            phone_number=sms_data["phone_number"],
            message=sms_data["message"],
            priority=NotificationPriority(sms_data.get("priority", "MEDIUM"))
        )
        
        return result
        
    except Exception as e:
        logger.error("Failed to send SMS", user_id=current_user, error=str(e))
        raise


@router.get("/sms/status/{message_id}")
async def get_sms_status(
    message_id: str = Path(...),
    current_user: str = Depends(get_current_user),
    sms_service: SMSService = Depends(get_sms_service)
):
    """
    Get SMS delivery status.
    
    Args:
        message_id: SMS message ID
        
    Returns:
        SMS delivery status
    """
    try:
        status = await sms_service.get_delivery_status(current_user, message_id)
        return status.dict()
        
    except Exception as e:
        logger.error("Failed to get SMS status", user_id=current_user, message_id=message_id, error=str(e))
        raise


@router.get("/sms/history")
async def get_sms_history(
    limit: int = Query(50, ge=1, le=200),
    current_user: str = Depends(get_current_user),
    sms_service: SMSService = Depends(get_sms_service)
):
    """
    Get SMS history.
    
    Args:
        limit: Maximum number of records
        
    Returns:
        SMS history
    """
    try:
        history = await sms_service.get_user_sms_history(current_user, limit)
        return {"history": [report.dict() for report in history]}
        
    except Exception as e:
        logger.error("Failed to get SMS history", user_id=current_user, error=str(e))
        raise


@router.get("/sms/stats")
async def get_sms_stats(
    current_user: str = Depends(get_current_user),
    sms_service: SMSService = Depends(get_sms_service)
):
    """
    Get SMS usage statistics.
    
    Returns:
        SMS statistics
    """
    try:
        stats = await sms_service.get_sms_statistics(current_user)
        return stats
        
    except Exception as e:
        logger.error("Failed to get SMS stats", user_id=current_user, error=str(e))
        raise


# Mobile API endpoints
@router.get("/dashboard")
async def get_mobile_dashboard(
    format: MobileDataFormat = Query(MobileDataFormat.COMPACT),
    if_modified_since: Optional[datetime] = Query(None),
    current_user: str = Depends(get_current_user),
    mobile_api: MobileAPIService = Depends(get_mobile_api_service)
):
    """
    Get mobile-optimized dashboard data.
    
    Args:
        format: Data format (compact, standard, detailed)
        if_modified_since: Client's last update timestamp
        
    Returns:
        Mobile dashboard data
    """
    try:
        response = await mobile_api.get_mobile_dashboard(
            user_id=current_user,
            format=format,
            if_modified_since=if_modified_since
        )
        
        if response.data is None:
            return Response(status_code=304)  # Not Modified
        
        return response.data
        
    except Exception as e:
        logger.error("Failed to get mobile dashboard", user_id=current_user, error=str(e))
        raise


@router.get("/portfolio")
async def get_mobile_portfolio(
    format: MobileDataFormat = Query(MobileDataFormat.COMPACT),
    current_user: str = Depends(get_current_user),
    mobile_api: MobileAPIService = Depends(get_mobile_api_service)
):
    """
    Get mobile-optimized portfolio data.
    
    Args:
        format: Data format
        
    Returns:
        Mobile portfolio data
    """
    try:
        response = await mobile_api.get_mobile_portfolio(
            user_id=current_user,
            format=format
        )
        
        return response.data
        
    except Exception as e:
        logger.error("Failed to get mobile portfolio", user_id=current_user, error=str(e))
        raise


@router.get("/signals")
async def get_mobile_signals(
    limit: int = Query(20, ge=1, le=100),
    format: MobileDataFormat = Query(MobileDataFormat.COMPACT),
    current_user: str = Depends(get_current_user),
    mobile_api: MobileAPIService = Depends(get_mobile_api_service)
):
    """
    Get mobile-optimized signals data.
    
    Args:
        limit: Maximum number of signals
        format: Data format
        
    Returns:
        Mobile signals data
    """
    try:
        response = await mobile_api.get_mobile_signals(
            user_id=current_user,
            limit=limit,
            format=format
        )
        
        return response.data
        
    except Exception as e:
        logger.error("Failed to get mobile signals", user_id=current_user, error=str(e))
        raise


@router.post("/offline/prepare")
async def prepare_offline_data(
    data_types: List[str] = Body(...),
    current_user: str = Depends(get_current_user),
    mobile_api: MobileAPIService = Depends(get_mobile_api_service)
):
    """
    Prepare offline data packages.
    
    Args:
        data_types: Types of data to prepare
        
    Returns:
        Offline data packages
    """
    try:
        packages = await mobile_api.prepare_offline_data(current_user, data_types)
        
        return {
            "status": "success",
            "packages": {
                data_type: {
                    "data_type": package.data_type,
                    "created_at": package.created_at,
                    "expires_at": package.expires_at,
                    "version": package.version
                }
                for data_type, package in packages.items()
            }
        }
        
    except Exception as e:
        logger.error("Failed to prepare offline data", user_id=current_user, error=str(e))
        raise


@router.post("/sync")
async def sync_mobile_data(
    client_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    mobile_api: MobileAPIService = Depends(get_mobile_api_service)
):
    """
    Synchronize mobile app data.
    
    Args:
        client_data: Client's current data state
        
    Returns:
        Synchronization result
    """
    try:
        sync_result = await mobile_api.sync_mobile_data(current_user, client_data)
        return sync_result
        
    except Exception as e:
        logger.error("Failed to sync mobile data", user_id=current_user, error=str(e))
        raise


# PWA endpoints
@router.get("/pwa/manifest.json")
async def get_pwa_manifest(
    pwa_service: PWAService = Depends(get_pwa_service)
):
    """
    Get PWA manifest file.
    
    Returns:
        PWA manifest JSON
    """
    try:
        manifest = await pwa_service.generate_manifest()
        return JSONResponse(
            content=manifest,
            headers={"Content-Type": "application/manifest+json"}
        )
        
    except Exception as e:
        logger.error("Failed to get PWA manifest", error=str(e))
        raise


@router.get("/pwa/sw.js")
async def get_service_worker(
    pwa_service: PWAService = Depends(get_pwa_service)
):
    """
    Get service worker JavaScript.
    
    Returns:
        Service worker code
    """
    try:
        sw_code = await pwa_service.generate_service_worker()
        return PlainTextResponse(
            content=sw_code,
            headers={"Content-Type": "application/javascript"}
        )
        
    except Exception as e:
        logger.error("Failed to get service worker", error=str(e))
        raise


@router.get("/pwa/offline.html")
async def get_offline_page(
    pwa_service: PWAService = Depends(get_pwa_service)
):
    """
    Get offline fallback page.
    
    Returns:
        Offline page HTML
    """
    try:
        offline_html = await pwa_service.get_offline_page()
        return PlainTextResponse(
            content=offline_html,
            headers={"Content-Type": "text/html"}
        )
        
    except Exception as e:
        logger.error("Failed to get offline page", error=str(e))
        raise


@router.get("/pwa/install-prompt")
async def get_install_prompt(
    pwa_service: PWAService = Depends(get_pwa_service)
):
    """
    Get PWA installation prompt data.
    
    Returns:
        Installation prompt configuration
    """
    try:
        prompt_data = await pwa_service.get_pwa_installation_prompt()
        return prompt_data
        
    except Exception as e:
        logger.error("Failed to get install prompt", error=str(e))
        raise


@router.post("/pwa/track")
async def track_pwa_usage(
    request: Request,
    tracking_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    pwa_service: PWAService = Depends(get_pwa_service)
):
    """
    Track PWA usage analytics.
    
    Args:
        tracking_data: Usage tracking data
        
    Returns:
        Tracking result
    """
    try:
        # Add request metadata
        tracking_data["user_agent"] = request.headers.get("user-agent")
        tracking_data["ip_address"] = request.client.host
        
        await pwa_service.track_pwa_usage(
            user_id=current_user,
            event=tracking_data["event"],
            data=tracking_data
        )
        
        return {"status": "success", "message": "Usage tracked"}
        
    except Exception as e:
        logger.error("Failed to track PWA usage", user_id=current_user, error=str(e))
        raise


# Bulk operations
@router.post("/push/bulk")
async def send_bulk_push_notifications(
    notifications: List[Dict[str, Any]] = Body(...),
    push_service: PushNotificationService = Depends(get_push_notification_service)
):
    """
    Send bulk push notifications.
    
    Args:
        notifications: List of notification data
        
    Returns:
        Bulk send results
    """
    try:
        results = await push_service.send_bulk_notifications(notifications)
        return results
        
    except Exception as e:
        logger.error("Failed to send bulk push notifications", error=str(e))
        raise


@router.post("/sms/bulk")
async def send_bulk_sms(
    messages: List[Dict[str, Any]] = Body(...),
    sms_service: SMSService = Depends(get_sms_service)
):
    """
    Send bulk SMS messages.
    
    Args:
        messages: List of SMS message data
        
    Returns:
        Bulk send results
    """
    try:
        results = await sms_service.send_bulk_sms(messages)
        return results
        
    except Exception as e:
        logger.error("Failed to send bulk SMS", error=str(e))
        raise
