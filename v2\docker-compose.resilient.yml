# ===========================================
# 🐳 TOKENTRACKER V2 RESILIENT DOCKER COMPOSE
# ===========================================
# Docker Compose configuration optimized for poor network conditions

version: '3.8'

services:
  # Development service with network resilience
  tokentracker-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
      args:
        - BUILDKIT_INLINE_CACHE=1
        - BUILD_DATE=${BUILD_DATE:-}
        - VERSION=${VERSION:-dev}
        - VCS_REF=${VCS_REF:-}
      # Network resilience options
      network: host
    image: tokentracker-v2:development
    container_name: tokentracker-dev
    ports:
      - "3000:3000"
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - NODE_ENV=development
      - PIP_RETRIES=10
      - PIP_TIMEOUT=300
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
      - pip-cache:/tmp/pip-cache
      - app-logs:/app/logs
    networks:
      - tokentracker-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Production service with network resilience
  tokentracker-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
      args:
        - BUILDKIT_INLINE_CACHE=1
        - BUILD_DATE=${BUILD_DATE:-}
        - VERSION=${VERSION:-latest}
        - VCS_REF=${VCS_REF:-}
      network: host
    image: tokentracker-v2:production
    container_name: tokentracker-prod
    ports:
      - "8000:8000"
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - NODE_ENV=production
      - PIP_RETRIES=10
      - PIP_TIMEOUT=300
      - PORT=8000
    volumes:
      - app-logs:/app/logs
      - app-data:/app/data
      - app-cache:/app/cache
      - pip-cache:/tmp/pip-cache
    networks:
      - tokentracker-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Monitoring service
  tokentracker-monitoring:
    build:
      context: .
      dockerfile: Dockerfile
      target: monitoring
      args:
        - BUILDKIT_INLINE_CACHE=1
        - BUILD_DATE=${BUILD_DATE:-}
        - VERSION=${VERSION:-monitoring}
        - VCS_REF=${VCS_REF:-}
      network: host
    image: tokentracker-v2:monitoring
    container_name: tokentracker-monitoring
    ports:
      - "3000:3000"
      - "9090:9090"
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - NODE_ENV=production
      - PIP_RETRIES=10
      - PIP_TIMEOUT=300
    volumes:
      - app-logs:/app/logs
      - app-data:/app/data
      - pip-cache:/tmp/pip-cache
    networks:
      - tokentracker-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1.5G
          cpus: '1.5'
        reservations:
          memory: 768M
          cpus: '0.75'

  # Redis for caching (optional, helps with network resilience)
  redis:
    image: redis:7-alpine
    container_name: tokentracker-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - tokentracker-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

# Named volumes for persistence and caching
volumes:
  app-logs:
    driver: local
  app-data:
    driver: local
  app-cache:
    driver: local
  pip-cache:
    driver: local
  redis-data:
    driver: local

# Custom network for service communication
networks:
  tokentracker-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
