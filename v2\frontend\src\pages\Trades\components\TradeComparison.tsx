import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { TradesService } from '@/services/trades';

const TradeComparison: React.FC = () => {
  const [tradeIds, setTradeIds] = useState<string[]>(['', '']);
  const [comparisonData, setComparisonData] = useState<any>(null);

  const compareMutation = useMutation({
    mutationFn: (params: { trade_ids: string[]; metrics: ('pnl' | 'duration' | 'fees' | 'slippage')[] }) =>
      TradesService.getTradeComparison(params),
    onSuccess: (data) => {
      setComparisonData(data);
    },
  });

  const handleCompare = () => {
    const validTradeIds = tradeIds.filter(id => id.trim());
    if (validTradeIds.length < 2) {
      alert('Please enter at least 2 trade IDs to compare');
      return;
    }

    compareMutation.mutate({
      trade_ids: validTradeIds,
      metrics: ['pnl', 'duration', 'fees', 'slippage'],
    });
  };

  const addTradeIdField = () => {
    setTradeIds(prev => [...prev, '']);
  };

  const removeTradeIdField = (index: number) => {
    setTradeIds(prev => prev.filter((_, i) => i !== index));
  };

  const updateTradeId = (index: number, value: string) => {
    setTradeIds(prev => prev.map((id, i) => i === index ? value : id));
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Compare Trades</h3>
        <div className="space-y-4">
          {tradeIds.map((tradeId, index) => (
            <div key={index} className="flex items-center space-x-3">
              <input
                type="text"
                value={tradeId}
                onChange={(e) => updateTradeId(index, e.target.value)}
                placeholder={`Trade ID ${index + 1}`}
                className="flex-1 px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
              />
              {tradeIds.length > 2 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeTradeIdField(index)}
                  className="text-red-600"
                >
                  Remove
                </Button>
              )}
            </div>
          ))}
          
          <div className="flex space-x-3">
            <Button variant="outline" onClick={addTradeIdField}>
              Add Another Trade
            </Button>
            <Button
              onClick={handleCompare}
              disabled={compareMutation.isPending}
              className="bg-primary hover:bg-primary-dark"
            >
              {compareMutation.isPending ? 'Comparing...' : 'Compare Trades'}
            </Button>
          </div>
        </div>
      </Card>

      {comparisonData && (
        <>
          {/* Summary */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-text-primary mb-4">Comparison Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-text-secondary mb-1">Best Performing</div>
                <div className="text-lg font-bold text-green-600">
                  {comparisonData.summary.best_performing.slice(0, 8)}...
                </div>
              </div>
              <div>
                <div className="text-sm text-text-secondary mb-1">Worst Performing</div>
                <div className="text-lg font-bold text-red-600">
                  {comparisonData.summary.worst_performing.slice(0, 8)}...
                </div>
              </div>
              <div>
                <div className="text-sm text-text-secondary mb-1">Average P&L</div>
                <div
                  className={`text-lg font-bold ${
                    comparisonData.summary.average_pnl >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {formatCurrency(comparisonData.summary.average_pnl)}
                </div>
              </div>
            </div>
          </Card>

          {/* Detailed Comparison */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-text-primary mb-4">Detailed Comparison</h3>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-surface-tertiary">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-text-secondary uppercase">
                      Trade ID
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-text-secondary uppercase">
                      Token
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-text-secondary uppercase">
                      P&L
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-text-secondary uppercase">
                      P&L %
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-text-secondary uppercase">
                      Duration (hrs)
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-text-secondary uppercase">
                      Fees
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-text-secondary uppercase">
                      Slippage
                    </th>
                    <th className="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase">
                      Quality
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-surface-tertiary">
                  {comparisonData.comparison.map((trade: any, index: number) => (
                    <tr key={index} className="hover:bg-surface-secondary">
                      <td className="px-4 py-3 text-sm font-mono text-text-primary">
                        {trade.trade_id.slice(0, 8)}...
                      </td>
                      <td className="px-4 py-3 text-sm text-text-primary">
                        {trade.token_symbol}
                      </td>
                      <td
                        className={`px-4 py-3 text-sm text-right font-medium ${
                          trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        {formatCurrency(trade.pnl)}
                      </td>
                      <td
                        className={`px-4 py-3 text-sm text-right font-medium ${
                          trade.pnl_percentage >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        {formatPercentage(trade.pnl_percentage)}
                      </td>
                      <td className="px-4 py-3 text-sm text-right text-text-primary">
                        {trade.duration_hours.toFixed(1)}
                      </td>
                      <td className="px-4 py-3 text-sm text-right text-text-primary">
                        {formatCurrency(trade.total_fees)}
                      </td>
                      <td className="px-4 py-3 text-sm text-right text-text-primary">
                        {formatPercentage(trade.slippage)}
                      </td>
                      <td className="px-4 py-3 text-center">
                        <span
                          className={`px-2 py-1 text-xs rounded-full ${
                            trade.execution_quality === 'excellent'
                              ? 'bg-green-100 text-green-800'
                              : trade.execution_quality === 'good'
                              ? 'bg-blue-100 text-blue-800'
                              : trade.execution_quality === 'fair'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {trade.execution_quality.toUpperCase()}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </>
      )}

      {!comparisonData && (
        <Card className="p-8 text-center">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Trade Comparison</h3>
          <p className="text-text-muted">
            Enter trade IDs above and click "Compare Trades" to see a detailed comparison.
          </p>
        </Card>
      )}
    </div>
  );
};

export default TradeComparison;
