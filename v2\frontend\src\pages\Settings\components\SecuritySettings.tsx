import React, { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Shield, Lock, Smartphone, Key, AlertTriangle, CheckCircle, Eye, EyeOff } from 'lucide-react';

const SecuritySettings: React.FC = () => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });

  // Fetch security status
  const { data: securityStatus } = useQuery({
    queryKey: ['security-status'],
    queryFn: async () => {
      // Mock API call
      return {
        two_factor_enabled: false,
        backup_codes_generated: false,
        last_password_change: '2024-06-15T10:30:00Z',
        active_sessions: 3,
        login_attempts: [],
        trusted_devices: 2,
      };
    },
  });

  // Change password mutation
  const changePasswordMutation = useMutation({
    mutationFn: async (data: any) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true };
    },
    onSuccess: () => {
      setPasswordData({
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
      alert('Password changed successfully!');
    },
  });

  // Enable 2FA mutation
  const enable2FAMutation = useMutation({
    mutationFn: async () => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        qr_code: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        secret: 'JBSWY3DPEHPK3PXP',
        backup_codes: ['123456', '789012', '345678', '901234', '567890'],
      };
    },
  });

  const handlePasswordChange = () => {
    if (passwordData.new_password !== passwordData.confirm_password) {
      alert('New passwords do not match');
      return;
    }
    if (passwordData.new_password.length < 8) {
      alert('Password must be at least 8 characters long');
      return;
    }
    changePasswordMutation.mutate(passwordData);
  };

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const getPasswordStrengthLabel = (strength: number) => {
    switch (strength) {
      case 0:
      case 1:
        return { label: 'Very Weak', color: 'text-red-600' };
      case 2:
        return { label: 'Weak', color: 'text-orange-600' };
      case 3:
        return { label: 'Fair', color: 'text-yellow-600' };
      case 4:
        return { label: 'Good', color: 'text-blue-600' };
      case 5:
        return { label: 'Strong', color: 'text-green-600' };
      default:
        return { label: 'Unknown', color: 'text-gray-600' };
    }
  };

  const passwordStrength = getPasswordStrength(passwordData.new_password);
  const strengthInfo = getPasswordStrengthLabel(passwordStrength);

  return (
    <div className="space-y-8">
      {/* Security Overview */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Security Overview</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-surface-secondary rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Shield className="w-5 h-5 text-primary mr-2" />
                <span className="font-medium text-text-primary">Two-Factor Authentication</span>
              </div>
              {securityStatus?.two_factor_enabled ? (
                <CheckCircle className="w-5 h-5 text-green-600" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
              )}
            </div>
            <p className="text-sm text-text-secondary">
              {securityStatus?.two_factor_enabled ? 'Enabled' : 'Not enabled'}
            </p>
          </div>

          <div className="p-4 bg-surface-secondary rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Smartphone className="w-5 h-5 text-primary mr-2" />
                <span className="font-medium text-text-primary">Trusted Devices</span>
              </div>
              <span className="text-sm font-medium text-text-primary">
                {securityStatus?.trusted_devices || 0}
              </span>
            </div>
            <p className="text-sm text-text-secondary">
              Devices you've marked as trusted
            </p>
          </div>

          <div className="p-4 bg-surface-secondary rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Key className="w-5 h-5 text-primary mr-2" />
                <span className="font-medium text-text-primary">Active Sessions</span>
              </div>
              <span className="text-sm font-medium text-text-primary">
                {securityStatus?.active_sessions || 0}
              </span>
            </div>
            <p className="text-sm text-text-secondary">
              Currently logged in sessions
            </p>
          </div>

          <div className="p-4 bg-surface-secondary rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Lock className="w-5 h-5 text-primary mr-2" />
                <span className="font-medium text-text-primary">Last Password Change</span>
              </div>
            </div>
            <p className="text-sm text-text-secondary">
              {securityStatus?.last_password_change 
                ? new Date(securityStatus.last_password_change).toLocaleDateString()
                : 'Never'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Change Password */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Change Password</h4>
        <div className="space-y-4 max-w-md">
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Current Password
            </label>
            <div className="relative">
              <Input
                type={showCurrentPassword ? 'text' : 'password'}
                value={passwordData.current_password}
                onChange={(e) => setPasswordData(prev => ({ ...prev, current_password: e.target.value }))}
                placeholder="Enter current password"
              />
              <button
                type="button"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-muted hover:text-text-primary"
              >
                {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              New Password
            </label>
            <div className="relative">
              <Input
                type={showNewPassword ? 'text' : 'password'}
                value={passwordData.new_password}
                onChange={(e) => setPasswordData(prev => ({ ...prev, new_password: e.target.value }))}
                placeholder="Enter new password"
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-muted hover:text-text-primary"
              >
                {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            {passwordData.new_password && (
              <div className="mt-2">
                <div className="flex items-center space-x-2">
                  <div className="flex-1 bg-surface-tertiary rounded-full h-2">
                    <div
                      className="h-2 rounded-full transition-all duration-300"
                      style={{
                        width: `${(passwordStrength / 5) * 100}%`,
                        backgroundColor: 
                          passwordStrength <= 1 ? '#ef4444' :
                          passwordStrength <= 2 ? '#f59e0b' :
                          passwordStrength <= 3 ? '#eab308' :
                          passwordStrength <= 4 ? '#3b82f6' : '#10b981'
                      }}
                    />
                  </div>
                  <span className={`text-xs font-medium ${strengthInfo.color}`}>
                    {strengthInfo.label}
                  </span>
                </div>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Confirm New Password
            </label>
            <div className="relative">
              <Input
                type={showConfirmPassword ? 'text' : 'password'}
                value={passwordData.confirm_password}
                onChange={(e) => setPasswordData(prev => ({ ...prev, confirm_password: e.target.value }))}
                placeholder="Confirm new password"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-muted hover:text-text-primary"
              >
                {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            {passwordData.confirm_password && passwordData.new_password !== passwordData.confirm_password && (
              <p className="text-sm text-red-600 mt-1">Passwords do not match</p>
            )}
          </div>

          <Button
            onClick={handlePasswordChange}
            disabled={
              !passwordData.current_password ||
              !passwordData.new_password ||
              !passwordData.confirm_password ||
              passwordData.new_password !== passwordData.confirm_password ||
              changePasswordMutation.isPending
            }
            className="bg-primary hover:bg-primary-dark"
          >
            {changePasswordMutation.isPending ? 'Changing Password...' : 'Change Password'}
          </Button>
        </div>
      </div>

      {/* Two-Factor Authentication */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Two-Factor Authentication</h4>
        {securityStatus?.two_factor_enabled ? (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center mb-2">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              <span className="font-medium text-green-800">2FA is enabled</span>
            </div>
            <p className="text-sm text-green-700 mb-4">
              Your account is protected with two-factor authentication.
            </p>
            <div className="flex space-x-3">
              <Button variant="outline" size="sm">
                View Backup Codes
              </Button>
              <Button variant="outline" size="sm" className="text-red-600 border-red-600">
                Disable 2FA
              </Button>
            </div>
          </div>
        ) : (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center mb-2">
              <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
              <span className="font-medium text-yellow-800">2FA is not enabled</span>
            </div>
            <p className="text-sm text-yellow-700 mb-4">
              Enable two-factor authentication to add an extra layer of security to your account.
            </p>
            <Button
              onClick={() => enable2FAMutation.mutate()}
              disabled={enable2FAMutation.isPending}
              className="bg-primary hover:bg-primary-dark"
            >
              {enable2FAMutation.isPending ? 'Setting up...' : 'Enable 2FA'}
            </Button>
          </div>
        )}
      </div>

      {/* Security Recommendations */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Security Recommendations</h4>
        <div className="space-y-3">
          <div className="flex items-start p-3 bg-surface-secondary rounded-lg">
            <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-0.5" />
            <div>
              <p className="font-medium text-text-primary">Use a strong, unique password</p>
              <p className="text-sm text-text-secondary">
                Your password should be at least 12 characters long and include a mix of letters, numbers, and symbols.
              </p>
            </div>
          </div>
          <div className="flex items-start p-3 bg-surface-secondary rounded-lg">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mr-3 mt-0.5" />
            <div>
              <p className="font-medium text-text-primary">Enable two-factor authentication</p>
              <p className="text-sm text-text-secondary">
                Add an extra layer of security by requiring a code from your phone in addition to your password.
              </p>
            </div>
          </div>
          <div className="flex items-start p-3 bg-surface-secondary rounded-lg">
            <Shield className="w-5 h-5 text-blue-600 mr-3 mt-0.5" />
            <div>
              <p className="font-medium text-text-primary">Review active sessions regularly</p>
              <p className="text-sm text-text-secondary">
                Check for any unfamiliar devices or locations and revoke access if needed.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecuritySettings;
