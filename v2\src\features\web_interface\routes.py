"""
Web Interface Routes

FastAPI routes for the web interface providing dashboard, user management,
charts, and trade history endpoints. Follows security best practices.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, Request
from fastapi.responses import Response
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>earer
from pydantic import BaseModel, Field

import structlog
import json

from src.shared.types import (
    DashboardData, RealTimeUpdate, UserInterfaceData, UserProfile,
    NotificationPreferences, PerformanceChart, SignalChart, TradeChart,
    PaginatedTradeHistory, TradeAnalytics, TradeExportData, TradeFilter
)
from src.features.security.auth_manager import AuthManager
from src.features.security.user_manager import UserManager
from src.features.web_interface.dashboard_service import DashboardService
from src.shared.types import UserRole
from src.features.web_interface.user_interface_manager import UserInterfaceManager
from src.features.web_interface.chart_service import ChartService, Time<PERSON>rame, ChartType
from src.features.web_interface.trade_history_service import TradeHistoryService, SortOrder
from src.features.paper_trading.portfolio_manager import PortfolioManager
from src.features.signal_processing.signal_generator import SignalGenerator
from src.features.paper_trading.performance_tracker import PerformanceTracker
from src.features.data_pipeline.cache_manager import CacheManager
from src.features.paper_trading.trade_executor import TradeExecutor
from src.features.notifications.subscription_manager import SubscriptionManager

logger = structlog.get_logger(__name__)
security = HTTPBearer()

# Create router
router = APIRouter(prefix="/api/v1/web", tags=["Web Interface"])


# Helper functions
def serialize_datetime_objects(obj):
    """Recursively convert datetime objects to ISO strings"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: serialize_datetime_objects(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_datetime_objects(item) for item in obj]
    elif hasattr(obj, '__dict__'):
        # Handle Pydantic models and other objects with __dict__
        return serialize_datetime_objects(obj.__dict__)
    else:
        return obj


# Authentication Models
class LoginRequest(BaseModel):
    email: str = Field(..., description="User email")
    password: str = Field(..., description="User password")


class RegisterRequest(BaseModel):
    email: str = Field(..., description="User email")
    password: str = Field(..., description="User password")
    username: str = Field(..., description="Username")
    first_name: Optional[str] = Field(None, description="First name")
    last_name: Optional[str] = Field(None, description="Last name")


class AuthResponse(BaseModel):
    success: bool
    message: str
    user: Optional[Dict[str, Any]] = None
    token: Optional[Dict[str, Any]] = None


# Dependency providers
def get_portfolio_manager():
    """Get PortfolioManager instance"""
    return PortfolioManager()


def get_signal_generator():
    """Get SignalGenerator instance"""
    return SignalGenerator()


def get_performance_tracker():
    """Get PerformanceTracker instance"""
    return PerformanceTracker()


def get_cache_manager():
    """Get CacheManager instance"""
    return CacheManager()


def get_dashboard_service(
    portfolio_manager=Depends(get_portfolio_manager),
    signal_generator=Depends(get_signal_generator),
    performance_tracker=Depends(get_performance_tracker),
    cache_manager=Depends(get_cache_manager)
):
    """Get DashboardService instance with dependencies"""
    return DashboardService(
        portfolio_manager=portfolio_manager,
        signal_generator=signal_generator,
        performance_tracker=performance_tracker,
        cache_manager=cache_manager
    )


def get_user_manager():
    """Get UserManager instance"""
    return UserManager()


def get_subscription_manager():
    """Get SubscriptionManager instance"""
    return SubscriptionManager()


def get_auth_manager():
    """Get AuthManager instance"""
    return AuthManager()


def get_trade_executor():
    """Get TradeExecutor instance"""
    return TradeExecutor()


def get_user_interface_manager(
    user_manager=Depends(get_user_manager),
    subscription_manager=Depends(get_subscription_manager),
    auth_manager=Depends(get_auth_manager),
    cache_manager=Depends(get_cache_manager)
):
    """Get UserInterfaceManager instance with dependencies"""
    return UserInterfaceManager(
        user_manager=user_manager,
        subscription_manager=subscription_manager,
        auth_manager=auth_manager,
        cache_manager=cache_manager
    )


def get_chart_service(
    portfolio_manager=Depends(get_portfolio_manager),
    performance_tracker=Depends(get_performance_tracker),
    signal_generator=Depends(get_signal_generator),
    cache_manager=Depends(get_cache_manager)
):
    """Get ChartService instance with dependencies"""
    return ChartService(
        portfolio_manager=portfolio_manager,
        performance_tracker=performance_tracker,
        signal_generator=signal_generator,
        cache_manager=cache_manager
    )


def get_trade_history_service(
    portfolio_manager=Depends(get_portfolio_manager),
    trade_executor=Depends(get_trade_executor),
    cache_manager=Depends(get_cache_manager)
):
    """Get TradeHistoryService instance with dependencies"""
    return TradeHistoryService(
        portfolio_manager=portfolio_manager,
        trade_executor=trade_executor,
        cache_manager=cache_manager
    )


# Dependency to get current user
async def get_current_user(token: str = Depends(security)) -> str:
    """Get current user from JWT token"""
    auth_manager = AuthManager()  # This should be injected in production
    user_id = await auth_manager.verify_token(token.credentials)
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid authentication token")
    return user_id


# Test endpoint
@router.get("/test")
async def test_endpoint():
    """Simple test endpoint"""
    return {"status": "ok", "message": "API is working"}


# Simple test endpoint for auth
@router.get("/test-auth")
async def test_auth_endpoint():
    """Simple test auth endpoint"""
    return {"status": "ok", "message": "Auth API is working"}


# Authentication endpoints
@router.post("/user/login", response_model=Dict[str, Any])
async def login_user(request: LoginRequest, req: Request):
    """Login user"""
    try:
        auth_manager = AuthManager()
        result = await auth_manager.login_user(
            email=request.email,
            password=request.password,
            ip_address=req.client.host if req.client else "unknown"
        )

        if result["success"]:
            logger.info("User logged in successfully", email=request.email)
            # Structure response to match frontend expectations
            clean_user = serialize_datetime_objects(result.get("user"))
            return {
                "user": clean_user,
                "token": {
                    "access_token": result.get("access_token"),
                    "refresh_token": result.get("refresh_token"),
                    "token_type": result.get("token_type", "bearer"),
                    "expires_in": 3600  # 1 hour in seconds
                }
            }
        else:
            logger.warning("Login failed", email=request.email, error=result.get("error"))
            raise HTTPException(status_code=401, detail=result.get("error", "Login failed"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error logging in user", email=request.email, error=str(e))
        raise HTTPException(status_code=500, detail="Login failed")


@router.post("/user/register", response_model=Dict[str, Any])
async def register_user(request: RegisterRequest, req: Request):
    """Register a new user"""
    try:
        auth_manager = AuthManager()
        result = await auth_manager.register_user(
            email=request.email,
            password=request.password,
            username=request.username,
            role=UserRole.USER,  # Default role
            auto_login=True  # Auto-login after registration
        )

        if result["success"]:
            logger.info("User registered successfully", email=request.email)
            # Structure response to match frontend expectations (same as login)
            clean_user = serialize_datetime_objects(result.get("user"))
            return {
                "user": clean_user,
                "token": {
                    "access_token": result.get("access_token"),
                    "refresh_token": result.get("refresh_token"),
                    "token_type": result.get("token_type", "bearer"),
                    "expires_in": 3600  # 1 hour in seconds
                }
            }
        else:
            logger.warning("Registration failed", email=request.email, error=result.get("error"))
            raise HTTPException(status_code=400, detail=result.get("error", "Registration failed"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error registering user", email=request.email, error=str(e))
        raise HTTPException(status_code=500, detail="Registration failed")


@router.post("/user/refresh", response_model=Dict[str, Any])
async def refresh_token(request: Dict[str, str] = Body(...)):
    """Refresh authentication token"""
    try:
        refresh_token = request.get("refresh_token")
        if not refresh_token:
            raise HTTPException(status_code=400, detail="Refresh token required")

        auth_manager = AuthManager()
        result = await auth_manager.refresh_token(refresh_token)

        if result.get("success"):
            logger.info("Token refreshed successfully")
            return {
                "access_token": result.get("access_token"),
                "refresh_token": result.get("refresh_token"),
                "token_type": result.get("token_type", "bearer"),
                "expires_in": result.get("expires_in", 3600)
            }
        else:
            raise HTTPException(status_code=401, detail="Invalid refresh token")
    except Exception as e:
        logger.error("Token refresh failed", error=str(e))
        raise HTTPException(status_code=500, detail="Token refresh failed")


@router.post("/user/logout")
async def logout_user(current_user: str = Depends(get_current_user)):
    """Logout user"""
    try:
        auth_manager = AuthManager()
        result = await auth_manager.logout_user(current_user)

        if result.get("success"):
            logger.info("User logged out successfully", user_id=current_user)
            return {"status": "success", "message": "Logged out successfully"}
        else:
            raise HTTPException(status_code=500, detail="Logout failed")
    except Exception as e:
        logger.error("Logout failed", user_id=current_user, error=str(e))
        raise HTTPException(status_code=500, detail="Logout failed")


# Dashboard endpoints
@router.get("/dashboard")
async def get_dashboard():
    """
    Get comprehensive dashboard data for the current user.

    Returns:
        Complete dashboard data including portfolio, signals, and performance
    """
    return {"status": "ok", "message": "Dashboard endpoint working"}


# @router.get("/dashboard/realtime")
# async def get_realtime_updates(
#     current_user: str = Depends(get_current_user),
#     dashboard_service: DashboardService = Depends(get_dashboard_service)
# ):
#     """
#     Get real-time dashboard updates.
#
#     Returns:
#         Real-time update data
#     """
#     try:
#         updates = await dashboard_service.get_real_time_updates(current_user)
#         return updates
#     except Exception as e:
#         logger.error("Failed to get real-time updates", user_id=current_user, error=str(e))
#         raise HTTPException(status_code=500, detail="Failed to get real-time updates")


@router.post("/dashboard/refresh")
async def refresh_dashboard(
    current_user: str = Depends(get_current_user),
    dashboard_service: DashboardService = Depends(get_dashboard_service)
):
    """
    Refresh dashboard cache for the current user.

    Returns:
        Success status
    """
    try:
        success = await dashboard_service.invalidate_cache(current_user)
        if success:
            return {"status": "success", "message": "Dashboard cache refreshed"}
        else:
            raise HTTPException(status_code=500, detail="Failed to refresh dashboard")
    except Exception as e:
        logger.error("Failed to refresh dashboard", user_id=current_user, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to refresh dashboard")


# User interface endpoints
@router.get("/user")
async def get_user_profile(current_user: str = Depends(get_current_user)):
    """
    Get user profile data.

    Returns:
        User profile information
    """
    try:
        user_manager = UserManager()
        profile = await user_manager.get_user_profile(current_user)

        if profile:
            logger.info("User profile retrieved", user_id=current_user)
            return profile
        else:
            raise HTTPException(status_code=404, detail="User not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get user profile", user_id=current_user, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to load user profile")


@router.put("/user/profile")
async def update_user_profile(
    profile_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends(get_user_interface_manager)
):
    """
    Update user profile.
    
    Args:
        profile_data: Profile data to update
        
    Returns:
        Updated user profile
    """
    try:
        updated_profile = await ui_manager.update_user_profile(current_user, profile_data)
        logger.info("User profile updated", user_id=current_user)
        return updated_profile
    except Exception as e:
        logger.error("Failed to update profile", user_id=current_user, error=str(e))
        raise


@router.put("/user/preferences")
async def update_notification_preferences(
    preferences: NotificationPreferences = Body(...),
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends(get_user_interface_manager)
):
    """
    Update notification preferences.
    
    Args:
        preferences: New notification preferences
        
    Returns:
        Updated preferences
    """
    try:
        updated_preferences = await ui_manager.update_notification_preferences(
            current_user, preferences
        )
        logger.info("Notification preferences updated", user_id=current_user)
        return updated_preferences
    except Exception as e:
        logger.error("Failed to update preferences", user_id=current_user, error=str(e))
        raise


@router.post("/user/api-keys")
async def create_api_key(
    key_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends(get_user_interface_manager)
):
    """
    Create new API key.
    
    Args:
        key_data: API key creation data
        
    Returns:
        Created API key data
    """
    try:
        api_key = await ui_manager.create_api_key(
            user_id=current_user,
            key_name=key_data.get("name"),
            permissions=key_data.get("permissions", []),
            expires_in_days=key_data.get("expires_in_days")
        )
        logger.info("API key created", user_id=current_user, key_name=key_data.get("name"))
        return api_key
    except Exception as e:
        logger.error("Failed to create API key", user_id=current_user, error=str(e))
        raise


@router.delete("/user/api-keys/{key_id}")
async def revoke_api_key(
    key_id: str = Path(...),
    current_user: str = Depends(get_current_user),
    ui_manager: UserInterfaceManager = Depends(get_user_interface_manager)
):
    """
    Revoke an API key.
    
    Args:
        key_id: API key identifier
        
    Returns:
        Success status
    """
    try:
        success = await ui_manager.revoke_api_key(current_user, key_id)
        if success:
            logger.info("API key revoked", user_id=current_user, key_id=key_id)
            return {"status": "success", "message": "API key revoked"}
        else:
            raise HTTPException(status_code=404, detail="API key not found")
    except Exception as e:
        logger.error("Failed to revoke API key", user_id=current_user, key_id=key_id, error=str(e))
        raise


# Chart endpoints
@router.get("/charts/portfolio")
async def get_portfolio_chart(
    timeframe: TimeFrame = Query(TimeFrame.DAY),
    chart_type: ChartType = Query(ChartType.LINE),
    current_user: str = Depends(get_current_user),
    chart_service: ChartService = Depends(get_chart_service)
):
    """
    Get portfolio performance chart.
    
    Args:
        timeframe: Chart timeframe
        chart_type: Type of chart
        
    Returns:
        Portfolio performance chart data
    """
    try:
        chart = await chart_service.get_portfolio_chart(current_user, timeframe, chart_type)
        return chart
    except Exception as e:
        logger.error("Failed to get portfolio chart", user_id=current_user, error=str(e))
        raise


@router.get("/charts/signals")
async def get_signals_chart(
    timeframe: TimeFrame = Query(TimeFrame.DAY),
    signal_type: Optional[str] = Query(None),
    current_user: str = Depends(get_current_user),
    chart_service: ChartService = Depends(get_chart_service)
):
    """
    Get signals chart.
    
    Args:
        timeframe: Chart timeframe
        signal_type: Filter by signal type (optional)
        
    Returns:
        Signals chart data
    """
    try:
        chart = await chart_service.get_signals_chart(current_user, timeframe, signal_type)
        return chart
    except Exception as e:
        logger.error("Failed to get signals chart", user_id=current_user, error=str(e))
        raise


@router.get("/charts/trades")
async def get_trade_chart(
    timeframe: TimeFrame = Query(TimeFrame.DAY),
    current_user: str = Depends(get_current_user),
    chart_service: ChartService = Depends(get_chart_service)
):
    """
    Get trade history chart.
    
    Args:
        timeframe: Chart timeframe
        
    Returns:
        Trade history chart data
    """
    try:
        chart = await chart_service.get_trade_history_chart(current_user, timeframe)
        return chart
    except Exception as e:
        logger.error("Failed to get trade chart", user_id=current_user, error=str(e))
        raise


# Trade history endpoints
@router.get("/trades")
async def get_trade_history(
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=200),
    sort_by: str = Query("created_at"),
    sort_order: SortOrder = Query(SortOrder.DESC),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    token_address: Optional[str] = Query(None),
    trade_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    min_value: Optional[float] = Query(None),
    max_value: Optional[float] = Query(None),
    current_user: str = Depends(get_current_user),
    trade_service: TradeHistoryService = Depends(get_trade_history_service)
):
    """
    Get paginated trade history with filtering and sorting.
    
    Returns:
        Paginated trade history
    """
    try:
        # Create filter object
        filters = TradeFilter(
            start_date=start_date,
            end_date=end_date,
            token_address=token_address,
            trade_type=trade_type,
            status=status,
            min_value=min_value,
            max_value=max_value
        )
        
        trade_history = await trade_service.get_trade_history(
            user_id=current_user,
            filters=filters,
            page=page,
            page_size=page_size,
            sort_by=sort_by,
            sort_order=sort_order
        )
        return trade_history
    except Exception as e:
        logger.error("Failed to get trade history", user_id=current_user, error=str(e))
        raise


@router.get("/trades/{trade_id}")
async def get_trade_details(
    trade_id: str = Path(...),
    current_user: str = Depends(get_current_user),
    trade_service: TradeHistoryService = Depends(get_trade_history_service)
):
    """
    Get detailed information for a specific trade.
    
    Args:
        trade_id: Trade identifier
        
    Returns:
        Detailed trade information
    """
    try:
        trade_details = await trade_service.get_trade_details(current_user, trade_id)
        return trade_details
    except Exception as e:
        logger.error("Failed to get trade details", user_id=current_user, trade_id=trade_id, error=str(e))
        raise


@router.get("/trades/analytics")
async def get_trade_analytics(
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    current_user: str = Depends(get_current_user),
    trade_service: TradeHistoryService = Depends(get_trade_history_service)
):
    """
    Get comprehensive trade analytics.
    
    Args:
        start_date: Start date for analysis
        end_date: End date for analysis
        
    Returns:
        Trade analytics data
    """
    try:
        analytics = await trade_service.get_trade_analytics(current_user, start_date, end_date)
        return analytics
    except Exception as e:
        logger.error("Failed to get trade analytics", user_id=current_user, error=str(e))
        raise


@router.post("/trades/export")
async def export_trade_history(
    export_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    trade_service: TradeHistoryService = Depends(get_trade_history_service)
):
    """
    Export trade history in specified format.
    
    Args:
        export_data: Export configuration
        
    Returns:
        Export data with download content
    """
    try:
        # Create filter object from export data
        filters = None
        if export_data.get("filters"):
            filter_data = export_data["filters"]
            filters = TradeFilter(
                start_date=filter_data.get("start_date"),
                end_date=filter_data.get("end_date"),
                token_address=filter_data.get("token_address"),
                trade_type=filter_data.get("trade_type"),
                status=filter_data.get("status"),
                min_value=filter_data.get("min_value"),
                max_value=filter_data.get("max_value")
            )
        
        export_result = await trade_service.export_trade_history(
            user_id=current_user,
            filters=filters,
            format=export_data.get("format", "csv")
        )
        
        logger.info("Trade history exported", user_id=current_user, format=export_data.get("format"))
        
        # Return as file download
        return Response(
            content=export_result.content.encode('utf-8'),
            media_type=export_result.content_type,
            headers={
                "Content-Disposition": f"attachment; filename={export_result.filename}"
            }
        )
    except Exception as e:
        logger.error("Failed to export trade history", user_id=current_user, error=str(e))
        raise
