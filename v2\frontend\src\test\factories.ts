import { Portfolio, Position, Trade, TradingSignal } from '@/types';

// Portfolio factory
export const createMockPortfolio = (overrides: Partial<Portfolio> = {}): Portfolio => ({
  id: 'portfolio-1',
  name: 'Test Portfolio',
  description: 'A test portfolio for unit testing',
  total_value: 10000,
  total_invested: 8000,
  total_pnl: 2000,
  total_pnl_percentage: 25,
  daily_pnl: 150,
  daily_pnl_percentage: 1.5,
  positions_count: 5,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-07-24T12:00:00Z',
  is_active: true,
  risk_score: 6.5,
  diversification_score: 8.2,
  performance_score: 7.8,
  ...overrides,
});

// Position factory
export const createMockPosition = (overrides: Partial<Position> = {}): Position => ({
  id: 'position-1',
  portfolio_id: 'portfolio-1',
  token_address: 'So11111111111111111111111111111111111111112',
  token_symbol: 'SOL',
  token_name: 'Solana',
  quantity: 100,
  average_price: 80,
  current_price: 100,
  market_value: 10000,
  total_invested: 8000,
  pnl: 2000,
  pnl_percentage: 25,
  allocation_percentage: 50,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-07-24T12:00:00Z',
  ...overrides,
});

// Trade factory
export const createMockTrade = (overrides: Partial<Trade> = {}): Trade => ({
  id: 'trade-1',
  portfolio_id: 'portfolio-1',
  signal_id: 'signal-1',
  token_address: 'So11111111111111111111111111111111111111112',
  token_symbol: 'SOL',
  token_name: 'Solana',
  trade_type: 'buy',
  quantity: 10,
  price: 100,
  total_value: 1000,
  fees: 5,
  status: 'executed',
  executed_at: '2024-07-24T12:00:00Z',
  created_at: '2024-07-24T11:00:00Z',
  pnl: 50,
  pnl_percentage: 5,
  notes: 'Test trade',
  ...overrides,
});

// Trading Signal factory
export const createMockTradingSignal = (overrides: Partial<TradingSignal> = {}): TradingSignal => ({
  id: 'signal-1',
  token_address: 'So11111111111111111111111111111111111111112',
  token_symbol: 'SOL',
  token_name: 'Solana',
  signal_type: 'buy',
  confidence: 85,
  current_price: 100,
  target_price: 120,
  stop_loss: 90,
  expected_return: 20,
  risk_score: 6,
  analysis: 'Strong bullish momentum with high volume',
  created_at: '2024-07-24T10:00:00Z',
  expires_at: '2024-07-25T10:00:00Z',
  status: 'active',
  suggested_quantity: 10,
  ...overrides,
});

// Market Overview factory
export const createMockMarketOverview = (overrides: any = {}) => ({
  total_market_cap: 2500000000000,
  market_cap_change_24h: 2.5,
  total_volume_24h: 85000000000,
  volume_change_24h: -5.2,
  fear_greed_index: 65,
  btc_dominance: 42.5,
  eth_dominance: 18.3,
  active_cryptocurrencies: 2800,
  markets: 15000,
  market_status: 'bullish',
  top_gainers: [
    {
      symbol: 'SOL',
      price: 100,
      change_24h: 15.5,
      market_cap: 45000000000,
      volume_24h: 2500000000,
      rank: 5,
    },
    {
      symbol: 'ADA',
      price: 0.45,
      change_24h: 12.3,
      market_cap: 15000000000,
      volume_24h: 800000000,
      rank: 8,
    },
  ],
  top_losers: [
    {
      symbol: 'DOGE',
      price: 0.08,
      change_24h: -8.5,
      market_cap: 11000000000,
      volume_24h: 600000000,
      rank: 10,
    },
  ],
  ...overrides,
});

// Dashboard Analytics factory
export const createMockDashboardAnalytics = (overrides: any = {}) => ({
  summary: {
    total_portfolios: 3,
    total_value: 25000,
    total_pnl: 5000,
    total_pnl_percentage: 25,
    daily_pnl: 250,
    daily_pnl_percentage: 1.2,
    active_signals: 8,
    executed_trades_today: 5,
  },
  performance_chart: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
    datasets: [
      {
        label: 'Portfolio Value',
        data: [20000, 21000, 19500, 22000, 23500, 24000, 25000],
      },
    ],
  },
  top_performers: [
    { symbol: 'SOL', pnl_percentage: 45.2 },
    { symbol: 'ETH', pnl_percentage: 32.1 },
    { symbol: 'BTC', pnl_percentage: 18.5 },
  ],
  recent_trades: [
    createMockTrade({ id: 'trade-1', token_symbol: 'SOL' }),
    createMockTrade({ id: 'trade-2', token_symbol: 'ETH', trade_type: 'sell' }),
  ],
  ...overrides,
});

// Trade Analytics factory
export const createMockTradeAnalytics = (overrides: any = {}) => ({
  summary: {
    total_trades: 150,
    winning_trades: 95,
    losing_trades: 55,
    win_rate: 0.633,
    total_pnl: 5000,
    total_pnl_percentage: 25,
    average_win: 120,
    average_loss: -80,
    profit_factor: 1.5,
    largest_win: 500,
    largest_loss: -300,
  },
  performance_by_month: [
    { month: 'Jan', trades: 20, pnl: 800, win_rate: 0.65 },
    { month: 'Feb', trades: 25, pnl: 1200, win_rate: 0.68 },
    { month: 'Mar', trades: 18, pnl: -200, win_rate: 0.55 },
    { month: 'Apr', trades: 22, pnl: 900, win_rate: 0.64 },
    { month: 'May', trades: 28, pnl: 1100, win_rate: 0.61 },
    { month: 'Jun', trades: 24, pnl: 850, win_rate: 0.63 },
    { month: 'Jul', trades: 13, pnl: 350, win_rate: 0.62 },
  ],
  performance_by_token: [
    { token_symbol: 'SOL', trades: 45, pnl: 2200, win_rate: 0.71 },
    { token_symbol: 'ETH', trades: 38, pnl: 1800, win_rate: 0.66 },
    { token_symbol: 'BTC', trades: 32, pnl: 1000, win_rate: 0.59 },
    { token_symbol: 'ADA', trades: 25, pnl: 0, win_rate: 0.52 },
    { token_symbol: 'DOT', trades: 10, pnl: 0, win_rate: 0.50 },
  ],
  performance_by_signal_type: [
    { signal_type: 'buy', trades: 80, pnl: 3200, win_rate: 0.68 },
    { signal_type: 'sell', trades: 45, pnl: 1200, win_rate: 0.62 },
    { signal_type: 'manual', trades: 25, pnl: 600, win_rate: 0.56 },
  ],
  ...overrides,
});

// Signal Performance factory
export const createMockSignalPerformance = (overrides: any = {}) => ({
  total_signals: 200,
  successful_signals: 130,
  success_rate: 0.65,
  average_return: 8.5,
  best_signal: {
    signal_id: 'signal-best',
    return_percentage: 45.2,
    token_symbol: 'SOL',
  },
  worst_signal: {
    signal_id: 'signal-worst',
    return_percentage: -15.8,
    token_symbol: 'DOGE',
  },
  performance_by_type: [
    { signal_type: 'buy', count: 120, success_rate: 0.68, average_return: 12.3 },
    { signal_type: 'sell', count: 60, success_rate: 0.62, average_return: 8.1 },
    { signal_type: 'hold', count: 20, success_rate: 0.55, average_return: 2.5 },
  ],
  ...overrides,
});

// User Profile factory
export const createMockUserProfile = (overrides: any = {}) => ({
  id: 'user-1',
  first_name: 'John',
  last_name: 'Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  location: 'New York, NY',
  bio: 'Crypto trader and blockchain enthusiast',
  timezone: 'America/New_York',
  language: 'en',
  avatar_url: null,
  created_at: '2024-01-01T00:00:00Z',
  last_login: '2024-07-24T12:00:00Z',
  ...overrides,
});

// API Response factories
export const createMockApiResponse = <T>(data: T, overrides: any = {}) => ({
  success: true,
  data,
  message: 'Success',
  timestamp: new Date().toISOString(),
  ...overrides,
});

export const createMockPaginatedResponse = <T>(items: T[], overrides: any = {}) => ({
  items,
  total: items.length,
  page: 1,
  page_size: 20,
  total_pages: Math.ceil(items.length / 20),
  has_next: false,
  has_previous: false,
  ...overrides,
});

// Error response factory
export const createMockErrorResponse = (message = 'An error occurred', code = 'UNKNOWN_ERROR') => ({
  success: false,
  error: {
    code,
    message,
    details: {},
  },
  timestamp: new Date().toISOString(),
});

// Batch factories for creating multiple items
export const createMockPortfolios = (count = 3): Portfolio[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockPortfolio({
      id: `portfolio-${index + 1}`,
      name: `Portfolio ${index + 1}`,
      total_value: 10000 + index * 5000,
    })
  );
};

export const createMockPositions = (count = 5, portfolioId = 'portfolio-1'): Position[] => {
  const tokens = ['SOL', 'ETH', 'BTC', 'ADA', 'DOT'];
  return Array.from({ length: count }, (_, index) =>
    createMockPosition({
      id: `position-${index + 1}`,
      portfolio_id: portfolioId,
      token_symbol: tokens[index] || `TOKEN${index + 1}`,
      quantity: 100 + index * 50,
      current_price: 50 + index * 25,
    })
  );
};

export const createMockTrades = (count = 10): Trade[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockTrade({
      id: `trade-${index + 1}`,
      trade_type: index % 2 === 0 ? 'buy' : 'sell',
      quantity: 10 + index * 5,
      price: 100 + index * 10,
    })
  );
};

export const createMockTradingSignals = (count = 5): TradingSignal[] => {
  const signalTypes: ('buy' | 'sell' | 'hold')[] = ['buy', 'sell', 'hold'];
  return Array.from({ length: count }, (_, index) =>
    createMockTradingSignal({
      id: `signal-${index + 1}`,
      signal_type: signalTypes[index % 3],
      confidence: 70 + index * 5,
      current_price: 100 + index * 20,
    })
  );
};
