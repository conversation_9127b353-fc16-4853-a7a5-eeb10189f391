import React, { useState } from 'react';
import { Plus, Zap, RefreshCw } from 'lucide-react';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';

/**
 * Quick actions component with common dashboard actions
 */
const QuickActions: React.FC = () => {
  const [showCreatePortfolio, setShowCreatePortfolio] = useState(false);
  const [showGenerateSignal, setShowGenerateSignal] = useState(false);

  const handleRefresh = () => {
    // Trigger a refresh of dashboard data
    window.location.reload();
  };

  return (
    <>
      <div className="flex items-center space-x-3">
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          leftIcon={<RefreshCw className="w-4 h-4" />}
        >
          Refresh
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowGenerateSignal(true)}
          leftIcon={<Zap className="w-4 h-4" />}
        >
          Generate Signal
        </Button>
        
        <Button
          variant="primary"
          size="sm"
          onClick={() => setShowCreatePortfolio(true)}
          leftIcon={<Plus className="w-4 h-4" />}
        >
          New Portfolio
        </Button>
      </div>

      {/* Create Portfolio Modal */}
      <Modal
        isOpen={showCreatePortfolio}
        onClose={() => setShowCreatePortfolio(false)}
        title="Create New Portfolio"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-text-secondary">
            Create a new portfolio to start tracking your trading performance.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowCreatePortfolio(false)}
            >
              Cancel
            </Button>
            <Button variant="primary">
              Create Portfolio
            </Button>
          </div>
        </div>
      </Modal>

      {/* Generate Signal Modal */}
      <Modal
        isOpen={showGenerateSignal}
        onClose={() => setShowGenerateSignal(false)}
        title="Generate Trading Signal"
        size="md"
      >
        <div className="space-y-4">
          <p className="text-text-secondary">
            Generate a new trading signal based on current market conditions.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowGenerateSignal(false)}
            >
              Cancel
            </Button>
            <Button variant="primary">
              Generate Signal
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default QuickActions;
