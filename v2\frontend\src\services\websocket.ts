import { io, Socket } from 'socket.io-client';
import { TokenManager } from './api';
import {
  // WebSocketMessage,
  PortfolioUpdate,
  SignalUpdate,
  TradeUpdate,
  Notification,
} from '@/types';

/**
 * WebSocket service for real-time updates
 */
export class WebSocketService {
  private static instance: WebSocketService;
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  /**
   * Connect to WebSocket server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        return;
      }

      this.isConnecting = true;
      const token = TokenManager.getAccessToken();

      if (!token) {
        this.isConnecting = false;
        reject(new Error('No authentication token available'));
        return;
      }

      const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:8000';

      this.socket = io(wsUrl, {
        auth: { token },
        transports: ['websocket'],
        timeout: 10000,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      });

      this.setupEventHandlers();

      this.socket.on('connect', () => {
        console.log('WebSocket connected');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.isConnecting = false;
        this.handleReconnect();
        reject(error);
      });
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventListeners.clear();
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * Subscribe to portfolio updates
   */
  subscribeToPortfolioUpdates(
    portfolioId: string,
    callback: (update: PortfolioUpdate) => void
  ): void {
    this.subscribe(`portfolio_update_${portfolioId}`, callback);
    this.socket?.emit('subscribe_portfolio', { portfolio_id: portfolioId });
  }

  /**
   * Unsubscribe from portfolio updates
   */
  unsubscribeFromPortfolioUpdates(portfolioId: string): void {
    this.unsubscribe(`portfolio_update_${portfolioId}`);
    this.socket?.emit('unsubscribe_portfolio', { portfolio_id: portfolioId });
  }

  /**
   * Subscribe to signal updates
   */
  subscribeToSignalUpdates(callback: (update: SignalUpdate) => void): void {
    this.subscribe('signal_update', callback);
    this.socket?.emit('subscribe_signals');
  }

  /**
   * Unsubscribe from signal updates
   */
  unsubscribeFromSignalUpdates(): void {
    this.unsubscribe('signal_update');
    this.socket?.emit('unsubscribe_signals');
  }

  /**
   * Subscribe to trade updates
   */
  subscribeToTradeUpdates(callback: (update: TradeUpdate) => void): void {
    this.subscribe('trade_update', callback);
    this.socket?.emit('subscribe_trades');
  }

  /**
   * Unsubscribe from trade updates
   */
  unsubscribeFromTradeUpdates(): void {
    this.unsubscribe('trade_update');
    this.socket?.emit('unsubscribe_trades');
  }

  /**
   * Subscribe to notifications
   */
  subscribeToNotifications(callback: (notification: Notification) => void): void {
    this.subscribe('notification', callback);
    this.socket?.emit('subscribe_notifications');
  }

  /**
   * Unsubscribe from notifications
   */
  unsubscribeFromNotifications(): void {
    this.unsubscribe('notification');
    this.socket?.emit('unsubscribe_notifications');
  }

  /**
   * Subscribe to market data updates
   */
  subscribeToMarketData(
    tokenAddress: string,
    callback: (data: { price: number; volume: number; timestamp: string }) => void
  ): void {
    this.subscribe(`market_data_${tokenAddress}`, callback);
    this.socket?.emit('subscribe_market_data', { token_address: tokenAddress });
  }

  /**
   * Unsubscribe from market data updates
   */
  unsubscribeFromMarketData(tokenAddress: string): void {
    this.unsubscribe(`market_data_${tokenAddress}`);
    this.socket?.emit('unsubscribe_market_data', { token_address: tokenAddress });
  }

  /**
   * Generic subscribe method
   */
  private subscribe(event: string, callback: (data: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);

    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  /**
   * Generic unsubscribe method
   */
  private unsubscribe(event: string, callback?: (data: any) => void): void {
    if (callback) {
      this.eventListeners.get(event)?.delete(callback);
      this.socket?.off(event, callback);
    } else {
      this.eventListeners.delete(event);
      this.socket?.off(event);
    }
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log(`WebSocket reconnected after ${attemptNumber} attempts`);
      this.reconnectAttempts = 0;
      this.resubscribeToEvents();
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('WebSocket reconnection error:', error);
    });

    this.socket.on('reconnect_failed', () => {
      console.error('WebSocket reconnection failed');
    });
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    setTimeout(() => {
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.connect().catch((error) => {
        console.error('Reconnection attempt failed:', error);
      });
    }, delay);
  }

  /**
   * Resubscribe to all events after reconnection
   */
  private resubscribeToEvents(): void {
    for (const [event, callbacks] of this.eventListeners) {
      for (const callback of callbacks) {
        this.socket?.on(event, callback);
      }
    }
  }

  /**
   * Send message to server
   */
  emit(event: string, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket not connected, cannot emit event:', event);
    }
  }
}

// Export singleton instance
export const websocketService = WebSocketService.getInstance();
