import React, { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { SignalsService } from '@/services/signals';

interface BacktestModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const BacktestModal: React.FC<BacktestModalProps> = ({ isOpen, onClose }) => {
  const [step, setStep] = useState<'configure' | 'running' | 'results'>('configure');
  const [formData, setFormData] = useState({
    timeframe: '3M' as '1D' | '1W' | '1M' | '3M' | '6M' | '1Y',
    initial_balance: 10000,
    strategy_config: {
      technical_indicators: {
        rsi_period: 14,
        rsi_overbought: 70,
        rsi_oversold: 30,
        macd_fast: 12,
        macd_slow: 26,
        macd_signal: 9,
      },
      risk_parameters: {
        max_risk_per_trade: 2,
        stop_loss_percentage: 5,
        take_profit_ratio: 2,
        max_open_positions: 5,
      },
    },
    tokens: [] as string[],
  });

  const [backtestResults, setBacktestResults] = useState<any>(null);

  // Backtest mutation
  const backtestMutation = useMutation({
    mutationFn: SignalsService.backtestStrategy,
    onSuccess: (data) => {
      setBacktestResults(data);
      setStep('results');
    },
    onError: () => {
      setStep('configure');
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setStep('running');
    backtestMutation.mutate(formData);
  };

  const handleClose = () => {
    if (step !== 'running') {
      setStep('configure');
      setBacktestResults(null);
      onClose();
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleStrategyChange = (section: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      strategy_config: {
        ...prev.strategy_config,
        [section]: {
          ...prev.strategy_config[section as keyof typeof prev.strategy_config],
          [field]: value,
        },
      },
    }));
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Backtest Strategy" size="lg">
      {step === 'configure' && (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Timeframe
              </label>
              <select
                value={formData.timeframe}
                onChange={(e) => handleInputChange('timeframe', e.target.value)}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
              >
                <option value="1D">1 Day</option>
                <option value="1W">1 Week</option>
                <option value="1M">1 Month</option>
                <option value="3M">3 Months</option>
                <option value="6M">6 Months</option>
                <option value="1Y">1 Year</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Initial Balance (USD)
              </label>
              <Input
                type="number"
                value={formData.initial_balance}
                onChange={(e) => handleInputChange('initial_balance', parseFloat(e.target.value) || 0)}
                min="1000"
                step="100"
              />
            </div>
          </div>

          {/* Technical Indicators */}
          <div>
            <h4 className="text-md font-medium text-text-primary mb-4">Technical Indicators</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm text-text-secondary mb-1">RSI Period</label>
                <Input
                  type="number"
                  value={formData.strategy_config.technical_indicators.rsi_period}
                  onChange={(e) => handleStrategyChange('technical_indicators', 'rsi_period', parseInt(e.target.value))}
                  min="1"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm text-text-secondary mb-1">RSI Overbought</label>
                <Input
                  type="number"
                  value={formData.strategy_config.technical_indicators.rsi_overbought}
                  onChange={(e) => handleStrategyChange('technical_indicators', 'rsi_overbought', parseInt(e.target.value))}
                  min="50"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm text-text-secondary mb-1">RSI Oversold</label>
                <Input
                  type="number"
                  value={formData.strategy_config.technical_indicators.rsi_oversold}
                  onChange={(e) => handleStrategyChange('technical_indicators', 'rsi_oversold', parseInt(e.target.value))}
                  min="0"
                  max="50"
                />
              </div>
              <div>
                <label className="block text-sm text-text-secondary mb-1">MACD Fast</label>
                <Input
                  type="number"
                  value={formData.strategy_config.technical_indicators.macd_fast}
                  onChange={(e) => handleStrategyChange('technical_indicators', 'macd_fast', parseInt(e.target.value))}
                  min="1"
                  max="50"
                />
              </div>
              <div>
                <label className="block text-sm text-text-secondary mb-1">MACD Slow</label>
                <Input
                  type="number"
                  value={formData.strategy_config.technical_indicators.macd_slow}
                  onChange={(e) => handleStrategyChange('technical_indicators', 'macd_slow', parseInt(e.target.value))}
                  min="1"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm text-text-secondary mb-1">MACD Signal</label>
                <Input
                  type="number"
                  value={formData.strategy_config.technical_indicators.macd_signal}
                  onChange={(e) => handleStrategyChange('technical_indicators', 'macd_signal', parseInt(e.target.value))}
                  min="1"
                  max="50"
                />
              </div>
            </div>
          </div>

          {/* Risk Parameters */}
          <div>
            <h4 className="text-md font-medium text-text-primary mb-4">Risk Parameters</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-text-secondary mb-1">Max Risk Per Trade (%)</label>
                <Input
                  type="number"
                  step="0.1"
                  value={formData.strategy_config.risk_parameters.max_risk_per_trade}
                  onChange={(e) => handleStrategyChange('risk_parameters', 'max_risk_per_trade', parseFloat(e.target.value))}
                  min="0.1"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm text-text-secondary mb-1">Stop Loss (%)</label>
                <Input
                  type="number"
                  step="0.1"
                  value={formData.strategy_config.risk_parameters.stop_loss_percentage}
                  onChange={(e) => handleStrategyChange('risk_parameters', 'stop_loss_percentage', parseFloat(e.target.value))}
                  min="0.1"
                  max="50"
                />
              </div>
              <div>
                <label className="block text-sm text-text-secondary mb-1">Take Profit Ratio</label>
                <Input
                  type="number"
                  step="0.1"
                  value={formData.strategy_config.risk_parameters.take_profit_ratio}
                  onChange={(e) => handleStrategyChange('risk_parameters', 'take_profit_ratio', parseFloat(e.target.value))}
                  min="0.1"
                  max="10"
                />
              </div>
              <div>
                <label className="block text-sm text-text-secondary mb-1">Max Open Positions</label>
                <Input
                  type="number"
                  value={formData.strategy_config.risk_parameters.max_open_positions}
                  onChange={(e) => handleStrategyChange('risk_parameters', 'max_open_positions', parseInt(e.target.value))}
                  min="1"
                  max="100"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-primary hover:bg-primary-dark">
              Start Backtest
            </Button>
          </div>
        </form>
      )}

      {step === 'running' && (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-6"></div>
          <h3 className="text-xl font-semibold text-text-primary mb-2">Running Backtest</h3>
          <p className="text-text-secondary mb-4">
            Analyzing historical data and simulating trades...
          </p>
          <div className="text-sm text-text-muted">
            This may take a few minutes depending on the timeframe selected.
          </div>
        </div>
      )}

      {step === 'results' && backtestResults && (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold text-text-primary mb-4">Backtest Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="p-4 bg-surface-secondary rounded-lg">
                <div className="text-sm text-text-secondary mb-1">Total Return</div>
                <div
                  className={`text-xl font-bold ${
                    backtestResults.results.total_return >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {formatCurrency(backtestResults.results.total_return)}
                </div>
                <div
                  className={`text-sm ${
                    backtestResults.results.total_return_percentage >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {formatPercentage(backtestResults.results.total_return_percentage)}
                </div>
              </div>

              <div className="p-4 bg-surface-secondary rounded-lg">
                <div className="text-sm text-text-secondary mb-1">Max Drawdown</div>
                <div className="text-xl font-bold text-red-600">
                  {formatPercentage(backtestResults.results.max_drawdown)}
                </div>
              </div>

              <div className="p-4 bg-surface-secondary rounded-lg">
                <div className="text-sm text-text-secondary mb-1">Sharpe Ratio</div>
                <div className="text-xl font-bold text-text-primary">
                  {backtestResults.results.sharpe_ratio.toFixed(2)}
                </div>
              </div>

              <div className="p-4 bg-surface-secondary rounded-lg">
                <div className="text-sm text-text-secondary mb-1">Win Rate</div>
                <div className="text-xl font-bold text-green-600">
                  {formatPercentage(backtestResults.results.win_rate)}
                </div>
              </div>

              <div className="p-4 bg-surface-secondary rounded-lg">
                <div className="text-sm text-text-secondary mb-1">Total Trades</div>
                <div className="text-xl font-bold text-text-primary">
                  {backtestResults.results.total_trades}
                </div>
                <div className="text-sm text-text-muted">
                  {backtestResults.results.profitable_trades} profitable
                </div>
              </div>
            </div>
          </div>

          {/* Trade History Sample */}
          {backtestResults.trades && backtestResults.trades.length > 0 && (
            <div>
              <h4 className="text-md font-medium text-text-primary mb-4">Recent Trades (Sample)</h4>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-surface-tertiary">
                    <tr>
                      <th className="px-4 py-2 text-left">Date</th>
                      <th className="px-4 py-2 text-left">Token</th>
                      <th className="px-4 py-2 text-left">Action</th>
                      <th className="px-4 py-2 text-right">Price</th>
                      <th className="px-4 py-2 text-right">Quantity</th>
                      <th className="px-4 py-2 text-right">P&L</th>
                    </tr>
                  </thead>
                  <tbody>
                    {backtestResults.trades.slice(0, 10).map((trade: any, index: number) => (
                      <tr key={index} className="border-b border-surface-tertiary">
                        <td className="px-4 py-2">{new Date(trade.date).toLocaleDateString()}</td>
                        <td className="px-4 py-2">{trade.token_symbol}</td>
                        <td className="px-4 py-2">
                          <span
                            className={`px-2 py-1 rounded text-xs ${
                              trade.action === 'buy'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {trade.action.toUpperCase()}
                          </span>
                        </td>
                        <td className="px-4 py-2 text-right">{formatCurrency(trade.price)}</td>
                        <td className="px-4 py-2 text-right">{trade.quantity.toLocaleString()}</td>
                        <td
                          className={`px-4 py-2 text-right font-medium ${
                            trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}
                        >
                          {formatCurrency(trade.pnl)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => {
                setStep('configure');
                setBacktestResults(null);
              }}
            >
              Run Another Backtest
            </Button>
            <Button onClick={handleClose} className="bg-primary hover:bg-primary-dark">
              Close
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default BacktestModal;
