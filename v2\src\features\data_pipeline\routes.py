"""
🔄 Data Pipeline API Routes

FastAPI routes for data pipeline operations including data aggregation,
cache management, and external data source integration.
"""

from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Query, Body
from pydantic import BaseModel, Field

from ...config.logging_config import get_logger
from .data_aggregator import DataAggregator
from .cache_manager import CacheManager
from .advanced_cache_manager import AdvancedCacheManager
from .data_validator import DataValidator
from .dune_client import DuneClient
from .jupiter_client import JupiterClient
from .raydium_client import RaydiumClient
from .solana_client import SolanaClient

logger = get_logger(__name__)
router = APIRouter()

# Initialize data pipeline components
data_aggregator = DataAggregator()
cache_manager = CacheManager()
advanced_cache_manager = AdvancedCacheManager()
data_validator = DataValidator()
dune_client = DuneClient()
jupiter_client = JupiterClient()
raydium_client = RaydiumClient()
solana_client = SolanaClient()


# Request/Response Models
class TokenDataRequest(BaseModel):
    token_address: str = Field(..., description="Token address")
    timeframe: Optional[str] = Field("1h", description="Data timeframe")
    limit: Optional[int] = Field(100, description="Number of data points")


class AggregateDataRequest(BaseModel):
    token_addresses: List[str] = Field(..., description="List of token addresses")
    metrics: List[str] = Field(..., description="Metrics to aggregate")
    timeframe: str = Field("1h", description="Data timeframe")


class CacheRequest(BaseModel):
    key: str = Field(..., description="Cache key")
    value: Optional[Any] = Field(None, description="Value to cache")
    ttl: Optional[int] = Field(3600, description="Time to live in seconds")


# Data Aggregation Routes
@router.get("/aggregate/{token_address}", response_model=Dict[str, Any])
async def get_aggregated_data(
    token_address: str,
    timeframe: str = Query("1h", description="Data timeframe"),
    limit: int = Query(100, description="Number of data points")
):
    """Get aggregated data for a token"""
    try:
        data = await data_aggregator.get_aggregated_token_data(
            token_address=token_address,
            force_refresh=False
        )
        
        return {"success": True, "data": data}
        
    except Exception as e:
        logger.error(f"Error getting aggregated data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/aggregate/batch", response_model=Dict[str, Any])
async def aggregate_batch_data(request: AggregateDataRequest):
    """Aggregate data for multiple tokens"""
    try:
        data = await data_aggregator.get_multiple_aggregated_data(
            token_addresses=request.token_addresses,
            force_refresh=False
        )
        
        return {"success": True, "aggregated_data": data}
        
    except Exception as e:
        logger.error(f"Error aggregating batch data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/aggregate/market-overview", response_model=Dict[str, Any])
async def get_market_overview():
    """Get aggregated market overview data"""
    try:
        overview = await data_aggregator.get_market_overview()
        return {"success": True, "market_overview": overview}
        
    except Exception as e:
        logger.error(f"Error getting market overview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Cache Management Routes
@router.get("/cache/{key}", response_model=Dict[str, Any])
async def get_cache_value(key: str):
    """Get value from cache"""
    try:
        value = await cache_manager.get(key)
        return {"success": True, "value": value, "found": value is not None}
        
    except Exception as e:
        logger.error(f"Error getting cache value: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/cache", response_model=Dict[str, Any])
async def set_cache_value(request: CacheRequest):
    """Set value in cache"""
    try:
        await cache_manager.set(request.key, request.value, request.ttl)
        return {"success": True, "message": "Value cached successfully"}
        
    except Exception as e:
        logger.error(f"Error setting cache value: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cache/{key}", response_model=Dict[str, Any])
async def delete_cache_value(key: str):
    """Delete value from cache"""
    try:
        await cache_manager.delete(key)
        return {"success": True, "message": "Value deleted from cache"}
        
    except Exception as e:
        logger.error(f"Error deleting cache value: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/cache/stats", response_model=Dict[str, Any])
async def get_cache_stats():
    """Get cache statistics"""
    try:
        stats = await cache_manager.get_stats()
        return {"success": True, "cache_stats": stats}
        
    except Exception as e:
        logger.error(f"Error getting cache stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Data Validation Routes
@router.post("/validate/token-data", response_model=Dict[str, Any])
async def validate_token_data(data: Dict[str, Any] = Body(...)):
    """Validate token data"""
    try:
        validation_result = await data_validator.validate_token_data(data)
        return {"success": True, "validation_result": validation_result}
        
    except Exception as e:
        logger.error(f"Error validating token data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate/price-data", response_model=Dict[str, Any])
async def validate_price_data(data: Dict[str, Any] = Body(...)):
    """Validate price data"""
    try:
        validation_result = await data_validator.validate_price_data(data)
        return {"success": True, "validation_result": validation_result}
        
    except Exception as e:
        logger.error(f"Error validating price data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# External Data Source Routes
@router.get("/dune/query/{query_id}", response_model=Dict[str, Any])
async def execute_dune_query(query_id: str):
    """Execute a Dune Analytics query"""
    try:
        result = await dune_client.execute_query(query_id)
        return {"success": True, "query_result": result}
        
    except Exception as e:
        logger.error(f"Error executing Dune query: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jupiter/quote/{input_mint}/{output_mint}", response_model=Dict[str, Any])
async def get_jupiter_quote(
    input_mint: str,
    output_mint: str,
    amount: int = Query(..., description="Amount in smallest unit")
):
    """Get quote from Jupiter"""
    try:
        quote = await jupiter_client.get_quote(input_mint, output_mint, amount)
        return {"success": True, "quote": quote}
        
    except Exception as e:
        logger.error(f"Error getting Jupiter quote: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/raydium/pools", response_model=Dict[str, Any])
async def get_raydium_pools():
    """Get Raydium pools data"""
    try:
        pools = await raydium_client.get_pools()
        return {"success": True, "pools": pools}
        
    except Exception as e:
        logger.error(f"Error getting Raydium pools: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/solana/token/{token_address}", response_model=Dict[str, Any])
async def get_solana_token_info(token_address: str):
    """Get Solana token information"""
    try:
        token_info = await solana_client.get_token_info(token_address)
        return {"success": True, "token_info": token_info}
        
    except Exception as e:
        logger.error(f"Error getting Solana token info: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# Health Check Routes
@router.get("/health", response_model=Dict[str, Any])
async def health_check():
    """Health check for data pipeline"""
    try:
        health_status = {
            "cache_manager": await cache_manager.health_check(),
            "data_aggregator": await data_aggregator.health_check(),
            "dune_client": await dune_client.health_check(),
            "jupiter_client": await jupiter_client.health_check(),
            "raydium_client": await raydium_client.health_check(),
            "solana_client": await solana_client.health_check(),
        }
        
        all_healthy = all(status.get("healthy", False) for status in health_status.values())
        
        return {
            "success": True,
            "healthy": all_healthy,
            "components": health_status,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in health check: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status", response_model=Dict[str, Any])
async def get_pipeline_status():
    """Get detailed status of data pipeline"""
    try:
        status = await data_aggregator.get_pipeline_status()
        return {"success": True, "pipeline_status": status}
        
    except Exception as e:
        logger.error(f"Error getting pipeline status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
