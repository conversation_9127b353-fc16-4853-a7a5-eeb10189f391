import { ApiService } from './api';
import {
  User,
  LoginCredentials,
  RegisterCredentials,
  AuthResponse,
  AuthToken,
} from '@/types';

/**
 * Authentication service for handling user authentication operations
 */
export class AuthService {
  private static readonly BASE_URL = '/api/v1/web/user';

  /**
   * Login user with email and password
   */
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return ApiService.post<AuthResponse>(`${this.BASE_URL}/login`, credentials);
  }

  /**
   * Register new user account
   */
  static async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    return ApiService.post<AuthResponse>(`${this.BASE_URL}/register`, credentials);
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(refreshToken: string): Promise<AuthToken> {
    return ApiService.post<AuthToken>(`${this.BASE_URL}/refresh`, {
      refresh_token: refreshToken,
    });
  }

  /**
   * Get current user profile
   */
  static async getProfile(): Promise<User> {
    return ApiService.get<User>(`${this.BASE_URL}`);
  }

  /**
   * Update user profile
   */
  static async updateProfile(userData: Partial<User>): Promise<User> {
    return ApiService.put<User>(`${this.BASE_URL}`, userData);
  }

  /**
   * Change user password
   */
  static async changePassword(data: {
    current_password: string;
    new_password: string;
  }): Promise<void> {
    return ApiService.post<void>(`${this.BASE_URL}/change-password`, data);
  }

  /**
   * Logout user
   */
  static async logout(): Promise<void> {
    return ApiService.post<void>(`${this.BASE_URL}/logout`);
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<void> {
    return ApiService.post<void>(`${this.BASE_URL}/password-reset`, { email });
  }

  /**
   * Reset password with token
   */
  static async resetPassword(data: {
    token: string;
    new_password: string;
  }): Promise<void> {
    return ApiService.post<void>(`${this.BASE_URL}/password-reset/confirm`, data);
  }

  /**
   * Verify email address
   */
  static async verifyEmail(token: string): Promise<void> {
    return ApiService.post<void>(`${this.BASE_URL}/verify-email`, { token });
  }

  /**
   * Resend email verification
   */
  static async resendVerification(): Promise<void> {
    return ApiService.post<void>(`${this.BASE_URL}/resend-verification`);
  }
}
