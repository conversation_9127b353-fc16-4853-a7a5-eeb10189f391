import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

import { TradesService } from '@/services/trades';
import { Trade } from '@/types';
import TradeHistory from './components/TradeHistory';
import TradeAnalytics from './components/TradeAnalytics';
import TradeDetails from './components/TradeDetails';
import CreateTradeModal from './components/CreateTradeModal';
import ExportModal from './components/ExportModal';
import TradeComparison from './components/TradeComparison';

/**
 * Trades page component
 */
const Trades: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'history' | 'analytics' | 'comparison'>('history');
  const [selectedTrade, setSelectedTrade] = useState<Trade | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [showTradeDetails, setShowTradeDetails] = useState(false);

  const queryClient = useQueryClient();

  // Fetch trade analytics
  const { data: analyticsData, isLoading: analyticsLoading } = useQuery({
    queryKey: ['trades', 'analytics'],
    queryFn: () => TradesService.getTradeAnalytics(),
  });

  // Fetch trade recommendations
  const { data: recommendationsData } = useQuery({
    queryKey: ['trades', 'recommendations'],
    queryFn: () => TradesService.getTradeRecommendations(),
  });

  // Create manual trade mutation
  const createTradeMutation = useMutation({
    mutationFn: TradesService.createManualTrade,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trades'] });
      setShowCreateModal(false);
    },
  });

  const handleCreateTrade = (tradeData: any) => {
    createTradeMutation.mutate(tradeData);
  };

  const handleTradeSelect = (trade: Trade) => {
    setSelectedTrade(trade);
    setShowTradeDetails(true);
  };

  const handleExport = async (exportParams: any) => {
    try {
      const blob = await TradesService.exportTrades(exportParams);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `trades_${new Date().toISOString().split('T')[0]}.${exportParams.format}`;
      a.click();
      window.URL.revokeObjectURL(url);
      setShowExportModal(false);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-text-primary">Trade History</h1>
          <p className="text-text-secondary mt-1">
            View your trading history and analytics.
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowExportModal(true)}
          >
            Export Data
          </Button>
          <Button
            onClick={() => setShowCreateModal(true)}
            className="bg-primary hover:bg-primary-dark"
          >
            Add Manual Trade
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {analyticsData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Total Trades</div>
            <div className="text-2xl font-bold text-text-primary">
              {analyticsData.summary.total_trades}
            </div>
            <div className="text-sm text-text-muted">
              {analyticsData.summary.winning_trades}W / {analyticsData.summary.losing_trades}L
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Win Rate</div>
            <div className="text-2xl font-bold text-green-600">
              {(analyticsData.summary.win_rate * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-text-muted">Success rate</div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Total P&L</div>
            <div
              className={`text-2xl font-bold ${
                analyticsData.summary.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2,
              }).format(analyticsData.summary.total_pnl)}
            </div>
            <div
              className={`text-sm ${
                analyticsData.summary.total_pnl_percentage >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {analyticsData.summary.total_pnl_percentage >= 0 ? '+' : ''}{analyticsData.summary.total_pnl_percentage.toFixed(2)}%
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Profit Factor</div>
            <div className="text-2xl font-bold text-text-primary">
              {analyticsData.summary.profit_factor.toFixed(2)}
            </div>
            <div className="text-sm text-text-muted">
              Avg Win: {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
              }).format(analyticsData.summary.average_win)}
            </div>
          </Card>
        </div>
      )}

      {/* Recommendations */}
      {recommendationsData && recommendationsData.recommendations.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Trading Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {recommendationsData.recommendations.slice(0, 4).map((rec, index) => (
              <div key={index} className="p-4 bg-surface-secondary rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-text-primary">{rec.title}</h4>
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      rec.impact === 'high'
                        ? 'bg-red-100 text-red-800'
                        : rec.impact === 'medium'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}
                  >
                    {rec.impact.toUpperCase()}
                  </span>
                </div>
                <p className="text-sm text-text-secondary mb-2">{rec.description}</p>
                <div className="text-xs text-text-muted">
                  Confidence: {rec.confidence.toFixed(1)}%
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1">
        {[
          { key: 'history', label: 'Trade History' },
          { key: 'analytics', label: 'Analytics' },
          { key: 'comparison', label: 'Comparison' },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-primary text-white'
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-tertiary'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'history' && (
        <TradeHistory onTradeSelect={handleTradeSelect} />
      )}

      {activeTab === 'analytics' && (
        <TradeAnalytics
          analyticsData={analyticsData || null}
          isLoading={analyticsLoading}
        />
      )}

      {activeTab === 'comparison' && (
        <TradeComparison />
      )}

      {/* Modals */}
      <CreateTradeModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateTrade}
        isLoading={createTradeMutation.isPending}
      />

      <ExportModal
        isOpen={showExportModal}
        onClose={() => setShowExportModal(false)}
        onExport={handleExport}
      />

      {selectedTrade && (
        <TradeDetails
          isOpen={showTradeDetails}
          onClose={() => setShowTradeDetails(false)}
          trade={selectedTrade}
        />
      )}
    </div>
  );
};

export default Trades;
