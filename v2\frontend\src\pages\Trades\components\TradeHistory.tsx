import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { Trade } from '@/types';
import { TradesService } from '@/services/trades';

interface TradeHistoryProps {
  onTradeSelect: (trade: Trade) => void;
}

const TradeHistory: React.FC<TradeHistoryProps> = ({ onTradeSelect }) => {
  const [filters, setFilters] = useState({
    trade_type: 'all',
    status: 'all',
    portfolio_id: '',
    token_symbol: '',
    timeframe: '1M',
  });
  const [sortBy, setSortBy] = useState<'executed_at' | 'pnl' | 'quantity'>('executed_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch trade history
  const { data: tradesData, isLoading, error } = useQuery({
    queryKey: ['trades', 'history', filters, sortBy, sortOrder, currentPage],
    queryFn: () =>
      TradesService.getTradeHistory({
        pagination: { page: currentPage, page_size: 20 },
        filters: {
          ...(filters.trade_type !== 'all' && { trade_type: filters.trade_type }),
          ...(filters.status !== 'all' && { status: filters.status }),
          ...(filters.portfolio_id && { portfolio_id: filters.portfolio_id }),
          ...(filters.token_symbol && { token_symbol: filters.token_symbol }),
        },
        sort: { field: sortBy, direction: sortOrder },
      }),
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'executed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTradeTypeColor = (type: string) => {
    switch (type) {
      case 'buy':
        return 'bg-green-100 text-green-800';
      case 'sell':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-surface-tertiary rounded"></div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold text-red-600 mb-4">Error Loading Trades</h3>
        <p className="text-text-muted">
          {error instanceof Error ? error.message : 'Failed to load trade history'}
        </p>
      </Card>
    );
  }

  const trades = tradesData?.items || [];

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div>
            <label className="text-sm text-text-secondary mb-1 block">Trade Type</label>
            <select
              value={filters.trade_type}
              onChange={(e) => setFilters(prev => ({ ...prev, trade_type: e.target.value }))}
              className="w-full px-3 py-2 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
            >
              <option value="all">All Types</option>
              <option value="buy">Buy</option>
              <option value="sell">Sell</option>
            </select>
          </div>
          <div>
            <label className="text-sm text-text-secondary mb-1 block">Status</label>
            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="w-full px-3 py-2 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
            >
              <option value="all">All Status</option>
              <option value="executed">Executed</option>
              <option value="pending">Pending</option>
              <option value="cancelled">Cancelled</option>
              <option value="failed">Failed</option>
            </select>
          </div>
          <div>
            <label className="text-sm text-text-secondary mb-1 block">Token</label>
            <input
              type="text"
              value={filters.token_symbol}
              onChange={(e) => setFilters(prev => ({ ...prev, token_symbol: e.target.value }))}
              placeholder="Token symbol"
              className="w-full px-3 py-2 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
            />
          </div>
          <div>
            <label className="text-sm text-text-secondary mb-1 block">Sort by</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="w-full px-3 py-2 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
            >
              <option value="executed_at">Date</option>
              <option value="pnl">P&L</option>
              <option value="quantity">Quantity</option>
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="w-full px-3 py-2 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm hover:bg-surface-tertiary transition-colors"
            >
              {sortOrder === 'asc' ? '↑ Ascending' : '↓ Descending'}
            </button>
          </div>
        </div>
      </Card>

      {/* Trades Table */}
      {trades.length === 0 ? (
        <Card className="p-8 text-center">
          <h3 className="text-lg font-semibold text-text-primary mb-4">No Trades Found</h3>
          <p className="text-text-muted">
            No trades found matching your current filters.
          </p>
        </Card>
      ) : (
        <Card className="overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-surface-tertiary">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Token
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Total Value
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    P&L
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-surface-primary divide-y divide-surface-tertiary">
                {trades.map((trade) => (
                  <tr key={trade.id} className="hover:bg-surface-secondary transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-text-primary">
                          {trade.token_symbol}
                        </div>
                        <div className="text-sm text-text-muted truncate max-w-32">
                          {trade.token_name || 'Unknown Token'}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTradeTypeColor(
                          trade.trade_type
                        )}`}
                      >
                        {trade.trade_type.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          trade.status
                        )}`}
                      >
                        {trade.status.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-primary">
                      {trade.quantity.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-primary">
                      {formatCurrency(trade.price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-text-primary">
                      {formatCurrency(trade.total_value)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                      {trade.pnl !== undefined ? (
                        <div>
                          <div
                            className={`font-medium ${
                              trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}
                          >
                            {formatCurrency(trade.pnl)}
                          </div>
                          {trade.pnl_percentage !== undefined && (
                            <div
                              className={`text-xs ${
                                trade.pnl_percentage >= 0 ? 'text-green-600' : 'text-red-600'
                              }`}
                            >
                              {formatPercentage(trade.pnl_percentage)}
                            </div>
                          )}
                        </div>
                      ) : (
                        <span className="text-text-muted">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-muted">
                      {formatDate(trade.executed_at || trade.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onTradeSelect(trade)}
                      >
                        Details
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}

      {/* Pagination */}
      {tradesData && tradesData.total > 20 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-text-secondary">
            Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, tradesData.total)} of {tradesData.total} trades
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={currentPage * 20 >= tradesData.total}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradeHistory;
