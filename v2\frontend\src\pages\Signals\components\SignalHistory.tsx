import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { TradingSignal } from '@/types';
import { SignalsService } from '@/services/signals';

interface SignalHistoryProps {
  onSignalSelect: (signal: TradingSignal) => void;
}

const SignalHistory: React.FC<SignalHistoryProps> = ({ onSignalSelect }) => {
  const [filters, setFilters] = useState({
    signal_type: 'all',
    status: 'all',
    timeframe: '1M',
  });
  const [sortBy, setSortBy] = useState<'created_at' | 'confidence' | 'return'>('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch signal history
  const { data: historyData, isLoading, error } = useQuery({
    queryKey: ['signals', 'history', filters, sortBy, sortOrder, currentPage],
    queryFn: () =>
      SignalsService.getSignalHistory({
        pagination: { page: currentPage, page_size: 20 },
        filters: filters.signal_type !== 'all' ? { signal_type: filters.signal_type } : {},
        sort: { field: sortBy, direction: sortOrder },
      }),
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'executed':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getSignalTypeColor = (type: string) => {
    switch (type) {
      case 'buy':
        return 'bg-green-100 text-green-800';
      case 'sell':
        return 'bg-red-100 text-red-800';
      case 'hold':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="h-16 bg-surface-tertiary rounded"></div>
              ))}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold text-red-600 mb-4">Error Loading History</h3>
        <p className="text-text-muted">
          {error instanceof Error ? error.message : 'Failed to load signal history'}
        </p>
      </Card>
    );
  }

  const signals = historyData?.items || [];

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-wrap items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div>
              <label className="text-sm text-text-secondary mr-2">Type:</label>
              <select
                value={filters.signal_type}
                onChange={(e) => setFilters(prev => ({ ...prev, signal_type: e.target.value }))}
                className="px-3 py-1 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
              >
                <option value="all">All Types</option>
                <option value="buy">Buy</option>
                <option value="sell">Sell</option>
                <option value="hold">Hold</option>
              </select>
            </div>
            <div>
              <label className="text-sm text-text-secondary mr-2">Status:</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="px-3 py-1 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
              >
                <option value="all">All Status</option>
                <option value="executed">Executed</option>
                <option value="expired">Expired</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label className="text-sm text-text-secondary mr-2">Sort by:</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-1 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm"
              >
                <option value="created_at">Date</option>
                <option value="confidence">Confidence</option>
                <option value="return">Return</option>
              </select>
            </div>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="px-3 py-1 border border-surface-tertiary rounded bg-surface-secondary text-text-primary text-sm hover:bg-surface-tertiary transition-colors"
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>
          <div className="text-sm text-text-secondary">
            {historyData?.total || 0} total signals
          </div>
        </div>
      </Card>

      {/* Signals List */}
      {signals.length === 0 ? (
        <Card className="p-8 text-center">
          <h3 className="text-lg font-semibold text-text-primary mb-4">No Signal History</h3>
          <p className="text-text-muted">
            No signals found matching your current filters.
          </p>
        </Card>
      ) : (
        <Card className="overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-surface-tertiary">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Token
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Confidence
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Target Price
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Actual Return
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-surface-primary divide-y divide-surface-tertiary">
                {signals.map((signal) => (
                  <tr key={signal.id} className="hover:bg-surface-secondary transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-text-primary">
                          {signal.token_symbol}
                        </div>
                        <div className="text-sm text-text-muted truncate max-w-32">
                          {signal.token_name}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getSignalTypeColor(
                          signal.signal_type
                        )}`}
                      >
                        {signal.signal_type.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          signal.status
                        )}`}
                      >
                        {signal.status.toUpperCase()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-primary">
                      {signal.confidence.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-primary">
                      {formatCurrency(signal.target_price)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                      {signal.actual_return !== undefined ? (
                        <span
                          className={`font-medium ${
                            signal.actual_return >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}
                        >
                          {formatPercentage(signal.actual_return)}
                        </span>
                      ) : (
                        <span className="text-text-muted">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-text-muted">
                      {formatDate(signal.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onSignalSelect(signal)}
                      >
                        View Details
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      )}

      {/* Pagination */}
      {historyData && historyData.total > 20 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-text-secondary">
            Showing {((currentPage - 1) * 20) + 1} to {Math.min(currentPage * 20, historyData.total)} of {historyData.total} signals
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={currentPage * 20 >= historyData.total}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SignalHistory;
