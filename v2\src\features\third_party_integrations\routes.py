"""
Third-Party Integrations Routes

FastAPI routes for third-party integrations including exchange APIs,
data providers, portfolio sync, arbitrage detection, and sentiment analysis.
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

import structlog

from src.features.security.auth_manager import AuthManager
from src.features.third_party_integrations.exchange_integrator import (
    ExchangeIntegrator, ExchangeType, OrderType
)
from src.features.third_party_integrations.data_provider_manager import DataProviderManager
from src.features.third_party_integrations.portfolio_sync_service import PortfolioSyncService
from src.features.third_party_integrations.arbitrage_detector import ArbitrageDetector
from src.features.third_party_integrations.news_sentiment_service import NewsSentimentService

logger = structlog.get_logger(__name__)
security = HTTPBearer()

# Create router
router = APIRouter(prefix="/api/v1/integrations", tags=["Third-Party Integrations"])


# Dependency providers
def get_exchange_integrator():
    """Get ExchangeIntegrator instance"""
    return ExchangeIntegrator()


def get_cache_manager():
    """Get CacheManager instance"""
    from ..data_pipeline.cache_manager import CacheManager
    return CacheManager()


def get_data_provider_manager(cache_manager=Depends(get_cache_manager)):
    """Get DataProviderManager instance"""
    from ..third_party_integrations.data_provider_manager import DataProvider, ProviderConfig

    # Create default provider configs (these would normally come from settings)
    provider_configs = {
        DataProvider.COINGECKO: ProviderConfig(
            provider=DataProvider.COINGECKO,
            api_key="dummy_key",
            base_url="https://api.coingecko.com",
            enabled=False,  # Disabled since we don't have real API keys
            rate_limit=50,
            timeout=30
        ),
        DataProvider.COINMARKETCAP: ProviderConfig(
            provider=DataProvider.COINMARKETCAP,
            api_key="dummy_key",
            base_url="https://pro-api.coinmarketcap.com",
            enabled=False,
            rate_limit=100,
            timeout=30
        ),
        DataProvider.DEXSCREENER: ProviderConfig(
            provider=DataProvider.DEXSCREENER,
            api_key="dummy_key",
            base_url="https://api.dexscreener.com",
            enabled=False,
            rate_limit=100,
            timeout=30
        )
    }

    return DataProviderManager(cache_manager, provider_configs)


def get_portfolio_sync_service(cache_manager=Depends(get_cache_manager)):
    """Get PortfolioSyncService instance"""
    from ..paper_trading.portfolio_manager import PortfolioManager
    from .exchange_integrator import ExchangeIntegrator, ExchangeType, ExchangeConfig

    # Create mock exchange configs
    exchange_configs = {
        ExchangeType.BINANCE: ExchangeConfig(
            api_key="dummy_key",
            api_secret="dummy_secret",
            sandbox=True,
            enabled=False
        )
    }

    # Create dependencies
    exchange_integrator = ExchangeIntegrator(cache_manager, exchange_configs)
    portfolio_manager = PortfolioManager()

    return PortfolioSyncService(
        exchange_integrator=exchange_integrator,
        portfolio_manager=portfolio_manager,
        cache_manager=cache_manager
    )


def get_arbitrage_detector(cache_manager=Depends(get_cache_manager)):
    """Get ArbitrageDetector instance"""
    from .exchange_integrator import ExchangeIntegrator, ExchangeType, ExchangeConfig
    from ..notifications.notification_manager import NotificationManager

    # Create mock exchange configs
    exchange_configs = {
        ExchangeType.BINANCE: ExchangeConfig(
            exchange_type=ExchangeType.BINANCE,
            api_key="dummy_key",
            api_secret="dummy_secret",
            sandbox=True
        )
    }

    # Create dependencies
    exchange_integrator = ExchangeIntegrator(cache_manager, exchange_configs)
    notification_manager = NotificationManager()

    return ArbitrageDetector(
        exchange_integrator=exchange_integrator,
        cache_manager=cache_manager,
        notification_manager=notification_manager
    )


def get_news_sentiment_service(
    data_provider_manager=Depends(get_data_provider_manager),
    cache_manager=Depends(get_cache_manager)
):
    """Get NewsSentimentService instance"""
    return NewsSentimentService(data_provider_manager, cache_manager)


# Dependency to get current user
async def get_current_user(token: str = Depends(security)) -> str:
    """Get current user from JWT token"""
    auth_manager = AuthManager()  # This should be injected in production
    user_id = await auth_manager.verify_token(token.credentials)
    if not user_id:
        raise HTTPException(status_code=401, detail="Invalid authentication token")
    return user_id


# Exchange integration endpoints
@router.get("/exchanges/balances")
async def get_exchange_balances(
    exchanges: Optional[List[str]] = Query(None),
    current_user: str = Depends(get_current_user),
    exchange_integrator: ExchangeIntegrator = Depends(get_exchange_integrator)
):
    """
    Get portfolio balances from multiple exchanges.
    
    Args:
        exchanges: List of exchanges to query
        
    Returns:
        Exchange balances
    """
    try:
        exchange_types = None
        if exchanges:
            exchange_types = [ExchangeType(ex) for ex in exchanges]
        
        balances = await exchange_integrator.get_portfolio_balances(
            user_id=current_user,
            exchanges=exchange_types
        )
        
        # Convert to serializable format
        result = {}
        for exchange, balance_list in balances.items():
            result[exchange.value] = [balance.__dict__ for balance in balance_list]
        
        return result
        
    except Exception as e:
        logger.error("Failed to get exchange balances", user_id=current_user, error=str(e))
        raise


@router.post("/exchanges/{exchange}/orders")
async def place_exchange_order(
    exchange: str = Path(...),
    order_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    exchange_integrator: ExchangeIntegrator = Depends(get_exchange_integrator)
):
    """
    Place an order on a specific exchange.
    
    Args:
        exchange: Exchange name
        order_data: Order details
        
    Returns:
        Order placement result
    """
    try:
        order = await exchange_integrator.place_order(
            user_id=current_user,
            exchange=ExchangeType(exchange),
            symbol=order_data["symbol"],
            side=order_data["side"],
            order_type=OrderType(order_data["order_type"]),
            quantity=order_data["quantity"],
            price=order_data.get("price")
        )
        
        return order.__dict__
        
    except Exception as e:
        logger.error("Failed to place exchange order", user_id=current_user, error=str(e))
        raise


@router.get("/exchanges/{exchange}/orders/{order_id}")
async def get_order_status(
    exchange: str = Path(...),
    order_id: str = Path(...),
    current_user: str = Depends(get_current_user),
    exchange_integrator: ExchangeIntegrator = Depends(get_exchange_integrator)
):
    """
    Get order status from exchange.
    
    Args:
        exchange: Exchange name
        order_id: Order identifier
        
    Returns:
        Order status
    """
    try:
        order = await exchange_integrator.get_order_status(
            user_id=current_user,
            exchange=ExchangeType(exchange),
            order_id=order_id
        )
        
        return order.__dict__
        
    except Exception as e:
        logger.error("Failed to get order status", user_id=current_user, error=str(e))
        raise


@router.delete("/exchanges/{exchange}/orders/{order_id}")
async def cancel_exchange_order(
    exchange: str = Path(...),
    order_id: str = Path(...),
    current_user: str = Depends(get_current_user),
    exchange_integrator: ExchangeIntegrator = Depends(get_exchange_integrator)
):
    """
    Cancel an order on exchange.
    
    Args:
        exchange: Exchange name
        order_id: Order identifier
        
    Returns:
        Cancellation result
    """
    try:
        success = await exchange_integrator.cancel_order(
            user_id=current_user,
            exchange=ExchangeType(exchange),
            order_id=order_id
        )
        
        return {"success": success, "message": "Order cancelled" if success else "Failed to cancel order"}
        
    except Exception as e:
        logger.error("Failed to cancel order", user_id=current_user, error=str(e))
        raise


# Data provider endpoints
@router.get("/data/tokens/{token_address}")
async def get_enhanced_token_data(
    token_address: str = Path(...),
    providers: Optional[List[str]] = Query(None),
    data_provider: DataProviderManager = Depends(get_data_provider_manager)
):
    """
    Get enhanced token metadata from multiple providers.
    
    Args:
        token_address: Token contract address
        providers: List of data providers to query
        
    Returns:
        Enhanced token metadata
    """
    try:
        provider_types = None
        if providers:
            from src.features.third_party_integrations.data_provider_manager import DataProvider
            provider_types = [DataProvider(p) for p in providers]
        
        metadata = await data_provider.get_enhanced_token_data(
            token_address=token_address,
            providers=provider_types
        )
        
        return metadata.__dict__
        
    except Exception as e:
        logger.error("Failed to get enhanced token data", token_address=token_address, error=str(e))
        raise


@router.get("/data/trending")
async def get_trending_tokens(
    limit: int = Query(50, ge=1, le=200),
    timeframe: str = Query("24h"),
    data_provider: DataProviderManager = Depends(get_data_provider_manager)
):
    """
    Get trending tokens across platforms.
    
    Args:
        limit: Maximum number of tokens
        timeframe: Trending timeframe
        
    Returns:
        Trending tokens data
    """
    try:
        trending = await data_provider.get_trending_tokens(limit, timeframe)
        return {"trending_tokens": trending}
        
    except Exception as e:
        logger.error("Failed to get trending tokens", error=str(e))
        raise


# Portfolio sync endpoints
@router.post("/portfolio/sync")
async def sync_portfolio(
    sync_data: Dict[str, Any] = Body(...),
    current_user: str = Depends(get_current_user),
    portfolio_sync: PortfolioSyncService = Depends(get_portfolio_sync_service)
):
    """
    Synchronize portfolio across exchanges.
    
    Args:
        sync_data: Sync configuration
        
    Returns:
        Sync result
    """
    try:
        exchanges = None
        if sync_data.get("exchanges"):
            exchanges = [ExchangeType(ex) for ex in sync_data["exchanges"]]
        
        sync_result = await portfolio_sync.sync_user_portfolio(
            user_id=current_user,
            exchanges=exchanges,
            force_sync=sync_data.get("force_sync", False)
        )
        
        return sync_result.__dict__
        
    except Exception as e:
        logger.error("Failed to sync portfolio", user_id=current_user, error=str(e))
        raise


@router.get("/portfolio/unified")
async def get_unified_portfolio(
    include_breakdown: bool = Query(True),
    current_user: str = Depends(get_current_user),
    portfolio_sync: PortfolioSyncService = Depends(get_portfolio_sync_service)
):
    """
    Get unified portfolio view across exchanges.
    
    Args:
        include_breakdown: Include per-exchange breakdown
        
    Returns:
        Unified portfolio report
    """
    try:
        report = await portfolio_sync.get_unified_portfolio(
            user_id=current_user,
            include_exchange_breakdown=include_breakdown
        )
        
        # Convert to serializable format
        result = {
            "user_id": report.user_id,
            "sync_result": report.sync_result.__dict__,
            "unified_positions": [pos.__dict__ for pos in report.unified_positions],
            "total_portfolio_value": report.total_portfolio_value,
            "exchange_breakdown": {k.value: v for k, v in report.exchange_breakdown.items()},
            "asset_allocation": report.asset_allocation,
            "sync_history": [result.__dict__ for result in report.sync_history]
        }
        
        return result
        
    except Exception as e:
        logger.error("Failed to get unified portfolio", user_id=current_user, error=str(e))
        raise


@router.get("/portfolio/analytics")
async def get_portfolio_analytics(
    timeframe: str = Query("30d"),
    current_user: str = Depends(get_current_user),
    portfolio_sync: PortfolioSyncService = Depends(get_portfolio_sync_service)
):
    """
    Get portfolio analytics across exchanges.
    
    Args:
        timeframe: Analytics timeframe
        
    Returns:
        Portfolio analytics
    """
    try:
        analytics = await portfolio_sync.get_portfolio_analytics(current_user, timeframe)
        return analytics
        
    except Exception as e:
        logger.error("Failed to get portfolio analytics", user_id=current_user, error=str(e))
        raise


# Arbitrage detection endpoints
@router.get("/arbitrage/opportunities")
async def scan_arbitrage_opportunities(
    symbols: Optional[List[str]] = Query(None),
    exchanges: Optional[List[str]] = Query(None),
    arbitrage_detector: ArbitrageDetector = Depends(get_arbitrage_detector)
):
    """
    Scan for arbitrage opportunities.
    
    Args:
        symbols: List of symbols to scan
        exchanges: List of exchanges to scan
        
    Returns:
        Arbitrage opportunities
    """
    try:
        exchange_types = None
        if exchanges:
            exchange_types = [ExchangeType(ex) for ex in exchanges]
        
        opportunities = await arbitrage_detector.scan_opportunities(
            symbols=symbols,
            exchanges=exchange_types
        )
        
        return {
            "opportunities": [opp.__dict__ for opp in opportunities],
            "count": len(opportunities)
        }
        
    except Exception as e:
        logger.error("Failed to scan arbitrage opportunities", error=str(e))
        raise


@router.get("/arbitrage/opportunities/{opportunity_id}")
async def get_arbitrage_opportunity_details(
    opportunity_id: str = Path(...),
    arbitrage_detector: ArbitrageDetector = Depends(get_arbitrage_detector)
):
    """
    Get detailed arbitrage opportunity information.
    
    Args:
        opportunity_id: Opportunity identifier
        
    Returns:
        Detailed opportunity information
    """
    try:
        details = await arbitrage_detector.get_opportunity_details(opportunity_id)
        return details
        
    except Exception as e:
        logger.error("Failed to get opportunity details", opportunity_id=opportunity_id, error=str(e))
        raise


@router.post("/arbitrage/execution-plan")
async def calculate_arbitrage_execution_plan(
    plan_data: Dict[str, Any] = Body(...),
    arbitrage_detector: ArbitrageDetector = Depends(get_arbitrage_detector)
):
    """
    Calculate execution plan for arbitrage opportunity.
    
    Args:
        plan_data: Execution plan parameters
        
    Returns:
        Execution plan details
    """
    try:
        from src.features.third_party_integrations.exchange_integrator import ArbitrageOpportunity
        
        opportunity = ArbitrageOpportunity(**plan_data["opportunity"])
        trade_amount = plan_data["trade_amount"]
        
        execution_plan = await arbitrage_detector.calculate_execution_plan(
            opportunity=opportunity,
            trade_amount=trade_amount
        )
        
        return execution_plan
        
    except Exception as e:
        logger.error("Failed to calculate execution plan", error=str(e))
        raise


@router.get("/arbitrage/statistics")
async def get_arbitrage_statistics(
    timeframe: str = Query("24h"),
    arbitrage_detector: ArbitrageDetector = Depends(get_arbitrage_detector)
):
    """
    Get arbitrage statistics and analytics.
    
    Args:
        timeframe: Statistics timeframe
        
    Returns:
        Arbitrage statistics
    """
    try:
        statistics = await arbitrage_detector.get_arbitrage_statistics(timeframe)
        return statistics
        
    except Exception as e:
        logger.error("Failed to get arbitrage statistics", error=str(e))
        raise


# News and sentiment endpoints
@router.get("/sentiment/tokens/{token_address}")
async def get_token_sentiment(
    token_address: str = Path(...),
    timeframe: str = Query("24h"),
    sentiment_service: NewsSentimentService = Depends(get_news_sentiment_service)
):
    """
    Get sentiment analysis for a token.
    
    Args:
        token_address: Token contract address
        timeframe: Analysis timeframe
        
    Returns:
        Token sentiment data
    """
    try:
        sentiment = await sentiment_service.get_token_sentiment(token_address, timeframe)
        return sentiment.__dict__
        
    except Exception as e:
        logger.error("Failed to get token sentiment", token_address=token_address, error=str(e))
        raise


@router.get("/sentiment/news/{token_address}")
async def get_news_analysis(
    token_address: str = Path(...),
    limit: int = Query(20, ge=1, le=100),
    hours_back: int = Query(24, ge=1, le=168),
    sentiment_service: NewsSentimentService = Depends(get_news_sentiment_service)
):
    """
    Get news analysis for a token.
    
    Args:
        token_address: Token contract address
        limit: Maximum number of articles
        hours_back: How many hours back to analyze
        
    Returns:
        News analysis data
    """
    try:
        analysis = await sentiment_service.get_news_analysis(
            token_address=token_address,
            limit=limit,
            hours_back=hours_back
        )
        
        return analysis
        
    except Exception as e:
        logger.error("Failed to get news analysis", token_address=token_address, error=str(e))
        raise


@router.post("/sentiment/signals")
async def generate_sentiment_signals(
    signal_data: Dict[str, Any] = Body(...),
    sentiment_service: NewsSentimentService = Depends(get_news_sentiment_service)
):
    """
    Generate sentiment-based trading signals.
    
    Args:
        signal_data: Signal generation parameters
        
    Returns:
        Sentiment signals
    """
    try:
        signals = await sentiment_service.generate_sentiment_signals(
            token_addresses=signal_data["token_addresses"],
            min_confidence=signal_data.get("min_confidence", 0.6)
        )
        
        return {
            "signals": [signal.__dict__ for signal in signals],
            "count": len(signals)
        }
        
    except Exception as e:
        logger.error("Failed to generate sentiment signals", error=str(e))
        raise


@router.get("/sentiment/trending")
async def get_trending_topics(
    limit: int = Query(20, ge=1, le=100),
    timeframe: str = Query("1h"),
    sentiment_service: NewsSentimentService = Depends(get_news_sentiment_service)
):
    """
    Get trending topics across social media and news.
    
    Args:
        limit: Maximum number of topics
        timeframe: Trending timeframe
        
    Returns:
        Trending topics
    """
    try:
        topics = await sentiment_service.get_trending_topics(limit, timeframe)
        
        return {
            "trending_topics": [topic.__dict__ for topic in topics],
            "count": len(topics)
        }
        
    except Exception as e:
        logger.error("Failed to get trending topics", error=str(e))
        raise


@router.get("/sentiment/social/{token_address}")
async def get_social_metrics(
    token_address: str = Path(...),
    timeframe: str = Query("24h"),
    sentiment_service: NewsSentimentService = Depends(get_news_sentiment_service)
):
    """
    Get social media metrics for a token.
    
    Args:
        token_address: Token contract address
        timeframe: Metrics timeframe
        
    Returns:
        Social media metrics
    """
    try:
        metrics = await sentiment_service.get_social_metrics(token_address, timeframe)
        return metrics
        
    except Exception as e:
        logger.error("Failed to get social metrics", token_address=token_address, error=str(e))
        raise
