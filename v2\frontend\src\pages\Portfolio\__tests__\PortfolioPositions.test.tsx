import React from 'react';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '@/test/utils';
import {
  mockSuccessfulQuery,
  mockLoadingQuery,
  mockErrorQuery,
  mockSuccessfulMutation,
  resetAllMocks,
} from '@/test/mocks';
import {
  createMockPositions,
  createMockPaginatedResponse,
} from '@/test/factories';
import PortfolioPositions from '../components/PortfolioPositions';

jest.mock('@tanstack/react-query');

describe('PortfolioPositions', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    resetAllMocks();
  });

  describe('Loading State', () => {
    it('should display loading skeleton when positions are loading', () => {
      mockLoadingQuery();

      render(<PortfolioPositions />);

      const skeletons = screen.getAllByTestId(/skeleton/i);
      expect(skeletons.length).toBeGreaterThan(0);
    });
  });

  describe('Success State', () => {
    it('should display positions table when data loads successfully', async () => {
      const mockPositions = createMockPositions(3);
      const mockResponse = createMockPaginatedResponse(mockPositions);
      
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        // Check table headers
        expect(screen.getByText('Asset')).toBeInTheDocument();
        expect(screen.getByText('Quantity')).toBeInTheDocument();
        expect(screen.getByText('Avg Price')).toBeInTheDocument();
        expect(screen.getByText('Current Price')).toBeInTheDocument();
        expect(screen.getByText('Market Value')).toBeInTheDocument();
        expect(screen.getByText('P&L')).toBeInTheDocument();
        expect(screen.getByText('Allocation')).toBeInTheDocument();

        // Check position data
        mockPositions.forEach(position => {
          expect(screen.getByText(position.token_symbol)).toBeInTheDocument();
        });
      });
    });

    it('should display position details correctly', async () => {
      const mockPositions = createMockPositions(1, 'portfolio-1');
      const position = mockPositions[0];
      const mockResponse = createMockPaginatedResponse(mockPositions);
      
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        expect(screen.getByText(position.token_symbol)).toBeInTheDocument();
        expect(screen.getByText(position.quantity.toString())).toBeInTheDocument();
        expect(screen.getByText(`$${position.average_price.toFixed(2)}`)).toBeInTheDocument();
        expect(screen.getByText(`$${position.current_price.toFixed(2)}`)).toBeInTheDocument();
        expect(screen.getByText(`$${position.market_value.toLocaleString()}`)).toBeInTheDocument();
      });
    });

    it('should display positive P&L with green color', async () => {
      const mockPositions = createMockPositions(1);
      mockPositions[0].pnl = 1000;
      mockPositions[0].pnl_percentage = 20;
      const mockResponse = createMockPaginatedResponse(mockPositions);
      
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        const pnlElement = screen.getByText('+$1,000.00');
        expect(pnlElement).toBeInTheDocument();
        expect(pnlElement.closest('div')).toHaveClass('text-green-600');
      });
    });

    it('should display negative P&L with red color', async () => {
      const mockPositions = createMockPositions(1);
      mockPositions[0].pnl = -500;
      mockPositions[0].pnl_percentage = -10;
      const mockResponse = createMockPaginatedResponse(mockPositions);
      
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        const pnlElement = screen.getByText('-$500.00');
        expect(pnlElement).toBeInTheDocument();
        expect(pnlElement.closest('div')).toHaveClass('text-red-600');
      });
    });
  });

  describe('Error State', () => {
    it('should display error message when positions loading fails', async () => {
      const mockError = new Error('Failed to fetch positions');
      mockErrorQuery(mockError);

      render(<PortfolioPositions />);

      await waitFor(() => {
        expect(screen.getByText(/error loading positions/i)).toBeInTheDocument();
        expect(screen.getByText(/failed to fetch positions/i)).toBeInTheDocument();
      });
    });

    it('should display retry button on error', async () => {
      const mockError = new Error('Network error');
      mockErrorQuery(mockError);

      render(<PortfolioPositions />);

      await waitFor(() => {
        const retryButton = screen.getByRole('button', { name: /try again/i });
        expect(retryButton).toBeInTheDocument();
      });
    });
  });

  describe('Empty State', () => {
    it('should display empty state when no positions exist', async () => {
      const mockResponse = createMockPaginatedResponse([]);
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        expect(screen.getByText(/no positions found/i)).toBeInTheDocument();
        expect(screen.getByText(/start trading to see your positions here/i)).toBeInTheDocument();
      });
    });
  });

  describe('Filtering and Sorting', () => {
    it('should filter positions by search term', async () => {
      const mockPositions = [
        ...createMockPositions(1),
        { ...createMockPositions(1)[0], id: 'pos-2', token_symbol: 'ETH', token_name: 'Ethereum' },
        { ...createMockPositions(1)[0], id: 'pos-3', token_symbol: 'BTC', token_name: 'Bitcoin' },
      ];
      const mockResponse = createMockPaginatedResponse(mockPositions);
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        expect(screen.getByText('SOL')).toBeInTheDocument();
        expect(screen.getByText('ETH')).toBeInTheDocument();
        expect(screen.getByText('BTC')).toBeInTheDocument();
      });

      // Search for ETH
      const searchInput = screen.getByPlaceholderText(/search positions/i);
      await user.type(searchInput, 'ETH');

      await waitFor(() => {
        expect(screen.getByText('ETH')).toBeInTheDocument();
        expect(screen.queryByText('SOL')).not.toBeInTheDocument();
        expect(screen.queryByText('BTC')).not.toBeInTheDocument();
      });
    });

    it('should sort positions by different columns', async () => {
      const mockPositions = createMockPositions(3);
      const mockResponse = createMockPaginatedResponse(mockPositions);
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        expect(screen.getByText('Asset')).toBeInTheDocument();
      });

      // Click on P&L column to sort
      const pnlHeader = screen.getByText('P&L');
      await user.click(pnlHeader);

      // Verify sorting indicator appears
      expect(pnlHeader.closest('th')).toContainHTML('mock-icon');
    });
  });

  describe('Position Actions', () => {
    it('should show action buttons for each position', async () => {
      const mockPositions = createMockPositions(1);
      const mockResponse = createMockPaginatedResponse(mockPositions);
      mockSuccessfulQuery(mockResponse);
      mockSuccessfulMutation();

      render(<PortfolioPositions />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /buy more/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /sell/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /details/i })).toBeInTheDocument();
      });
    });

    it('should handle buy more action', async () => {
      const mockPositions = createMockPositions(1);
      const mockResponse = createMockPaginatedResponse(mockPositions);
      mockSuccessfulQuery(mockResponse);
      mockSuccessfulMutation();

      render(<PortfolioPositions />);

      await waitFor(() => {
        const buyButton = screen.getByRole('button', { name: /buy more/i });
        expect(buyButton).toBeInTheDocument();
      });

      const buyButton = screen.getByRole('button', { name: /buy more/i });
      await user.click(buyButton);

      // Should open buy modal or navigate to trade page
      // This would depend on the actual implementation
    });

    it('should handle sell action', async () => {
      const mockPositions = createMockPositions(1);
      const mockResponse = createMockPaginatedResponse(mockPositions);
      mockSuccessfulQuery(mockResponse);
      mockSuccessfulMutation();

      render(<PortfolioPositions />);

      await waitFor(() => {
        const sellButton = screen.getByRole('button', { name: /sell/i });
        expect(sellButton).toBeInTheDocument();
      });

      const sellButton = screen.getByRole('button', { name: /sell/i });
      await user.click(sellButton);

      // Should open sell modal or navigate to trade page
    });
  });

  describe('Pagination', () => {
    it('should display pagination when there are multiple pages', async () => {
      const mockPositions = createMockPositions(25); // More than one page
      const mockResponse = createMockPaginatedResponse(mockPositions.slice(0, 20), {
        total: 25,
        page: 1,
        page_size: 20,
        total_pages: 2,
        has_next: true,
      });
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        expect(screen.getByText('1')).toBeInTheDocument(); // Current page
        expect(screen.getByText('2')).toBeInTheDocument(); // Next page
        expect(screen.getByRole('button', { name: /next/i })).toBeInTheDocument();
      });
    });

    it('should handle page navigation', async () => {
      const mockPositions = createMockPositions(25);
      const mockResponse = createMockPaginatedResponse(mockPositions.slice(0, 20), {
        total: 25,
        page: 1,
        page_size: 20,
        total_pages: 2,
        has_next: true,
      });
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        const nextButton = screen.getByRole('button', { name: /next/i });
        expect(nextButton).toBeInTheDocument();
      });

      const nextButton = screen.getByRole('button', { name: /next/i });
      await user.click(nextButton);

      // Should trigger a new query for page 2
    });
  });

  describe('Responsive Design', () => {
    it('should adapt table for mobile screens', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const mockPositions = createMockPositions(3);
      const mockResponse = createMockPaginatedResponse(mockPositions);
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        // On mobile, table should be scrollable or show cards
        const container = screen.getByTestId('positions-container');
        expect(container).toBeInTheDocument();
      });
    });
  });

  describe('Real-time Updates', () => {
    it('should update position data when prices change', async () => {
      const mockPositions = createMockPositions(1);
      const mockResponse = createMockPaginatedResponse(mockPositions);
      mockSuccessfulQuery(mockResponse);

      render(<PortfolioPositions />);

      await waitFor(() => {
        expect(screen.getByText(`$${mockPositions[0].current_price.toFixed(2)}`)).toBeInTheDocument();
      });

      // Simulate price update
      const updatedPositions = [...mockPositions];
      updatedPositions[0].current_price = 150;
      updatedPositions[0].market_value = 15000;
      updatedPositions[0].pnl = 5000;
      updatedPositions[0].pnl_percentage = 50;

      const updatedResponse = createMockPaginatedResponse(updatedPositions);
      mockSuccessfulQuery(updatedResponse);

      // Trigger refetch (this would happen automatically in real app)
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      if (refreshButton) {
        await user.click(refreshButton);
      }
    });
  });
});
