{"name": "tokentracker-v2-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__ --coverage", "test:unit:watch": "jest --testPathPattern=__tests__ --watch", "test:integration": "jest --testPathPattern=integration --coverage --coverageDirectory=coverage/integration", "test:integration:watch": "jest --testPathPattern=integration --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:headed": "playwright test --headed", "test:all": "tsx src/test/run-all-tests.ts", "test:ci": "npm run lint && npm run type-check && npm run test:unit && npm run test:integration && npm run build", "type-check": "tsc --noEmit", "playwright:install": "playwright install", "playwright:install-deps": "playwright install-deps"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "@tanstack/react-query": "^5.8.4", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "socket.io-client": "^4.7.4", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.1.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "typescript": "^5.2.2", "vite": "^5.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "identity-obj-proxy": "^3.0.0", "@playwright/test": "^1.40.0", "tsx": "^4.6.0"}}