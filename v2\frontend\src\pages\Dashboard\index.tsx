import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardService } from '@/services/dashboard';
import PortfolioSummaryCards from './components/PortfolioSummaryCards';
import PerformanceChart from './components/PerformanceChart';
import RecentActivity from './components/RecentActivity';
import MarketOverview from './components/MarketOverview';
import QuickActions from './components/QuickActions';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

/**
 * Main dashboard page component
 */
const Dashboard: React.FC = () => {
  // Fetch dashboard data
  const {
    data: dashboardData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['dashboard'],
    queryFn: DashboardService.getDashboardData,
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Fetch real-time updates
  const { data: realtimeData } = useQuery({
    queryKey: ['dashboard-realtime'],
    queryFn: DashboardService.getRealtimeData,
    refetchInterval: 5000, // Refetch every 5 seconds
    enabled: !!dashboardData, // Only fetch if main data is loaded
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-accent-red mb-4">
          <p className="text-lg font-medium">Failed to load dashboard</p>
          <p className="text-sm text-text-muted">
            {error instanceof Error ? error.message : 'An unexpected error occurred'}
          </p>
        </div>
        <button
          onClick={() => refetch()}
          className="px-4 py-2 bg-accent-blue text-white rounded-lg hover:bg-accent-blue/90 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-12">
        <p className="text-text-muted">No dashboard data available</p>
      </div>
    );
  }

  // Merge real-time data with main data
  const currentData = {
    ...dashboardData,
    ...realtimeData,
    portfolio_summary: {
      ...dashboardData.portfolio_summary,
      ...realtimeData?.portfolio_summary,
    },
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-text-primary">Dashboard</h1>
          <p className="text-text-secondary mt-1">
            Welcome back! Here's what's happening with your trading.
          </p>
        </div>
        <QuickActions />
      </div>

      {/* Portfolio Summary Cards */}
      <PortfolioSummaryCards data={currentData.portfolio_summary} />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Performance Chart - Takes 2 columns on large screens */}
        <div className="lg:col-span-2">
          <PerformanceChart />
        </div>

        {/* Market Overview */}
        <div className="lg:col-span-1">
          <MarketOverview data={currentData.market_overview} />
        </div>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <RecentActivity
          title="Recent Signals"
          data={currentData.recent_signals}
          type="signals"
        />
        <RecentActivity
          title="Recent Trades"
          data={currentData.recent_trades}
          type="trades"
        />
      </div>
    </div>
  );
};

export default Dashboard;
