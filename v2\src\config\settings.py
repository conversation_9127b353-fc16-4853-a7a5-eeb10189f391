"""
⚙️ Application Settings Configuration

Centralized settings management using Pydantic for validation
and environment-specific configuration following V2 instructions.
"""

import os
from functools import lru_cache
from typing import Optional, List
from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    🔧 Main application settings with environment validation
    """

    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"  # Allow extra fields from .env file
    )

    # 🌍 Application Configuration
    app_name: str = Field(default="TokenTracker_V2", env="APP_NAME")
    app_version: str = Field(default="2.0.0", env="APP_VERSION")
    environment: str = Field(default="development", env="NODE_ENV")
    debug: bool = Field(default=False, env="DEBUG")
    app_port: int = Field(default=3000, env="APP_PORT")
    
    # 🗄️ Database Configuration
    mongodb_uri: str = Field(default="mongodb://localhost:27017/tokentracker", env="MONGODB_URI")
    mongodb_read_replica_uri: Optional[str] = Field(None, env="MONGODB_READ_REPLICA_URI")
    mongodb_db_name: str = Field(default="tokentracker_v2", env="MONGODB_DB_NAME")
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")

    # 🔍 Dune Analytics Configuration
    dune_api_key: str = Field(default="test_dune_api_key", env="DUNE_API_KEY")
    dune_query_id: str = Field(default="123456", env="DUNE_QUERY_ID")
    dune_execution_id: Optional[str] = Field(None, env="DUNE_EXECUTION_ID")
    dune_base_url: str = Field(default="https://api.dune.com/api/v1", env="DUNE_BASE_URL")

    # 📱 Telegram Configuration
    telegram_bot_token: str = Field(default="test_telegram_bot_token", env="TELEGRAM_BOT_TOKEN")
    telegram_channel_id: str = Field(default="test_telegram_channel_id", env="TELEGRAM_CHANNEL_ID")
    telegram_admin_chat_id: Optional[str] = Field(None, env="TELEGRAM_ADMIN_CHAT_ID")
    
    # 🌐 Solana & DEX Configuration
    solana_rpc_url: str = Field(default="https://api.mainnet-beta.solana.com", env="SOLANA_RPC_URL")
    solana_ws_url: str = Field(default="wss://api.mainnet-beta.solana.com", env="SOLANA_WS_URL")
    jupiter_api_url: str = Field(default="https://quote-api.jup.ag/v6", env="JUPITER_API_URL")
    raydium_api_url: str = Field(default="https://api.raydium.io/v2", env="RAYDIUM_API_URL")
    birdeye_api_key: Optional[str] = Field(None, env="BIRDEYE_API_KEY")
    
    # 🔐 Security Configuration
    jwt_secret: str = Field(default="test_jwt_secret_key", env="JWT_SECRET")
    jwt_secret_key: str = Field(default="test_jwt_secret_key", env="JWT_SECRET")  # Alias for compatibility
    jwt_expires_in: str = Field(default="24h", env="JWT_EXPIRES_IN")
    api_rate_limit: int = Field(default=100, env="API_RATE_LIMIT")
    encryption_key: str = Field(default="test_encryption_key_32_characters_long", env="ENCRYPTION_KEY")
    encryption_master_key: Optional[str] = Field(None, env="ENCRYPTION_MASTER_KEY")
    
    # 📊 Trading Configuration
    query_interval_minutes: int = Field(default=30, env="QUERY_INTERVAL_MINUTES")
    cache_duration_minutes: int = Field(default=60, env="CACHE_DURATION_MINUTES")
    max_position_size_usd: float = Field(default=1000.0, env="MAX_POSITION_SIZE_USD")
    default_stop_loss_percent: float = Field(default=5.0, env="DEFAULT_STOP_LOSS_PERCENT")
    default_take_profit_percent: float = Field(default=15.0, env="DEFAULT_TAKE_PROFIT_PERCENT")
    min_liquidity_usd: float = Field(default=50000.0, env="MIN_LIQUIDITY_USD")
    
    # 🤖 AI Integration (Optional)
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    
    # 📈 Monitoring & Logging
    log_level: str = Field(default="info", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    health_check_interval: int = Field(default=60, env="HEALTH_CHECK_INTERVAL")
    prometheus_port: int = Field(default=9090, env="PROMETHEUS_PORT")

    # 🔍 Advanced Monitoring Configuration
    monitoring_enabled: bool = Field(default=True, env="MONITORING_ENABLED")
    metrics_port: int = Field(default=8001, env="METRICS_PORT")
    alert_webhook_url: Optional[str] = Field(None, env="ALERT_WEBHOOK_URL")
    prometheus_enabled: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    deep_health_check_interval: int = Field(default=300, env="DEEP_HEALTH_CHECK_INTERVAL")

    # 📊 Performance Monitoring
    performance_profiling_enabled: bool = Field(default=True, env="PERFORMANCE_PROFILING_ENABLED")
    memory_profiling_enabled: bool = Field(default=False, env="MEMORY_PROFILING_ENABLED")
    cpu_profiling_enabled: bool = Field(default=False, env="CPU_PROFILING_ENABLED")
    max_request_profiles: int = Field(default=1000, env="MAX_REQUEST_PROFILES")
    max_database_profiles: int = Field(default=1000, env="MAX_DATABASE_PROFILES")

    # 🚨 Alert Configuration
    alert_enabled: bool = Field(default=True, env="ALERT_ENABLED")
    alert_cooldown_minutes: int = Field(default=15, env="ALERT_COOLDOWN_MINUTES")
    alert_escalation_minutes: int = Field(default=60, env="ALERT_ESCALATION_MINUTES")
    max_alerts_per_hour: int = Field(default=50, env="MAX_ALERTS_PER_HOUR")

    # 📝 Log Aggregation
    log_aggregation_enabled: bool = Field(default=True, env="LOG_AGGREGATION_ENABLED")
    max_log_entries: int = Field(default=10000, env="MAX_LOG_ENTRIES")
    log_retention_days: int = Field(default=30, env="LOG_RETENTION_DAYS")
    log_pattern_detection_enabled: bool = Field(default=True, env="LOG_PATTERN_DETECTION_ENABLED")

    # 🎯 Performance Thresholds
    slow_request_threshold_ms: int = Field(default=1000, env="SLOW_REQUEST_THRESHOLD_MS")
    slow_database_threshold_ms: int = Field(default=500, env="SLOW_DATABASE_THRESHOLD_MS")
    high_memory_threshold_mb: int = Field(default=500, env="HIGH_MEMORY_THRESHOLD_MB")
    high_cpu_threshold_percent: float = Field(default=80.0, env="HIGH_CPU_THRESHOLD_PERCENT")
    high_disk_threshold_percent: float = Field(default=90.0, env="HIGH_DISK_THRESHOLD_PERCENT")
    
    # 🔄 Performance Configuration
    max_concurrent_requests: int = Field(default=50, env="MAX_CONCURRENT_REQUESTS")
    request_timeout_ms: int = Field(default=30000, env="REQUEST_TIMEOUT_MS")
    retry_attempts: int = Field(default=3, env="RETRY_ATTEMPTS")
    retry_delay_ms: int = Field(default=1000, env="RETRY_DELAY_MS")
    
    # 📧 Notification Configuration
    email_smtp_host: Optional[str] = Field(None, env="EMAIL_SMTP_HOST")
    email_smtp_port: Optional[int] = Field(None, env="EMAIL_SMTP_PORT")
    email_user: Optional[str] = Field(None, env="EMAIL_USER")
    email_password: Optional[str] = Field(None, env="EMAIL_PASSWORD")
    email_from: Optional[str] = Field(None, env="EMAIL_FROM")
    
    # 🔔 Additional Alert Configuration
    slack_webhook_url: Optional[str] = Field(None, env="SLACK_WEBHOOK_URL")
    discord_webhook_url: Optional[str] = Field(None, env="DISCORD_WEBHOOK_URL")
    
    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v):
        """Validate environment values"""
        allowed_envs = ["development", "staging", "production", "test"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of: {allowed_envs}")
        return v

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        """Validate log level values"""
        allowed_levels = ["trace", "debug", "info", "warn", "error", "fatal"]
        if v.lower() not in allowed_levels:
            raise ValueError(f"Log level must be one of: {allowed_levels}")
        return v.lower()

    @field_validator("encryption_key")
    @classmethod
    def validate_encryption_key(cls, v):
        """Validate encryption key length"""
        if len(v) < 32:
            raise ValueError("Encryption key must be at least 32 characters long")
        return v
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment == "development"
    
    @property
    def is_testing(self) -> bool:
        """Check if running in test environment"""
        return self.environment == "test"


@lru_cache()
def get_settings() -> Settings:
    """
    🔧 Get cached settings instance
    
    Returns:
        Settings: Validated application settings
    """
    return Settings()
