# ===========================================
# 🐳 TOKENTRACKER V2 DOCKERFILE
# ===========================================
# Multi-stage build following DEPLOYMENT_SCALING.md guidelines

# 🏗️ Base stage with Python and system dependencies
FROM python:3.11-slim as base

# Set environment variables for network resilience
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_RETRIES=10 \
    PIP_TIMEOUT=300 \
    PIP_DEFAULT_TIMEOUT=300 \
    DEBIAN_FRONTEND=noninteractive

# Configure apt for better network handling
RUN echo 'Acquire::Retries "10";' > /etc/apt/apt.conf.d/80-retries && \
    echo 'Acquire::http::Timeout "300";' >> /etc/apt/apt.conf.d/80-retries && \
    echo 'Acquire::ftp::Timeout "300";' >> /etc/apt/apt.conf.d/80-retries && \
    echo 'APT::Get::Assume-Yes "true";' >> /etc/apt/apt.conf.d/80-retries && \
    echo 'APT::Install-Recommends "false";' >> /etc/apt/apt.conf.d/80-retries

# Install system dependencies with retry logic
RUN for i in 1 2 3 4 5; do \
        apt-get update && \
        apt-get install -y \
            build-essential \
            curl \
            git \
            wget \
            ca-certificates \
            && break || \
        (echo "Attempt $i failed, retrying in 10 seconds..." && sleep 10); \
    done && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Create app directory
WORKDIR /app

# 📦 Dependencies stage
FROM base as deps

# Copy requirements first for better caching
COPY requirements-simple.txt .
COPY requirements.txt .

# Create pip configuration for network resilience
RUN mkdir -p ~/.pip && \
    echo '[global]' > ~/.pip/pip.conf && \
    echo 'timeout = 300' >> ~/.pip/pip.conf && \
    echo 'retries = 10' >> ~/.pip/pip.conf && \
    echo 'trusted-host = pypi.org' >> ~/.pip/pip.conf && \
    echo '               pypi.python.org' >> ~/.pip/pip.conf && \
    echo '               files.pythonhosted.org' >> ~/.pip/pip.conf

# Install Python dependencies with retry logic and caching enabled
RUN for i in 1 2 3 4 5; do \
        pip install --timeout=300 --retries=10 \
            --cache-dir=/tmp/pip-cache \
            -r requirements-simple.txt && break || \
        (echo "Pip install attempt $i failed, retrying in 15 seconds..." && sleep 15); \
    done

# 🧪 Development stage
FROM deps as development

# Install development dependencies with retry logic
RUN for i in 1 2 3 4 5; do \
        pip install --timeout=300 --retries=10 \
            --cache-dir=/tmp/pip-cache \
            pytest \
            pytest-asyncio \
            pytest-cov \
            black \
            flake8 \
            mypy && break || \
        (echo "Dev dependencies install attempt $i failed, retrying in 15 seconds..." && sleep 15); \
    done

# Copy source code
COPY . .

# Create non-root user for development
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 3000

# Development command
CMD ["python", "-m", "uvicorn", "src.app:socket_app", "--host", "0.0.0.0", "--port", "3000", "--reload"]

# 🚀 Production stage - Phase 3 Optimized
FROM deps as production

# Set production build arguments
ARG BUILD_DATE
ARG VERSION
ARG VCS_REF

# Add production labels
LABEL maintainer="TokenTracker Team" \
      version="${VERSION}" \
      description="TokenTracker V2 - Advanced Token Analysis Platform" \
      build-date="${BUILD_DATE}" \
      vcs-ref="${VCS_REF}"

# Set production environment variables
ENV NODE_ENV=production \
    PYTHONPATH=/app/src \
    PORT=8000

# Install production optimizations with retry logic
RUN for i in 1 2 3 4 5; do \
        pip install --timeout=300 --retries=10 \
            --cache-dir=/tmp/pip-cache \
            uvloop \
            httptools \
            gunicorn && break || \
        (echo "Production dependencies install attempt $i failed, retrying in 15 seconds..." && sleep 15); \
    done

# Copy only necessary files
COPY src/ ./src/
COPY scripts/ ./scripts/
COPY .env.example .env.example

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create necessary directories with proper permissions
RUN mkdir -p /app/logs /app/data /app/cache /app/tmp && \
    chown -R appuser:appuser /app

# Copy and set up health check script
COPY scripts/docker-healthcheck.sh /usr/local/bin/healthcheck.sh
RUN chmod +x /usr/local/bin/healthcheck.sh

# Switch to non-root user
USER appuser

# Health check with comprehensive monitoring endpoints
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# Expose port (changed to 8000 for production)
EXPOSE 8000

# Production command with optimizations
CMD ["python", "-m", "uvicorn", "src.app:socket_app", \
     "--host", "0.0.0.0", \
     "--port", "8000", \
     "--workers", "4", \
     "--loop", "uvloop", \
     "--access-log", \
     "--log-level", "info"]

# 🧪 Testing stage
FROM development as testing

# Copy test files
COPY tests/ ./tests/

# Run tests
RUN python -m pytest tests/ -v --cov=src --cov-report=term-missing

# 📊 Monitoring stage (for metrics collection)
FROM production as monitoring

# Install monitoring tools with retry logic
USER root
RUN for i in 1 2 3 4 5; do \
        pip install --timeout=300 --retries=10 \
            --cache-dir=/tmp/pip-cache \
            prometheus-client \
            psutil && break || \
        (echo "Monitoring dependencies install attempt $i failed, retrying in 15 seconds..." && sleep 15); \
    done

USER appuser

# Expose metrics port
EXPOSE 9090

# Command with metrics
CMD ["python", "-m", "uvicorn", "src.app:socket_app", "--host", "0.0.0.0", "--port", "3000", "--workers", "4"]
