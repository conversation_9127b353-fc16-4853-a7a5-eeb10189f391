# 🚀 TokenTracker V2 - Minimal Dependencies for Quick Start
# Essential dependencies only to get the application running

# 🌐 Web Framework & API
fastapi==0.115.0
uvicorn[standard]==0.32.0
pydantic==2.10.0
pydantic-settings==2.7.0

# 🗄️ Database & Caching
pymongo==4.8.0
motor==3.5.1
beanie==1.26.0
redis==5.1.1

# 🌐 HTTP & API Clients
httpx==0.28.0
aiohttp==3.11.0
requests==2.32.0

# 📊 Data Processing (minimal)
pandas==2.2.0
numpy==1.26.0

# 🔐 Security & Authentication
cryptography==43.0.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.12
PyJWT==2.10.0

# 📝 Logging & Monitoring
structlog==24.4.0

# 🔧 Development & Utilities
python-dotenv==1.0.1
python-dateutil==2.9.0
pytz==2024.2

# 📋 Data Validation & Serialization
jinja2==3.1.4
email-validator==2.2.0

# 🔄 Async & Concurrency
aiofiles==24.1.0

# 🔧 System & Performance
psutil==6.1.0

# 🔄 Retry & Resilience
tenacity==9.0.0

# 🌐 Blockchain & Solana
solana==0.34.3
solders==0.21.0
