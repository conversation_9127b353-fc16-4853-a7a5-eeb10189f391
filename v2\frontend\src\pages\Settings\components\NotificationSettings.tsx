import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import Button from '@/components/ui/Button';
import { Bell, Mail, Smartphone, TrendingUp, AlertTriangle, DollarSign, Activity } from 'lucide-react';

const NotificationSettings: React.FC = () => {
  const queryClient = useQueryClient();

  // Fetch notification preferences
  const { data: preferences, isLoading } = useQuery({
    queryKey: ['notification-preferences'],
    queryFn: async () => {
      // Mock API call
      return {
        email_notifications: {
          trade_executions: true,
          portfolio_alerts: true,
          price_alerts: false,
          market_news: true,
          security_alerts: true,
          weekly_summary: true,
        },
        push_notifications: {
          trade_executions: true,
          portfolio_alerts: true,
          price_alerts: true,
          market_news: false,
          security_alerts: true,
        },
        alert_thresholds: {
          portfolio_change_percent: 5,
          position_change_percent: 10,
          price_change_percent: 15,
        },
        quiet_hours: {
          enabled: true,
          start_time: '22:00',
          end_time: '08:00',
        },
      };
    },
  });

  const [localPreferences, setLocalPreferences] = useState<any>(null);

  React.useEffect(() => {
    if (preferences && !localPreferences) {
      setLocalPreferences(preferences);
    }
  }, [preferences, localPreferences]);

  // Update preferences mutation
  const updatePreferencesMutation = useMutation({
    mutationFn: async (data: any) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notification-preferences'] });
    },
  });

  const handleToggle = (category: string, setting: string) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: !prev[category][setting],
      },
    }));
  };

  const handleThresholdChange = (setting: string, value: number) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      alert_thresholds: {
        ...prev.alert_thresholds,
        [setting]: value,
      },
    }));
  };

  const handleQuietHoursChange = (setting: string, value: any) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      quiet_hours: {
        ...prev.quiet_hours,
        [setting]: value,
      },
    }));
  };

  const handleSave = () => {
    updatePreferencesMutation.mutate(localPreferences);
  };

  if (isLoading || !localPreferences) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
            <div className="space-y-3">
              {[1, 2, 3].map((j) => (
                <div key={j} className="h-12 bg-surface-tertiary rounded"></div>
              ))}
            </div>
          </div>
        ))}
      </div>
    );
  }

  const notificationTypes = [
    {
      key: 'trade_executions',
      label: 'Trade Executions',
      description: 'Notifications when trades are executed',
      icon: Activity,
    },
    {
      key: 'portfolio_alerts',
      label: 'Portfolio Alerts',
      description: 'Alerts about significant portfolio changes',
      icon: TrendingUp,
    },
    {
      key: 'price_alerts',
      label: 'Price Alerts',
      description: 'Notifications for price movements',
      icon: DollarSign,
    },
    {
      key: 'market_news',
      label: 'Market News',
      description: 'Important market news and updates',
      icon: Bell,
    },
    {
      key: 'security_alerts',
      label: 'Security Alerts',
      description: 'Security-related notifications',
      icon: AlertTriangle,
    },
  ];

  return (
    <div className="space-y-8">
      {/* Email Notifications */}
      <div>
        <div className="flex items-center mb-4">
          <Mail className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Email Notifications</h4>
        </div>
        <div className="space-y-3">
          {notificationTypes.map((type) => {
            const Icon = type.icon;
            return (
              <div key={type.key} className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
                <div className="flex items-center">
                  <Icon className="w-5 h-5 text-text-muted mr-3" />
                  <div>
                    <p className="font-medium text-text-primary">{type.label}</p>
                    <p className="text-sm text-text-secondary">{type.description}</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.email_notifications[type.key]}
                    onChange={() => handleToggle('email_notifications', type.key)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>
            );
          })}
          
          {/* Weekly Summary */}
          <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
            <div className="flex items-center">
              <Mail className="w-5 h-5 text-text-muted mr-3" />
              <div>
                <p className="font-medium text-text-primary">Weekly Summary</p>
                <p className="text-sm text-text-secondary">Weekly portfolio performance summary</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.email_notifications.weekly_summary}
                onChange={() => handleToggle('email_notifications', 'weekly_summary')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>

      {/* Push Notifications */}
      <div>
        <div className="flex items-center mb-4">
          <Smartphone className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Push Notifications</h4>
        </div>
        <div className="space-y-3">
          {notificationTypes.filter(type => type.key !== 'weekly_summary').map((type) => {
            const Icon = type.icon;
            return (
              <div key={type.key} className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
                <div className="flex items-center">
                  <Icon className="w-5 h-5 text-text-muted mr-3" />
                  <div>
                    <p className="font-medium text-text-primary">{type.label}</p>
                    <p className="text-sm text-text-secondary">{type.description}</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={localPreferences.push_notifications[type.key]}
                    onChange={() => handleToggle('push_notifications', type.key)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
                </label>
              </div>
            );
          })}
        </div>
      </div>

      {/* Alert Thresholds */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Alert Thresholds</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Portfolio Change (%)
            </label>
            <input
              type="number"
              value={localPreferences.alert_thresholds.portfolio_change_percent}
              onChange={(e) => handleThresholdChange('portfolio_change_percent', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
              min="0"
              max="100"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Position Change (%)
            </label>
            <input
              type="number"
              value={localPreferences.alert_thresholds.position_change_percent}
              onChange={(e) => handleThresholdChange('position_change_percent', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
              min="0"
              max="100"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Price Change (%)
            </label>
            <input
              type="number"
              value={localPreferences.alert_thresholds.price_change_percent}
              onChange={(e) => handleThresholdChange('price_change_percent', parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
              min="0"
              max="100"
              step="0.1"
            />
          </div>
        </div>
      </div>

      {/* Quiet Hours */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">Quiet Hours</h4>
        <div className="p-4 bg-surface-secondary rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="font-medium text-text-primary">Enable Quiet Hours</p>
              <p className="text-sm text-text-secondary">Disable notifications during specified hours</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localPreferences.quiet_hours.enabled}
                onChange={() => handleQuietHoursChange('enabled', !localPreferences.quiet_hours.enabled)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
          
          {localPreferences.quiet_hours.enabled && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Start Time
                </label>
                <input
                  type="time"
                  value={localPreferences.quiet_hours.start_time}
                  onChange={(e) => handleQuietHoursChange('start_time', e.target.value)}
                  className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  End Time
                </label>
                <input
                  type="time"
                  value={localPreferences.quiet_hours.end_time}
                  onChange={(e) => handleQuietHoursChange('end_time', e.target.value)}
                  className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={updatePreferencesMutation.isPending}
          className="bg-primary hover:bg-primary-dark"
        >
          {updatePreferencesMutation.isPending ? 'Saving...' : 'Save Preferences'}
        </Button>
      </div>
    </div>
  );
};

export default NotificationSettings;
