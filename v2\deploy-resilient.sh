#!/bin/bash

# ===========================================
# 🚀 TOKENTRACKER V2 RESILIENT DEPLOYMENT
# ===========================================
# Deployment script optimized for poor network conditions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MAX_RETRIES=3
RETRY_DELAY=30
COMPOSE_FILE="docker-compose.resilient.yml"
SERVICE="tokentracker-dev"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS] [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build       Build the Docker images"
    echo "  up          Start the services"
    echo "  down        Stop the services"
    echo "  restart     Restart the services"
    echo "  logs        Show service logs"
    echo "  status      Show service status"
    echo "  clean       Clean up Docker resources"
    echo ""
    echo "Options:"
    echo "  -s, --service SERVICE   Target service (tokentracker-dev, tokentracker-prod, tokentracker-monitoring)"
    echo "  -f, --file FILE        Docker Compose file (default: docker-compose.resilient.yml)"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 up -s tokentracker-prod"
    echo "  $0 logs -s tokentracker-dev"
}

# Parse command line arguments
COMMAND=""
while [[ $# -gt 0 ]]; do
    case $1 in
        build|up|down|restart|logs|status|clean)
            COMMAND="$1"
            shift
            ;;
        -s|--service)
            SERVICE="$2"
            shift 2
            ;;
        -f|--file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate command
if [ -z "$COMMAND" ]; then
    print_error "No command specified"
    show_usage
    exit 1
fi

# Set environment variables for build
export BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')
export VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo 'unknown')
export VCS_REF=$(git rev-parse HEAD 2>/dev/null || echo 'unknown')
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

print_status "TokenTracker V2 Resilient Deployment"
print_status "Command: $COMMAND"
print_status "Service: $SERVICE"
print_status "Compose file: $COMPOSE_FILE"

# Function to execute with retries
execute_with_retries() {
    local cmd="$1"
    local description="$2"
    local attempt=1
    
    while [ $attempt -le $MAX_RETRIES ]; do
        print_status "$description (attempt $attempt of $MAX_RETRIES)"
        
        if eval "$cmd"; then
            print_success "$description completed successfully!"
            return 0
        else
            print_warning "$description attempt $attempt failed"
            
            if [ $attempt -lt $MAX_RETRIES ]; then
                print_status "Waiting ${RETRY_DELAY} seconds before retry..."
                sleep $RETRY_DELAY
            fi
        fi
        
        attempt=$((attempt + 1))
    done
    
    print_error "$description failed after $MAX_RETRIES attempts!"
    return 1
}

# Pre-flight checks
print_status "Performing pre-flight checks..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running or not accessible"
    exit 1
fi

# Check if Docker Compose is available
if ! docker-compose --version >/dev/null 2>&1; then
    print_error "Docker Compose is not installed or not accessible"
    exit 1
fi

# Check if compose file exists
if [ ! -f "$COMPOSE_FILE" ]; then
    print_error "Docker Compose file not found: $COMPOSE_FILE"
    exit 1
fi

# Execute commands based on input
case $COMMAND in
    build)
        print_status "Building Docker images with network resilience..."
        execute_with_retries "docker-compose -f $COMPOSE_FILE build --parallel --progress plain $SERVICE" "Docker build"
        ;;
        
    up)
        print_status "Starting services..."
        execute_with_retries "docker-compose -f $COMPOSE_FILE up -d $SERVICE" "Service startup"
        
        # Wait for services to be healthy
        print_status "Waiting for services to be healthy..."
        sleep 10
        docker-compose -f $COMPOSE_FILE ps
        ;;
        
    down)
        print_status "Stopping services..."
        docker-compose -f $COMPOSE_FILE down
        print_success "Services stopped successfully!"
        ;;
        
    restart)
        print_status "Restarting services..."
        docker-compose -f $COMPOSE_FILE restart $SERVICE
        print_success "Services restarted successfully!"
        ;;
        
    logs)
        print_status "Showing logs for $SERVICE..."
        docker-compose -f $COMPOSE_FILE logs -f --tail=100 $SERVICE
        ;;
        
    status)
        print_status "Service status:"
        docker-compose -f $COMPOSE_FILE ps
        echo ""
        print_status "Resource usage:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
        ;;
        
    clean)
        print_status "Cleaning up Docker resources..."
        docker-compose -f $COMPOSE_FILE down --volumes --remove-orphans
        docker system prune -f
        docker volume prune -f
        print_success "Cleanup completed!"
        ;;
        
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac

print_success "Operation completed successfully!"
