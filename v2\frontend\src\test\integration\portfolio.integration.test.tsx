import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '@/test/utils';
import { resetAllMocks, mockFetchSuccess, mockFetchError } from '@/test/mocks';
import {
  createMockPortfolios,
  createMockPositions,
  createMockApiResponse,
  createMockPaginatedResponse,
} from '@/test/factories';
import Portfolio from '@/pages/Portfolio';

// Mock the API endpoints
const mockApiEndpoints = {
  portfolios: '/api/portfolios',
  positions: '/api/portfolios/portfolio-1/positions',
  analytics: '/api/portfolios/portfolio-1/analytics',
};

describe('Portfolio Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    resetAllMocks();
    // Reset fetch mock
    global.fetch = jest.fn();
  });

  describe('Portfolio Overview Integration', () => {
    it('should load and display portfolio data from API', async () => {
      const mockPortfolios = createMockPortfolios(3);
      const mockPositions = createMockPositions(5, 'portfolio-1');
      
      // Mock API responses
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));
      
      render(<Portfolio />);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/portfolios'),
          expect.any(Object)
        );
      });

      await waitFor(() => {
        // Should display portfolio data
        mockPortfolios.forEach(portfolio => {
          expect(screen.getByText(portfolio.name)).toBeInTheDocument();
        });
      });
    });

    it('should handle API errors gracefully', async () => {
      mockFetchError(500, 'Internal Server Error');

      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(/error loading portfolio/i)).toBeInTheDocument();
        expect(screen.getByText(/internal server error/i)).toBeInTheDocument();
      });
    });

    it('should retry failed requests', async () => {
      // First call fails, second succeeds
      mockFetchError(500, 'Network Error');
      
      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(/error loading portfolio/i)).toBeInTheDocument();
      });

      // Mock successful retry
      const mockPortfolios = createMockPortfolios(1);
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });
    });
  });

  describe('Portfolio Positions Integration', () => {
    it('should load positions for selected portfolio', async () => {
      const mockPortfolios = createMockPortfolios(2);
      const mockPositions = createMockPositions(3, 'portfolio-1');

      // Mock portfolio list response
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      render(<Portfolio />);

      // Wait for portfolios to load
      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });

      // Mock positions response
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPositions)));

      // Click on first portfolio to view positions
      const portfolioCard = screen.getByText(mockPortfolios[0].name);
      await user.click(portfolioCard);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining(`/api/portfolios/${mockPortfolios[0].id}/positions`),
          expect.any(Object)
        );
      });

      await waitFor(() => {
        // Should display position data
        mockPositions.forEach(position => {
          expect(screen.getByText(position.token_symbol)).toBeInTheDocument();
        });
      });
    });

    it('should update positions when portfolio changes', async () => {
      const mockPortfolios = createMockPortfolios(2);
      const portfolio1Positions = createMockPositions(2, 'portfolio-1');
      const portfolio2Positions = createMockPositions(3, 'portfolio-2');

      // Mock initial data
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });

      // Select first portfolio
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(portfolio1Positions)));
      
      const portfolio1Card = screen.getByText(mockPortfolios[0].name);
      await user.click(portfolio1Card);

      await waitFor(() => {
        portfolio1Positions.forEach(position => {
          expect(screen.getByText(position.token_symbol)).toBeInTheDocument();
        });
      });

      // Switch to second portfolio
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(portfolio2Positions)));
      
      const portfolio2Card = screen.getByText(mockPortfolios[1].name);
      await user.click(portfolio2Card);

      await waitFor(() => {
        portfolio2Positions.forEach(position => {
          expect(screen.getByText(position.token_symbol)).toBeInTheDocument();
        });
      });
    });
  });

  describe('Portfolio Analytics Integration', () => {
    it('should load analytics data for portfolio', async () => {
      const mockPortfolios = createMockPortfolios(1);
      const mockAnalytics = {
        performance_chart: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
          datasets: [{
            label: 'Portfolio Value',
            data: [10000, 12000, 11500, 13000, 15000],
          }],
        },
        risk_metrics: {
          volatility: 0.15,
          sharpe_ratio: 1.2,
          max_drawdown: -0.08,
        },
        allocation: [
          { token_symbol: 'SOL', percentage: 40 },
          { token_symbol: 'ETH', percentage: 35 },
          { token_symbol: 'BTC', percentage: 25 },
        ],
      };

      // Mock portfolio list
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });

      // Navigate to analytics tab
      mockFetchSuccess(createMockApiResponse(mockAnalytics));
      
      const analyticsTab = screen.getByRole('tab', { name: /analytics/i });
      await user.click(analyticsTab);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining(`/api/portfolios/${mockPortfolios[0].id}/analytics`),
          expect.any(Object)
        );
      });

      await waitFor(() => {
        // Should display analytics data
        expect(screen.getByText(/volatility/i)).toBeInTheDocument();
        expect(screen.getByText(/sharpe ratio/i)).toBeInTheDocument();
        expect(screen.getByText(/max drawdown/i)).toBeInTheDocument();
      });
    });
  });

  describe('Portfolio Rebalancing Integration', () => {
    it('should generate and execute rebalancing plan', async () => {
      const mockPortfolios = createMockPortfolios(1);
      const mockRebalancePlan = {
        current_allocation: [
          { token_symbol: 'SOL', current_percentage: 45, target_percentage: 40 },
          { token_symbol: 'ETH', current_percentage: 30, target_percentage: 35 },
          { token_symbol: 'BTC', current_percentage: 25, target_percentage: 25 },
        ],
        trades_required: [
          { token_symbol: 'SOL', action: 'sell', quantity: 5, estimated_value: 500 },
          { token_symbol: 'ETH', action: 'buy', quantity: 2.5, estimated_value: 500 },
        ],
        estimated_fees: 10,
        estimated_slippage: 0.5,
      };

      // Mock portfolio data
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });

      // Open rebalance modal
      const rebalanceButton = screen.getByRole('button', { name: /rebalance/i });
      await user.click(rebalanceButton);

      // Mock rebalance plan generation
      mockFetchSuccess(createMockApiResponse(mockRebalancePlan));

      const generatePlanButton = screen.getByRole('button', { name: /generate plan/i });
      await user.click(generatePlanButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining(`/api/portfolios/${mockPortfolios[0].id}/rebalance`),
          expect.objectContaining({
            method: 'POST',
          })
        );
      });

      await waitFor(() => {
        // Should display rebalance plan
        expect(screen.getByText(/trades required/i)).toBeInTheDocument();
        expect(screen.getByText(/estimated fees/i)).toBeInTheDocument();
      });

      // Execute rebalancing
      mockFetchSuccess(createMockApiResponse({ success: true, trades_executed: 2 }));

      const executeButton = screen.getByRole('button', { name: /execute rebalance/i });
      await user.click(executeButton);

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining(`/api/portfolios/${mockPortfolios[0].id}/rebalance/execute`),
          expect.objectContaining({
            method: 'POST',
          })
        );
      });
    });
  });

  describe('Real-time Data Updates', () => {
    it('should update portfolio data when prices change', async () => {
      const mockPortfolios = createMockPortfolios(1);
      const initialPositions = createMockPositions(2, 'portfolio-1');
      
      // Set initial prices
      initialPositions[0].current_price = 100;
      initialPositions[0].market_value = 10000;
      initialPositions[1].current_price = 200;
      initialPositions[1].market_value = 20000;

      // Mock initial data
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });

      // Load positions
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(initialPositions)));
      
      const portfolioCard = screen.getByText(mockPortfolios[0].name);
      await user.click(portfolioCard);

      await waitFor(() => {
        expect(screen.getByText('$100.00')).toBeInTheDocument();
        expect(screen.getByText('$200.00')).toBeInTheDocument();
      });

      // Simulate price update
      const updatedPositions = [...initialPositions];
      updatedPositions[0].current_price = 110;
      updatedPositions[0].market_value = 11000;
      updatedPositions[1].current_price = 190;
      updatedPositions[1].market_value = 19000;

      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(updatedPositions)));

      // Trigger refresh (this would happen automatically in real app)
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      if (refreshButton) {
        await user.click(refreshButton);
      }

      await waitFor(() => {
        expect(screen.getByText('$110.00')).toBeInTheDocument();
        expect(screen.getByText('$190.00')).toBeInTheDocument();
      });
    });
  });

  describe('Error Recovery', () => {
    it('should handle network errors and retry automatically', async () => {
      // Simulate network failure
      mockFetchError(0, 'Network Error');

      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument();
      });

      // Simulate network recovery
      const mockPortfolios = createMockPortfolios(1);
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      // Auto-retry should happen (or manual retry)
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });
    });

    it('should handle partial data loading failures', async () => {
      const mockPortfolios = createMockPortfolios(1);
      
      // Portfolio list loads successfully
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });

      // Positions fail to load
      mockFetchError(500, 'Positions Service Unavailable');

      const portfolioCard = screen.getByText(mockPortfolios[0].name);
      await user.click(portfolioCard);

      await waitFor(() => {
        expect(screen.getByText(/error loading positions/i)).toBeInTheDocument();
        // Portfolio overview should still be visible
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });
    });
  });

  describe('Data Consistency', () => {
    it('should maintain data consistency across components', async () => {
      const mockPortfolios = createMockPortfolios(1);
      const mockPositions = createMockPositions(2, 'portfolio-1');
      
      // Calculate expected totals
      const expectedTotalValue = mockPositions.reduce((sum, pos) => sum + pos.market_value, 0);

      // Mock data
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPortfolios)));

      render(<Portfolio />);

      await waitFor(() => {
        expect(screen.getByText(mockPortfolios[0].name)).toBeInTheDocument();
      });

      // Load positions
      mockFetchSuccess(createMockApiResponse(createMockPaginatedResponse(mockPositions)));
      
      const portfolioCard = screen.getByText(mockPortfolios[0].name);
      await user.click(portfolioCard);

      await waitFor(() => {
        // Check that totals match across components
        const totalValueElements = screen.getAllByText(`$${expectedTotalValue.toLocaleString()}`);
        expect(totalValueElements.length).toBeGreaterThan(0);
      });
    });
  });
});
