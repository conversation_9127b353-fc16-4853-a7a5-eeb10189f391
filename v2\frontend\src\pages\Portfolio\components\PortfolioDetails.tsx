import React from 'react';
import Card from '@/components/ui/Card';
import { Portfolio, PerformanceMetrics } from '@/types';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface PortfolioDetailsProps {
  portfolio: Portfolio;
  performance: PerformanceMetrics | null;
  isLoading: boolean;
}

const PortfolioDetails: React.FC<PortfolioDetailsProps> = ({
  portfolio,
  performance,
  isLoading,
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Mock chart data - in real implementation, this would come from the API
  const chartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Portfolio Value',
        data: [
          portfolio.initial_balance,
          portfolio.initial_balance * 1.05,
          portfolio.initial_balance * 1.03,
          portfolio.initial_balance * 1.08,
          portfolio.initial_balance * 1.12,
          portfolio.current_balance,
        ],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Portfolio Performance',
      },
    },
    scales: {
      y: {
        beginAtZero: false,
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          },
        },
      },
    },
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="p-4">
              <div className="animate-pulse">
                <div className="h-4 bg-surface-tertiary rounded mb-2"></div>
                <div className="h-6 bg-surface-tertiary rounded"></div>
              </div>
            </Card>
          ))}
        </div>
        <Card className="p-6">
          <div className="animate-pulse">
            <div className="h-64 bg-surface-tertiary rounded"></div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Current Balance</div>
          <div className="text-2xl font-bold text-text-primary">
            {formatCurrency(portfolio.current_balance)}
          </div>
          <div className="text-sm text-text-muted">
            Initial: {formatCurrency(portfolio.initial_balance)}
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Total P&L</div>
          <div
            className={`text-2xl font-bold ${
              portfolio.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatCurrency(portfolio.total_pnl)}
          </div>
          <div
            className={`text-sm ${
              portfolio.total_pnl_percentage >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatPercentage(portfolio.total_pnl_percentage)}
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Daily P&L</div>
          <div
            className={`text-2xl font-bold ${
              portfolio.daily_pnl >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatCurrency(portfolio.daily_pnl)}
          </div>
          <div
            className={`text-sm ${
              portfolio.daily_pnl_percentage >= 0 ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {formatPercentage(portfolio.daily_pnl_percentage)}
          </div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Active Positions</div>
          <div className="text-2xl font-bold text-text-primary">
            {portfolio.active_positions}
          </div>
          <div className="text-sm text-text-muted">
            {portfolio.total_trades} total trades
          </div>
        </Card>
      </div>

      {/* Performance Chart */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Performance Chart</h3>
        <div className="h-64">
          <Line data={chartData} options={chartOptions} />
        </div>
      </Card>

      {/* Additional Metrics */}
      {performance && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Win Rate</div>
            <div className="text-xl font-bold text-text-primary">
              {formatPercentage(portfolio.win_rate)}
            </div>
            <div className="text-sm text-text-muted">
              {performance.winning_trades}W / {performance.losing_trades}L
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Sharpe Ratio</div>
            <div className="text-xl font-bold text-text-primary">
              {portfolio.sharpe_ratio.toFixed(2)}
            </div>
            <div className="text-sm text-text-muted">Risk-adjusted return</div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Max Drawdown</div>
            <div className="text-xl font-bold text-red-600">
              {formatPercentage(portfolio.max_drawdown)}
            </div>
            <div className="text-sm text-text-muted">Largest peak-to-trough decline</div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Volatility</div>
            <div className="text-xl font-bold text-text-primary">
              {formatPercentage(performance.volatility)}
            </div>
            <div className="text-sm text-text-muted">Price volatility</div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Annualized Return</div>
            <div
              className={`text-xl font-bold ${
                performance.annualized_return >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {formatPercentage(performance.annualized_return)}
            </div>
            <div className="text-sm text-text-muted">Yearly return rate</div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Risk Level</div>
            <div className="flex items-center space-x-2">
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  portfolio.risk_level === 'low'
                    ? 'bg-green-100 text-green-800'
                    : portfolio.risk_level === 'medium'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {portfolio.risk_level.toUpperCase()}
              </span>
            </div>
            <div className="text-sm text-text-muted mt-1">Portfolio risk assessment</div>
          </Card>
        </div>
      )}

      {/* Portfolio Info */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Portfolio Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <div className="text-sm text-text-secondary mb-1">Created</div>
            <div className="text-text-primary">{formatDate(portfolio.created_at)}</div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Last Updated</div>
            <div className="text-text-primary">{formatDate(portfolio.updated_at)}</div>
          </div>
          <div>
            <div className="text-sm text-text-secondary mb-1">Status</div>
            <div className="flex items-center space-x-2">
              <span
                className={`inline-block w-2 h-2 rounded-full ${
                  portfolio.is_active ? 'bg-green-500' : 'bg-gray-400'
                }`}
              />
              <span className="text-text-primary">
                {portfolio.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
          {portfolio.description && (
            <div className="md:col-span-2">
              <div className="text-sm text-text-secondary mb-1">Description</div>
              <div className="text-text-primary">{portfolio.description}</div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default PortfolioDetails;
