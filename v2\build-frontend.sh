#!/bin/bash

# ===========================================
# 🚀 TOKENTRACKER V2 FRONTEND BUILD SCRIPT
# ===========================================
# Network-resilient frontend build with Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_DIR="frontend"
IMAGE_NAME="tokentracker-v2-frontend"
CONTAINER_NAME="tokentracker-v2-frontend-build"

echo -e "${BLUE}🚀 Starting TokenTracker v2 Frontend Build${NC}"
echo "=================================================="

# Check if frontend directory exists
if [ ! -d "$FRONTEND_DIR" ]; then
    echo -e "${RED}❌ Frontend directory not found: $FRONTEND_DIR${NC}"
    exit 1
fi

cd "$FRONTEND_DIR"

echo -e "${YELLOW}📦 Building Docker image for frontend...${NC}"

# Build the Docker image with network resilience
docker build \
    --target production \
    --tag "$IMAGE_NAME:latest" \
    --tag "$IMAGE_NAME:$(date +%Y%m%d-%H%M%S)" \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --progress=plain \
    . || {
    echo -e "${RED}❌ Docker build failed${NC}"
    exit 1
}

echo -e "${GREEN}✅ Frontend Docker image built successfully${NC}"

# Test the built image
echo -e "${YELLOW}🧪 Testing the built image...${NC}"

# Remove existing test container if it exists
docker rm -f "$CONTAINER_NAME-test" 2>/dev/null || true

# Run a test container
docker run -d \
    --name "$CONTAINER_NAME-test" \
    --publish 3000:80 \
    "$IMAGE_NAME:latest" || {
    echo -e "${RED}❌ Failed to start test container${NC}"
    exit 1
}

# Wait for container to be ready
echo -e "${YELLOW}⏳ Waiting for container to be ready...${NC}"
sleep 5

# Test health check
if docker exec "$CONTAINER_NAME-test" /usr/local/bin/docker-healthcheck.sh; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed${NC}"
    docker logs "$CONTAINER_NAME-test"
    docker rm -f "$CONTAINER_NAME-test"
    exit 1
fi

# Clean up test container
docker rm -f "$CONTAINER_NAME-test"

echo -e "${GREEN}✅ Frontend build completed successfully!${NC}"
echo ""
echo "🐳 Docker image: $IMAGE_NAME:latest"
echo "🌐 To run: docker run -p 3000:80 $IMAGE_NAME:latest"
echo "📊 To start with backend: docker-compose up frontend"

cd ..

echo -e "${BLUE}=================================================="
echo -e "🎉 Frontend build process completed successfully!${NC}"
