import { ApiService } from './api';
import {
  TradingSignal,
  PaginatedResponse,
  PaginationConfig,
  FilterConfig,
  SortConfig,
} from '@/types';

/**
 * Trading signals service for managing trading signals
 */
export class SignalsService {
  private static readonly BASE_URL = '/api/v1/signals';

  /**
   * Get active trading signals
   */
  static async getActiveSignals(params?: {
    pagination?: PaginationConfig;
    filters?: FilterConfig;
    sort?: SortConfig;
  }): Promise<PaginatedResponse<TradingSignal>> {
    const queryParams = {
      page: params?.pagination?.page || 1,
      page_size: params?.pagination?.page_size || 20,
      ...params?.filters,
      sort_by: params?.sort?.field,
      sort_order: params?.sort?.direction,
    };

    return ApiService.get<PaginatedResponse<TradingSignal>>(
      `${this.BASE_URL}/active`,
      queryParams
    );
  }

  /**
   * Get signal history
   */
  static async getSignalHistory(params?: {
    pagination?: PaginationConfig;
    filters?: FilterConfig;
    sort?: SortConfig;
  }): Promise<PaginatedResponse<TradingSignal>> {
    const queryParams = {
      page: params?.pagination?.page || 1,
      page_size: params?.pagination?.page_size || 20,
      ...params?.filters,
      sort_by: params?.sort?.field,
      sort_order: params?.sort?.direction,
    };

    return ApiService.get<PaginatedResponse<TradingSignal>>(
      `${this.BASE_URL}/history`,
      queryParams
    );
  }

  /**
   * Get signal by ID
   */
  static async getSignal(signalId: string): Promise<TradingSignal> {
    return ApiService.get<TradingSignal>(`${this.BASE_URL}/${signalId}`);
  }

  /**
   * Generate new trading signal
   */
  static async generateSignal(signalData: {
    token_address: string;
    analysis_type?: 'technical' | 'fundamental' | 'combined';
    timeframe?: '1H' | '4H' | '1D' | '1W';
    risk_level?: 'low' | 'medium' | 'high';
  }): Promise<TradingSignal> {
    return ApiService.post<TradingSignal>(`${this.BASE_URL}/generate`, signalData);
  }

  /**
   * Update signal status
   */
  static async updateSignalStatus(
    signalId: string,
    status: 'active' | 'executed' | 'expired' | 'cancelled'
  ): Promise<TradingSignal> {
    return ApiService.patch<TradingSignal>(`${this.BASE_URL}/${signalId}`, {
      status,
    });
  }

  /**
   * Execute trading signal
   */
  static async executeSignal(
    signalId: string,
    executionData: {
      portfolio_id: string;
      quantity?: number;
      price_limit?: number;
      notes?: string;
    }
  ): Promise<{
    trade_id: string;
    execution_status: 'pending' | 'executed' | 'failed';
    message: string;
  }> {
    return ApiService.post(
      `${this.BASE_URL}/${signalId}/execute`,
      executionData
    );
  }

  /**
   * Get signal configuration
   */
  static async getSignalConfig(): Promise<{
    technical_indicators: {
      rsi_period: number;
      rsi_overbought: number;
      rsi_oversold: number;
      macd_fast: number;
      macd_slow: number;
      macd_signal: number;
      bb_period: number;
      bb_std_dev: number;
    };
    risk_parameters: {
      max_risk_per_trade: number;
      stop_loss_percentage: number;
      take_profit_ratio: number;
      max_open_positions: number;
    };
    filters: {
      min_market_cap: number;
      min_volume_24h: number;
      max_volatility: number;
      blacklisted_tokens: string[];
    };
  }> {
    return ApiService.get(`${this.BASE_URL}/config`);
  }

  /**
   * Update signal configuration
   */
  static async updateSignalConfig(configData: {
    technical_indicators?: {
      rsi_period?: number;
      rsi_overbought?: number;
      rsi_oversold?: number;
      macd_fast?: number;
      macd_slow?: number;
      macd_signal?: number;
      bb_period?: number;
      bb_std_dev?: number;
    };
    risk_parameters?: {
      max_risk_per_trade?: number;
      stop_loss_percentage?: number;
      take_profit_ratio?: number;
      max_open_positions?: number;
    };
    filters?: {
      min_market_cap?: number;
      min_volume_24h?: number;
      max_volatility?: number;
      blacklisted_tokens?: string[];
    };
  }): Promise<void> {
    return ApiService.put(`${this.BASE_URL}/config`, configData);
  }

  /**
   * Get signal performance statistics
   */
  static async getSignalPerformance(params?: {
    timeframe?: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y';
    signal_type?: 'buy' | 'sell' | 'hold';
    token_address?: string;
  }): Promise<{
    total_signals: number;
    successful_signals: number;
    success_rate: number;
    average_return: number;
    best_signal: {
      signal_id: string;
      return_percentage: number;
      token_symbol: string;
    };
    worst_signal: {
      signal_id: string;
      return_percentage: number;
      token_symbol: string;
    };
    performance_by_type: {
      signal_type: 'buy' | 'sell' | 'hold';
      count: number;
      success_rate: number;
      average_return: number;
    }[];
  }> {
    return ApiService.get(`${this.BASE_URL}/performance`, params);
  }

  /**
   * Backtest signal strategy
   */
  static async backtestStrategy(backtestData: {
    strategy_config: {
      technical_indicators: Record<string, number>;
      risk_parameters: Record<string, number>;
    };
    timeframe: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y';
    initial_balance: number;
    tokens?: string[];
  }): Promise<{
    backtest_id: string;
    results: {
      total_return: number;
      total_return_percentage: number;
      max_drawdown: number;
      sharpe_ratio: number;
      win_rate: number;
      total_trades: number;
      profitable_trades: number;
    };
    trades: {
      date: string;
      token_symbol: string;
      action: 'buy' | 'sell';
      price: number;
      quantity: number;
      pnl: number;
    }[];
  }> {
    return ApiService.post(`${this.BASE_URL}/backtest`, backtestData);
  }

  /**
   * Subscribe to signal notifications
   */
  static async subscribeToSignals(subscriptionData: {
    signal_types: ('buy' | 'sell' | 'hold')[];
    tokens?: string[];
    min_confidence?: number;
    notification_methods: ('email' | 'push' | 'webhook')[];
  }): Promise<void> {
    return ApiService.post(`${this.BASE_URL}/subscribe`, subscriptionData);
  }

  /**
   * Unsubscribe from signal notifications
   */
  static async unsubscribeFromSignals(): Promise<void> {
    return ApiService.delete(`${this.BASE_URL}/subscribe`);
  }
}
