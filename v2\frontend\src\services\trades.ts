import { ApiService } from './api';
import {
  Trade,
  PaginatedResponse,
  PaginationConfig,
  FilterConfig,
  SortConfig,
} from '@/types';

/**
 * Trades service for managing trade history and analytics
 */
export class TradesService {
  private static readonly BASE_URL = '/api/v1/web/trades';

  /**
   * Get trade history with pagination and filtering
   */
  static async getTradeHistory(params?: {
    pagination?: PaginationConfig;
    filters?: FilterConfig;
    sort?: SortConfig;
  }): Promise<PaginatedResponse<Trade>> {
    const queryParams = {
      page: params?.pagination?.page || 1,
      page_size: params?.pagination?.page_size || 20,
      ...params?.filters,
      sort_by: params?.sort?.field,
      sort_order: params?.sort?.direction,
    };

    return ApiService.get<PaginatedResponse<Trade>>(this.BASE_URL, queryParams);
  }

  /**
   * Get trade by ID
   */
  static async getTrade(tradeId: string): Promise<Trade> {
    return ApiService.get<Trade>(`${this.BASE_URL}/${tradeId}`);
  }

  /**
   * Get trade analytics
   */
  static async getTradeAnalytics(params?: {
    timeframe?: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL';
    portfolio_id?: string;
    token_address?: string;
  }): Promise<{
    summary: {
      total_trades: number;
      winning_trades: number;
      losing_trades: number;
      win_rate: number;
      total_pnl: number;
      total_pnl_percentage: number;
      average_win: number;
      average_loss: number;
      profit_factor: number;
      largest_win: number;
      largest_loss: number;
    };
    performance_by_month: {
      month: string;
      trades: number;
      pnl: number;
      win_rate: number;
    }[];
    performance_by_token: {
      token_symbol: string;
      trades: number;
      pnl: number;
      win_rate: number;
    }[];
    performance_by_signal_type: {
      signal_type: 'buy' | 'sell' | 'manual';
      trades: number;
      pnl: number;
      win_rate: number;
    }[];
  }> {
    return ApiService.get(`${this.BASE_URL}/analytics`, params);
  }

  /**
   * Export trade history
   */
  static async exportTrades(params: {
    format: 'csv' | 'json' | 'pdf';
    timeframe?: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL';
    portfolio_id?: string;
    filters?: FilterConfig;
  }): Promise<Blob> {
    const queryParams: Record<string, string> = {
      format: params.format,
      ...(params.timeframe && { timeframe: params.timeframe }),
      ...(params.portfolio_id && { portfolio_id: params.portfolio_id }),
      ...params.filters,
    };

    const response = await fetch(
      `${this.BASE_URL}/export?${new URLSearchParams(queryParams).toString()}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('access_token')}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  }

  /**
   * Create manual trade
   */
  static async createManualTrade(tradeData: {
    portfolio_id: string;
    token_address: string;
    trade_type: 'buy' | 'sell';
    quantity: number;
    price: number;
    notes?: string;
  }): Promise<Trade> {
    return ApiService.post<Trade>(`${this.BASE_URL}/manual`, tradeData);
  }

  /**
   * Update trade notes
   */
  static async updateTradeNotes(tradeId: string, notes: string): Promise<Trade> {
    return ApiService.patch<Trade>(`${this.BASE_URL}/${tradeId}`, { notes });
  }

  /**
   * Cancel pending trade
   */
  static async cancelTrade(tradeId: string): Promise<Trade> {
    return ApiService.patch<Trade>(`${this.BASE_URL}/${tradeId}/cancel`);
  }

  /**
   * Get trade execution details
   */
  static async getTradeExecution(tradeId: string): Promise<{
    execution_details: {
      order_id: string;
      exchange: string;
      execution_time: string;
      execution_price: number;
      slippage: number;
      fees_breakdown: {
        exchange_fee: number;
        network_fee: number;
        platform_fee: number;
        total_fees: number;
      };
    };
    market_conditions: {
      market_price_at_execution: number;
      volume_24h: number;
      volatility: number;
      liquidity_score: number;
    };
  }> {
    return ApiService.get(`${this.BASE_URL}/${tradeId}/execution`);
  }

  /**
   * Get trade performance comparison
   */
  static async getTradeComparison(params: {
    trade_ids: string[];
    metrics: ('pnl' | 'duration' | 'fees' | 'slippage')[];
  }): Promise<{
    comparison: {
      trade_id: string;
      token_symbol: string;
      pnl: number;
      pnl_percentage: number;
      duration_hours: number;
      total_fees: number;
      slippage: number;
      execution_quality: 'excellent' | 'good' | 'fair' | 'poor';
    }[];
    summary: {
      best_performing: string;
      worst_performing: string;
      average_pnl: number;
      average_duration: number;
      average_fees: number;
    };
  }> {
    return ApiService.post(`${this.BASE_URL}/compare`, params);
  }

  /**
   * Get trade recommendations based on history
   */
  static async getTradeRecommendations(params?: {
    portfolio_id?: string;
    risk_level?: 'low' | 'medium' | 'high';
    timeframe?: '1D' | '1W' | '1M';
  }): Promise<{
    recommendations: {
      type: 'position_size' | 'timing' | 'token_selection' | 'risk_management';
      title: string;
      description: string;
      impact: 'high' | 'medium' | 'low';
      confidence: number;
    }[];
    insights: {
      best_trading_hours: string[];
      most_profitable_tokens: string[];
      optimal_position_sizes: {
        token_symbol: string;
        recommended_size: number;
        reason: string;
      }[];
    };
  }> {
    return ApiService.get(`${this.BASE_URL}/recommendations`, params);
  }
}
