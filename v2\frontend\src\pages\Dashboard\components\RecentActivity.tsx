import React from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, TrendingDown, ArrowRight, Clock, Target } from 'lucide-react';
import { TradingSignal, Trade } from '@/types';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

interface RecentActivityProps {
  title: string;
  data: TradingSignal[] | Trade[];
  type: 'signals' | 'trades';
}

/**
 * Recent activity component showing signals or trades
 */
const RecentActivity: React.FC<RecentActivityProps> = ({ title, data, type }) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return date.toLocaleDateString();
  };

  const getSignalIcon = (signalType: string) => {
    switch (signalType) {
      case 'buy':
        return <TrendingUp className="w-4 h-4 text-accent-green" />;
      case 'sell':
        return <TrendingDown className="w-4 h-4 text-accent-red" />;
      default:
        return <Target className="w-4 h-4 text-accent-yellow" />;
    }
  };

  const getSignalColor = (signalType: string) => {
    switch (signalType) {
      case 'buy':
        return 'text-accent-green';
      case 'sell':
        return 'text-accent-red';
      default:
        return 'text-accent-yellow';
    }
  };

  const getTradeIcon = (tradeType: string) => {
    return tradeType === 'buy' ? (
      <TrendingUp className="w-4 h-4 text-accent-green" />
    ) : (
      <TrendingDown className="w-4 h-4 text-accent-red" />
    );
  };

  const getTradeColor = (tradeType: string) => {
    return tradeType === 'buy' ? 'text-accent-green' : 'text-accent-red';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'executed':
      case 'active':
        return 'text-accent-green';
      case 'pending':
        return 'text-accent-yellow';
      case 'failed':
      case 'cancelled':
      case 'expired':
        return 'text-accent-red';
      default:
        return 'text-text-muted';
    }
  };

  const renderSignalItem = (signal: TradingSignal) => (
    <div key={signal.id} className="flex items-center justify-between p-3 hover:bg-bg-primary rounded-lg transition-colors">
      <div className="flex items-center flex-1">
        <div className="p-2 bg-bg-tertiary rounded-lg mr-3">
          {getSignalIcon(signal.signal_type)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="text-text-primary font-medium text-sm truncate">
              {signal.token_symbol}
            </p>
            <span className={`text-xs font-medium uppercase ${getSignalColor(signal.signal_type)}`}>
              {signal.signal_type}
            </span>
          </div>
          <div className="flex items-center space-x-2 mt-1">
            <p className="text-text-muted text-xs">
              Target: {formatCurrency(signal.price_target)}
            </p>
            <span className="text-text-muted">•</span>
            <p className="text-text-muted text-xs">
              Confidence: {(signal.confidence * 100).toFixed(0)}%
            </p>
          </div>
        </div>
      </div>
      <div className="text-right ml-4">
        <p className={`text-xs font-medium ${getStatusColor(signal.status)}`}>
          {signal.status}
        </p>
        <p className="text-text-muted text-xs flex items-center">
          <Clock className="w-3 h-3 mr-1" />
          {formatTime(signal.created_at)}
        </p>
      </div>
    </div>
  );

  const renderTradeItem = (trade: Trade) => (
    <div key={trade.id} className="flex items-center justify-between p-3 hover:bg-bg-primary rounded-lg transition-colors">
      <div className="flex items-center flex-1">
        <div className="p-2 bg-bg-tertiary rounded-lg mr-3">
          {getTradeIcon(trade.trade_type)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="text-text-primary font-medium text-sm truncate">
              {trade.token_symbol}
            </p>
            <span className={`text-xs font-medium uppercase ${getTradeColor(trade.trade_type)}`}>
              {trade.trade_type}
            </span>
          </div>
          <div className="flex items-center space-x-2 mt-1">
            <p className="text-text-muted text-xs">
              {trade.quantity.toFixed(4)} @ {formatCurrency(trade.price)}
            </p>
            {trade.pnl !== undefined && (
              <>
                <span className="text-text-muted">•</span>
                <p className={`text-xs font-medium ${trade.pnl >= 0 ? 'text-accent-green' : 'text-accent-red'}`}>
                  {trade.pnl >= 0 ? '+' : ''}{formatCurrency(trade.pnl)}
                </p>
              </>
            )}
          </div>
        </div>
      </div>
      <div className="text-right ml-4">
        <p className={`text-xs font-medium ${getStatusColor(trade.status)}`}>
          {trade.status}
        </p>
        <p className="text-text-muted text-xs flex items-center">
          <Clock className="w-3 h-3 mr-1" />
          {formatTime(trade.created_at)}
        </p>
      </div>
    </div>
  );

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-text-primary">{title}</h2>
        <Link to={`/${type}`}>
          <Button variant="ghost" size="sm" rightIcon={<ArrowRight className="w-4 h-4" />}>
            View All
          </Button>
        </Link>
      </div>

      <div className="space-y-2">
        {data.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-text-muted">No recent {type} available</p>
          </div>
        ) : (
          data.slice(0, 5).map((item) =>
            type === 'signals'
              ? renderSignalItem(item as TradingSignal)
              : renderTradeItem(item as Trade)
          )
        )}
      </div>
    </Card>
  );
};

export default RecentActivity;
