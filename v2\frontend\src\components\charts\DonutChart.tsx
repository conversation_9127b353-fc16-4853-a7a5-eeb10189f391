import React from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  <PERSON><PERSON><PERSON>,
  Legend,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';

ChartJS.register(ArcElement, Tooltip, Legend);

interface DonutChartData {
  label: string;
  value: number;
  color?: string;
}

interface DonutChartProps {
  data: DonutChartData[];
  title?: string;
  height?: number;
  showLegend?: boolean;
  centerText?: string;
  centerSubtext?: string;
  formatValue?: (value: number) => string;
}

/**
 * Donut chart component for displaying proportional data
 */
const DonutChart: React.FC<DonutChartProps> = ({
  data,
  title,
  height = 300,
  showLegend = true,
  centerText,
  centerSubtext,
  formatValue = (value) => value.toLocaleString(),
}) => {
  // Default colors for chart segments
  const defaultColors = [
    '#3b82f6', // blue
    '#10b981', // green
    '#ef4444', // red
    '#f59e0b', // yellow
    '#8b5cf6', // purple
    '#06b6d4', // cyan
    '#f97316', // orange
    '#84cc16', // lime
  ];

  // Prepare chart data
  const chartData = {
    labels: data.map(item => item.label),
    datasets: [
      {
        data: data.map(item => item.value),
        backgroundColor: data.map((item, index) => 
          item.color || defaultColors[index % defaultColors.length]
        ),
        borderColor: '#1e293b',
        borderWidth: 2,
        hoverBorderWidth: 3,
      },
    ],
  };

  // Chart options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '60%',
    plugins: {
      legend: {
        display: showLegend,
        position: 'bottom' as const,
        labels: {
          color: '#cbd5e1',
          font: {
            size: 12,
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle',
        },
      },
      tooltip: {
        backgroundColor: '#1e293b',
        titleColor: '#f8fafc',
        bodyColor: '#cbd5e1',
        borderColor: '#475569',
        borderWidth: 1,
        cornerRadius: 8,
        callbacks: {
          label: (context: any) => {
            const label = context.label || '';
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${formatValue(value)} (${percentage}%)`;
          },
        },
      },
    },
    elements: {
      arc: {
        hoverBackgroundColor: (context: any) => {
          const color = context.element.options.backgroundColor;
          return color + 'CC'; // Add transparency on hover
        },
      },
    },
  };

  // Custom plugin for center text
  const centerTextPlugin = {
    id: 'centerText',
    beforeDraw: (chart: any) => {
      if (!centerText) return;

      const { ctx, width, height } = chart;
      const centerX = width / 2;
      const centerY = height / 2;

      ctx.save();
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Main center text
      ctx.fillStyle = '#f8fafc';
      ctx.font = 'bold 24px sans-serif';
      ctx.fillText(centerText, centerX, centerY - 10);

      // Subtext
      if (centerSubtext) {
        ctx.fillStyle = '#64748b';
        ctx.font = '14px sans-serif';
        ctx.fillText(centerSubtext, centerX, centerY + 15);
      }

      ctx.restore();
    },
  };

  return (
    <div className="w-full">
      {title && (
        <h3 className="text-lg font-semibold text-text-primary mb-4 text-center">
          {title}
        </h3>
      )}
      <div style={{ height }}>
        <Doughnut 
          data={chartData} 
          options={options} 
          plugins={[centerTextPlugin]}
        />
      </div>
    </div>
  );
};

export default DonutChart;
