import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { DashboardService } from '@/services/dashboard';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LineChart from '@/components/charts/LineChart';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

type TimeframeOption = '1D' | '1W' | '1M' | '3M' | '1Y';

/**
 * Performance chart component with timeframe selection
 */
const PerformanceChart: React.FC = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<TimeframeOption>('1M');

  const timeframeOptions: { value: TimeframeOption; label: string }[] = [
    { value: '1D', label: '1D' },
    { value: '1W', label: '1W' },
    { value: '1M', label: '1M' },
    { value: '3M', label: '3M' },
    { value: '1Y', label: '1Y' },
  ];

  // Fetch performance chart data
  const {
    data: chartData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['performance-chart', selectedTimeframe],
    queryFn: () => DashboardService.getPerformanceChart({ timeframe: selectedTimeframe }),
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  const formatValue = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatLabel = (timestamp: string) => {
    const date = new Date(timestamp);
    
    switch (selectedTimeframe) {
      case '1D':
        return date.toLocaleTimeString('en-US', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      case '1W':
        return date.toLocaleDateString('en-US', { 
          weekday: 'short' 
        });
      case '1M':
      case '3M':
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric' 
        });
      case '1Y':
        return date.toLocaleDateString('en-US', { 
          month: 'short', 
          year: '2-digit' 
        });
      default:
        return date.toLocaleDateString('en-US');
    }
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-text-primary">
            Portfolio Performance
          </h2>
          <p className="text-text-muted text-sm mt-1">
            Track your portfolio value over time
          </p>
        </div>
        
        {/* Timeframe selector */}
        <div className="flex space-x-1 bg-bg-primary rounded-lg p-1">
          {timeframeOptions.map((option) => (
            <Button
              key={option.value}
              variant={selectedTimeframe === option.value ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setSelectedTimeframe(option.value)}
              className="px-3 py-1"
            >
              {option.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Chart content */}
      <div className="h-80">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-accent-red mb-2">Failed to load chart data</p>
              <p className="text-text-muted text-sm">
                {error instanceof Error ? error.message : 'An unexpected error occurred'}
              </p>
            </div>
          </div>
        ) : chartData && chartData.length > 0 ? (
          <LineChart
            data={chartData}
            height={320}
            color="#3b82f6"
            backgroundColor="rgba(59, 130, 246, 0.1)"
            fill={true}
            formatValue={formatValue}
            formatLabel={formatLabel}
            showGrid={true}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <p className="text-text-muted">No performance data available</p>
          </div>
        )}
      </div>

      {/* Chart statistics */}
      {chartData && chartData.length > 0 && (
        <div className="mt-6 pt-6 border-t border-border">
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-text-muted text-sm">Starting Value</p>
              <p className="text-text-primary font-semibold">
                {formatValue(chartData[0]?.value || 0)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-text-muted text-sm">Current Value</p>
              <p className="text-text-primary font-semibold">
                {formatValue(chartData[chartData.length - 1]?.value || 0)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-text-muted text-sm">Change</p>
              {(() => {
                const start = chartData[0]?.value || 0;
                const end = chartData[chartData.length - 1]?.value || 0;
                const change = end - start;
                const changePercent = start > 0 ? (change / start) * 100 : 0;
                const isPositive = change >= 0;
                
                return (
                  <p className={`font-semibold ${isPositive ? 'text-accent-green' : 'text-accent-red'}`}>
                    {isPositive ? '+' : ''}{formatValue(change)} ({isPositive ? '+' : ''}{changePercent.toFixed(2)}%)
                  </p>
                );
              })()}
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export default PerformanceChart;
