#!/usr/bin/env node

/**
 * Comprehensive test runner for the TokenTracker frontend
 * Runs unit tests, integration tests, and E2E tests in sequence
 */

import { execSync } from 'child_process';
import { existsSync, mkdirSync } from 'fs';
import path from 'path';

interface TestResult {
  type: string;
  success: boolean;
  duration: number;
  coverage?: number;
  error?: string;
}

class TestRunner {
  private results: TestResult[] = [];
  private startTime = Date.now();

  constructor() {
    this.ensureDirectories();
  }

  private ensureDirectories() {
    const dirs = [
      'test-results',
      'coverage',
      'playwright-report',
    ];

    dirs.forEach(dir => {
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }
    });
  }

  private async runCommand(command: string, description: string): Promise<TestResult> {
    console.log(`\n🚀 ${description}...`);
    console.log(`📝 Command: ${command}`);
    
    const startTime = Date.now();
    
    try {
      const output = execSync(command, { 
        stdio: 'inherit',
        encoding: 'utf8',
        timeout: 300000, // 5 minutes timeout
      });
      
      const duration = Date.now() - startTime;
      console.log(`✅ ${description} completed in ${duration}ms`);
      
      return {
        type: description,
        success: true,
        duration,
      };
    } catch (error: any) {
      const duration = Date.now() - startTime;
      console.error(`❌ ${description} failed after ${duration}ms`);
      console.error(`Error: ${error.message}`);
      
      return {
        type: description,
        success: false,
        duration,
        error: error.message,
      };
    }
  }

  private async runUnitTests(): Promise<TestResult> {
    return this.runCommand(
      'npm run test:unit -- --coverage --watchAll=false --passWithNoTests',
      'Unit Tests'
    );
  }

  private async runIntegrationTests(): Promise<TestResult> {
    return this.runCommand(
      'npm run test:integration -- --coverage --watchAll=false --passWithNoTests',
      'Integration Tests'
    );
  }

  private async runE2ETests(): Promise<TestResult> {
    // First, build the application
    const buildResult = await this.runCommand(
      'npm run build',
      'Build Application'
    );

    if (!buildResult.success) {
      return {
        type: 'E2E Tests',
        success: false,
        duration: buildResult.duration,
        error: 'Build failed, skipping E2E tests',
      };
    }

    // Then run E2E tests
    return this.runCommand(
      'npx playwright test',
      'E2E Tests'
    );
  }

  private async runLinting(): Promise<TestResult> {
    return this.runCommand(
      'npm run lint',
      'ESLint'
    );
  }

  private async runTypeChecking(): Promise<TestResult> {
    return this.runCommand(
      'npx tsc --noEmit',
      'TypeScript Type Checking'
    );
  }

  private generateSummary() {
    const totalDuration = Date.now() - this.startTime;
    const successful = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;

    console.log('\n' + '='.repeat(80));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(80));
    console.log(`⏱️  Total Duration: ${totalDuration}ms (${(totalDuration / 1000).toFixed(2)}s)`);
    console.log(`✅ Successful: ${successful}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${((successful / this.results.length) * 100).toFixed(1)}%`);
    
    console.log('\n📋 Detailed Results:');
    this.results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const duration = `${result.duration}ms`;
      console.log(`${status} ${result.type.padEnd(25)} ${duration.padStart(10)}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });

    console.log('\n📁 Generated Reports:');
    console.log('   • Unit Test Coverage: ./coverage/lcov-report/index.html');
    console.log('   • Integration Test Coverage: ./coverage/integration/index.html');
    console.log('   • E2E Test Report: ./playwright-report/index.html');
    console.log('   • Test Results: ./test-results/');

    if (failed > 0) {
      console.log('\n❌ Some tests failed. Please check the output above for details.');
      process.exit(1);
    } else {
      console.log('\n🎉 All tests passed successfully!');
      process.exit(0);
    }
  }

  async run() {
    console.log('🧪 Starting comprehensive test suite for TokenTracker Frontend');
    console.log('=' .repeat(80));

    // Run tests in sequence
    const testSuite = [
      () => this.runLinting(),
      () => this.runTypeChecking(),
      () => this.runUnitTests(),
      () => this.runIntegrationTests(),
      () => this.runE2ETests(),
    ];

    for (const testFn of testSuite) {
      const result = await testFn();
      this.results.push(result);

      // Stop on critical failures (linting, type checking)
      if (!result.success && (result.type.includes('ESLint') || result.type.includes('TypeScript'))) {
        console.log(`\n⚠️  Critical failure in ${result.type}. Stopping test suite.`);
        break;
      }
    }

    this.generateSummary();
  }
}

// CLI interface
const args = process.argv.slice(2);
const runner = new TestRunner();

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
TokenTracker Frontend Test Runner

Usage: npm run test:all [options]

Options:
  --help, -h     Show this help message
  
Test Types:
  • ESLint: Code quality and style checking
  • TypeScript: Type checking and compilation
  • Unit Tests: Component and utility function tests
  • Integration Tests: API and component integration tests
  • E2E Tests: End-to-end user workflow tests

Reports:
  • Coverage reports are generated in ./coverage/
  • E2E test reports are generated in ./playwright-report/
  • Test results are saved in ./test-results/

Examples:
  npm run test:all              # Run all tests
  npm run test:unit             # Run only unit tests
  npm run test:integration      # Run only integration tests
  npm run test:e2e              # Run only E2E tests
  npm run test:lint             # Run only linting
  npm run test:type-check       # Run only type checking
`);
  process.exit(0);
}

// Run the test suite
runner.run().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
