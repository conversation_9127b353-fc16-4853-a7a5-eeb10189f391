import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

interface GenerateSignalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (signalData: {
    token_address: string;
    analysis_type?: 'technical' | 'fundamental' | 'combined';
    timeframe?: '1H' | '4H' | '1D' | '1W';
    risk_level?: 'low' | 'medium' | 'high';
  }) => void;
  isLoading: boolean;
}

const GenerateSignalModal: React.FC<GenerateSignalModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
}) => {
  const [formData, setFormData] = useState({
    token_address: '',
    analysis_type: 'combined' as 'technical' | 'fundamental' | 'combined',
    timeframe: '1D' as '1H' | '4H' | '1D' | '1W',
    risk_level: 'medium' as 'low' | 'medium' | 'high',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Popular token addresses for quick selection
  const popularTokens = [
    { symbol: 'SOL', address: 'So11111111111111111111111111111111111111112', name: 'Solana' },
    { symbol: 'USDC', address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', name: 'USD Coin' },
    { symbol: 'RAY', address: '4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R', name: 'Raydium' },
    { symbol: 'SRM', address: 'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt', name: 'Serum' },
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    const newErrors: Record<string, string> = {};
    
    if (!formData.token_address.trim()) {
      newErrors.token_address = 'Token address is required';
    } else if (formData.token_address.length < 32) {
      newErrors.token_address = 'Invalid token address format';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setErrors({});
    onSubmit(formData);
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        token_address: '',
        analysis_type: 'combined',
        timeframe: '1D',
        risk_level: 'medium',
      });
      setErrors({});
      onClose();
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleTokenSelect = (address: string) => {
    handleInputChange('token_address', address);
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Generate Trading Signal">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="token_address" className="block text-sm font-medium text-text-primary mb-2">
            Token Address *
          </label>
          <Input
            id="token_address"
            type="text"
            value={formData.token_address}
            onChange={(e) => handleInputChange('token_address', e.target.value)}
            placeholder="Enter Solana token address"
            className={errors.token_address ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {errors.token_address && (
            <p className="mt-1 text-sm text-red-600">{errors.token_address}</p>
          )}
          
          {/* Popular Tokens */}
          <div className="mt-3">
            <p className="text-sm text-text-secondary mb-2">Popular tokens:</p>
            <div className="flex flex-wrap gap-2">
              {popularTokens.map((token) => (
                <button
                  key={token.address}
                  type="button"
                  onClick={() => handleTokenSelect(token.address)}
                  className="px-3 py-1 text-sm bg-surface-secondary hover:bg-surface-tertiary text-text-primary rounded-lg border border-surface-tertiary transition-colors"
                  disabled={isLoading}
                >
                  {token.symbol}
                </button>
              ))}
            </div>
          </div>
        </div>

        <div>
          <label htmlFor="analysis_type" className="block text-sm font-medium text-text-primary mb-2">
            Analysis Type
          </label>
          <select
            id="analysis_type"
            value={formData.analysis_type}
            onChange={(e) => handleInputChange('analysis_type', e.target.value)}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={isLoading}
          >
            <option value="technical">Technical Analysis - Chart patterns and indicators</option>
            <option value="fundamental">Fundamental Analysis - Project metrics and news</option>
            <option value="combined">Combined Analysis - Technical + Fundamental</option>
          </select>
        </div>

        <div>
          <label htmlFor="timeframe" className="block text-sm font-medium text-text-primary mb-2">
            Timeframe
          </label>
          <select
            id="timeframe"
            value={formData.timeframe}
            onChange={(e) => handleInputChange('timeframe', e.target.value)}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={isLoading}
          >
            <option value="1H">1 Hour - Short-term scalping</option>
            <option value="4H">4 Hours - Intraday trading</option>
            <option value="1D">1 Day - Swing trading</option>
            <option value="1W">1 Week - Position trading</option>
          </select>
        </div>

        <div>
          <label htmlFor="risk_level" className="block text-sm font-medium text-text-primary mb-2">
            Risk Level
          </label>
          <select
            id="risk_level"
            value={formData.risk_level}
            onChange={(e) => handleInputChange('risk_level', e.target.value)}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={isLoading}
          >
            <option value="low">Low Risk - Conservative signals with higher confidence</option>
            <option value="medium">Medium Risk - Balanced approach</option>
            <option value="high">High Risk - Aggressive signals with higher potential returns</option>
          </select>
        </div>

        <div className="bg-surface-tertiary p-4 rounded-lg">
          <h4 className="text-sm font-medium text-text-primary mb-2">Signal Generation Process</h4>
          <div className="space-y-2 text-sm text-text-secondary">
            <div className="flex items-start space-x-2">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500 mt-2"></span>
              <div>
                <span className="font-medium">Data Collection:</span> Gather price data, volume, and market metrics
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500 mt-2"></span>
              <div>
                <span className="font-medium">Technical Analysis:</span> Apply indicators (RSI, MACD, Bollinger Bands)
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500 mt-2"></span>
              <div>
                <span className="font-medium">Risk Assessment:</span> Calculate risk score and position sizing
              </div>
            </div>
            <div className="flex items-start space-x-2">
              <span className="inline-block w-2 h-2 rounded-full bg-blue-500 mt-2"></span>
              <div>
                <span className="font-medium">Signal Generation:</span> Generate buy/sell/hold recommendation
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-primary hover:bg-primary-dark"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Generating...</span>
              </div>
            ) : (
              'Generate Signal'
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default GenerateSignalModal;
