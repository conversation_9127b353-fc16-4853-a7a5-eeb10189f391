import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import { TradingSignal } from '@/types';
import { SignalsService } from '@/services/signals';

interface ActiveSignalsProps {
  signals: TradingSignal[];
  isLoading: boolean;
  onSignalSelect: (signal: TradingSignal) => void;
}

const ActiveSignals: React.FC<ActiveSignalsProps> = ({
  signals,
  isLoading,
  onSignalSelect,
}) => {
  const [selectedSignal, setSelectedSignal] = useState<TradingSignal | null>(null);
  const [showExecuteModal, setShowExecuteModal] = useState(false);
  const [executionData, setExecutionData] = useState({
    portfolio_id: '',
    quantity: 0,
    price_limit: 0,
    notes: '',
  });

  const queryClient = useQueryClient();

  // Execute signal mutation
  const executeSignalMutation = useMutation({
    mutationFn: ({ signalId, data }: { signalId: string; data: any }) =>
      SignalsService.executeSignal(signalId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['signals'] });
      setShowExecuteModal(false);
      setSelectedSignal(null);
    },
  });

  // Update signal status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ signalId, status }: { signalId: string; status: any }) =>
      SignalsService.updateSignalStatus(signalId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['signals'] });
    },
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getSignalTypeColor = (type: string) => {
    switch (type) {
      case 'buy':
        return 'bg-green-100 text-green-800';
      case 'sell':
        return 'bg-red-100 text-red-800';
      case 'hold':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-600';
    if (confidence >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleExecuteSignal = (signal: TradingSignal) => {
    setSelectedSignal(signal);
    setExecutionData({
      portfolio_id: '',
      quantity: signal.suggested_quantity || 0,
      price_limit: signal.target_price || 0,
      notes: '',
    });
    setShowExecuteModal(true);
  };

  const handleSubmitExecution = () => {
    if (!selectedSignal) return;
    
    executeSignalMutation.mutate({
      signalId: selectedSignal.id,
      data: executionData,
    });
  };

  const handleUpdateStatus = (signalId: string, status: string) => {
    updateStatusMutation.mutate({ signalId, status });
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="p-6">
            <div className="animate-pulse">
              <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
              <div className="space-y-3">
                <div className="h-4 bg-surface-tertiary rounded w-3/4"></div>
                <div className="h-4 bg-surface-tertiary rounded w-1/2"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (signals.length === 0) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold text-text-primary mb-4">No Active Signals</h3>
        <p className="text-text-muted mb-4">
          There are no active trading signals at the moment.
        </p>
        <Button className="bg-primary hover:bg-primary-dark">
          Generate New Signal
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {signals.map((signal) => (
        <Card key={signal.id} className="p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div>
                <h3 className="text-lg font-semibold text-text-primary">
                  {signal.token_symbol}
                </h3>
                <p className="text-sm text-text-muted">{signal.token_name}</p>
              </div>
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${getSignalTypeColor(
                  signal.signal_type
                )}`}
              >
                {signal.signal_type.toUpperCase()}
              </span>
            </div>
            <div className="text-right">
              <div className="text-sm text-text-secondary">Confidence</div>
              <div className={`text-xl font-bold ${getConfidenceColor(signal.confidence)}`}>
                {signal.confidence.toFixed(1)}%
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <div>
              <div className="text-sm text-text-secondary mb-1">Current Price</div>
              <div className="text-lg font-semibold text-text-primary">
                {formatCurrency(signal.current_price)}
              </div>
            </div>
            <div>
              <div className="text-sm text-text-secondary mb-1">Target Price</div>
              <div className="text-lg font-semibold text-text-primary">
                {formatCurrency(signal.target_price)}
              </div>
            </div>
            <div>
              <div className="text-sm text-text-secondary mb-1">Stop Loss</div>
              <div className="text-lg font-semibold text-red-600">
                {formatCurrency(signal.stop_loss)}
              </div>
            </div>
            <div>
              <div className="text-sm text-text-secondary mb-1">Potential Return</div>
              <div
                className={`text-lg font-semibold ${
                  signal.expected_return >= 0 ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {formatPercentage(signal.expected_return)}
              </div>
            </div>
          </div>

          {signal.analysis && (
            <div className="mb-4">
              <div className="text-sm text-text-secondary mb-2">Analysis</div>
              <p className="text-text-primary text-sm bg-surface-secondary p-3 rounded-lg">
                {signal.analysis}
              </p>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-text-muted">
              <span>Generated: {formatDate(signal.created_at)}</span>
              <span>Expires: {formatDate(signal.expires_at)}</span>
              <span>Risk: {signal.risk_score.toFixed(1)}/10</span>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleUpdateStatus(signal.id, 'cancelled')}
                disabled={updateStatusMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={() => handleExecuteSignal(signal)}
                className="bg-primary hover:bg-primary-dark"
              >
                Execute
              </Button>
            </div>
          </div>
        </Card>
      ))}

      {/* Execute Signal Modal */}
      <Modal
        isOpen={showExecuteModal}
        onClose={() => setShowExecuteModal(false)}
        title="Execute Trading Signal"
      >
        {selectedSignal && (
          <div className="space-y-6">
            <div className="p-4 bg-surface-tertiary rounded-lg">
              <h4 className="font-semibold text-text-primary mb-2">
                {selectedSignal.token_symbol} - {selectedSignal.signal_type.toUpperCase()}
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-text-secondary">Current Price:</span>
                  <span className="ml-2 font-medium">{formatCurrency(selectedSignal.current_price)}</span>
                </div>
                <div>
                  <span className="text-text-secondary">Target Price:</span>
                  <span className="ml-2 font-medium">{formatCurrency(selectedSignal.target_price)}</span>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Portfolio ID *
              </label>
              <input
                type="text"
                value={executionData.portfolio_id}
                onChange={(e) => setExecutionData(prev => ({ ...prev, portfolio_id: e.target.value }))}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
                placeholder="Enter portfolio ID"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Quantity
              </label>
              <input
                type="number"
                value={executionData.quantity}
                onChange={(e) => setExecutionData(prev => ({ ...prev, quantity: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
                placeholder="Enter quantity"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Price Limit
              </label>
              <input
                type="number"
                step="0.0001"
                value={executionData.price_limit}
                onChange={(e) => setExecutionData(prev => ({ ...prev, price_limit: parseFloat(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
                placeholder="Enter price limit"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Notes
              </label>
              <textarea
                value={executionData.notes}
                onChange={(e) => setExecutionData(prev => ({ ...prev, notes: e.target.value }))}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
                rows={3}
                placeholder="Optional execution notes"
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowExecuteModal(false)}
                disabled={executeSignalMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitExecution}
                disabled={!executionData.portfolio_id || executeSignalMutation.isPending}
                className="bg-primary hover:bg-primary-dark"
              >
                {executeSignalMutation.isPending ? 'Executing...' : 'Execute Signal'}
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ActiveSignals;
