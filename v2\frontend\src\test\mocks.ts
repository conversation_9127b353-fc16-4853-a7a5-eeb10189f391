import { jest } from '@jest/globals';
import {
  createMockPortfolio,
  createMockPosition,
  createMockTrade,
  createMockTradingSignal,
  createMockMarketOverview,
  createMockDashboardAnalytics,
  createMockTradeAnalytics,
  createMockSignalPerformance,
  createMockUserProfile,
  createMockApiResponse,
  createMockPaginatedResponse,
  createMockErrorResponse,
  createMockPortfolios,
  createMockPositions,
  createMockTrades,
  createMockTradingSignals,
} from './factories';

// Mock React Query
export const mockUseQuery = jest.fn();
export const mockUseMutation = jest.fn();
export const mockUseQueryClient = jest.fn();

jest.mock('@tanstack/react-query', () => ({
  useQuery: mockUseQuery,
  useMutation: mockUseMutation,
  useQueryClient: mockUseQueryClient,
  QueryClient: jest.fn().mockImplementation(() => ({
    invalidateQueries: jest.fn(),
    setQueryData: jest.fn(),
    getQueryData: jest.fn(),
  })),
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock React Router
export const mockNavigate = jest.fn();
export const mockUseParams = jest.fn();
export const mockUseLocation = jest.fn();

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useParams: mockUseParams,
  useLocation: mockUseLocation,
  BrowserRouter: ({ children }: { children: React.ReactNode }) => children,
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to}>{children}</a>
  ),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => {
  const MockIcon = ({ className, ...props }: any) => (
    <div className={className} data-testid="mock-icon" {...props} />
  );

  return new Proxy({}, {
    get: () => MockIcon,
  });
});

// API Service Mocks
export const mockApiService = {
  // Portfolio API mocks
  portfolios: {
    getAll: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockPaginatedResponse(createMockPortfolios()))
    ),
    getById: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockPortfolio())
    ),
    create: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockPortfolio())
    ),
    update: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockPortfolio())
    ),
    delete: jest.fn().mockResolvedValue(
      createMockApiResponse({ success: true })
    ),
    getPositions: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockPaginatedResponse(createMockPositions()))
    ),
    rebalance: jest.fn().mockResolvedValue(
      createMockApiResponse({ rebalance_plan: [] })
    ),
  },

  // Trading Signals API mocks
  signals: {
    getActive: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockPaginatedResponse(createMockTradingSignals()))
    ),
    getHistory: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockPaginatedResponse(createMockTradingSignals()))
    ),
    getPerformance: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockSignalPerformance())
    ),
    generate: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockTradingSignal())
    ),
    execute: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockTrade())
    ),
    backtest: jest.fn().mockResolvedValue(
      createMockApiResponse({
        total_return: 25.5,
        win_rate: 0.68,
        max_drawdown: -8.2,
        sharpe_ratio: 1.45,
        trades: createMockTrades(20),
      })
    ),
  },

  // Trades API mocks
  trades: {
    getAll: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockPaginatedResponse(createMockTrades()))
    ),
    getById: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockTrade())
    ),
    create: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockTrade())
    ),
    update: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockTrade())
    ),
    delete: jest.fn().mockResolvedValue(
      createMockApiResponse({ success: true })
    ),
    getAnalytics: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockTradeAnalytics())
    ),
    export: jest.fn().mockResolvedValue(
      createMockApiResponse({ download_url: 'https://example.com/export.csv' })
    ),
  },

  // Dashboard API mocks
  dashboard: {
    getOverview: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockDashboardAnalytics())
    ),
    getMarketOverview: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockMarketOverview())
    ),
  },

  // User API mocks
  user: {
    getProfile: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockUserProfile())
    ),
    updateProfile: jest.fn().mockResolvedValue(
      createMockApiResponse(createMockUserProfile())
    ),
    getSettings: jest.fn().mockResolvedValue(
      createMockApiResponse({
        notifications: { email: true, push: false },
        trading: { auto_execute: false, risk_level: 'medium' },
        appearance: { theme: 'dark', language: 'en' },
      })
    ),
    updateSettings: jest.fn().mockResolvedValue(
      createMockApiResponse({ success: true })
    ),
  },
};

// Mock fetch globally
global.fetch = jest.fn();

export const mockFetchSuccess = (data: any) => {
  (global.fetch as jest.Mock).mockResolvedValueOnce({
    ok: true,
    status: 200,
    json: jest.fn().mockResolvedValueOnce(data),
  });
};

export const mockFetchError = (status = 500, message = 'Internal Server Error') => {
  (global.fetch as jest.Mock).mockResolvedValueOnce({
    ok: false,
    status,
    json: jest.fn().mockResolvedValueOnce(createMockErrorResponse(message)),
  });
};

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock window methods
Object.defineProperty(window, 'confirm', {
  writable: true,
  value: jest.fn().mockReturnValue(true),
});

Object.defineProperty(window, 'alert', {
  writable: true,
  value: jest.fn(),
});

// Mock clipboard API
Object.defineProperty(navigator, 'clipboard', {
  writable: true,
  value: {
    writeText: jest.fn().mockResolvedValue(undefined),
    readText: jest.fn().mockResolvedValue(''),
  },
});

// Mock URL methods
Object.defineProperty(window.URL, 'createObjectURL', {
  writable: true,
  value: jest.fn().mockReturnValue('mock-blob-url'),
});

Object.defineProperty(window.URL, 'revokeObjectURL', {
  writable: true,
  value: jest.fn(),
});

// Mock file download
export const mockDownload = jest.fn();
Object.defineProperty(document, 'createElement', {
  writable: true,
  value: jest.fn().mockImplementation((tagName: string) => {
    if (tagName === 'a') {
      return {
        href: '',
        download: '',
        click: mockDownload,
        style: {},
      };
    }
    return document.createElement(tagName);
  }),
});

// Mock Chart.js
jest.mock('chart.js', () => ({
  Chart: {
    register: jest.fn(),
    defaults: {
      font: {},
      color: '',
    },
  },
  CategoryScale: jest.fn(),
  LinearScale: jest.fn(),
  PointElement: jest.fn(),
  LineElement: jest.fn(),
  Title: jest.fn(),
  Tooltip: jest.fn(),
  Legend: jest.fn(),
  Filler: jest.fn(),
  ArcElement: jest.fn(),
}));

jest.mock('react-chartjs-2', () => ({
  Line: jest.fn(({ data, options }) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)} />
  )),
  Doughnut: jest.fn(({ data, options }) => (
    <div data-testid="doughnut-chart" data-chart-data={JSON.stringify(data)} />
  )),
}));

// Reset all mocks helper
export const resetAllMocks = () => {
  jest.clearAllMocks();
  
  // Reset API service mocks
  Object.values(mockApiService).forEach(service => {
    Object.values(service).forEach(method => {
      if (jest.isMockFunction(method)) {
        method.mockClear();
      }
    });
  });

  // Reset React Query mocks
  mockUseQuery.mockClear();
  mockUseMutation.mockClear();
  mockUseQueryClient.mockClear();

  // Reset Router mocks
  mockNavigate.mockClear();
  mockUseParams.mockClear();
  mockUseLocation.mockClear();

  // Reset storage mocks
  localStorageMock.getItem.mockClear();
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();

  sessionStorageMock.getItem.mockClear();
  sessionStorageMock.setItem.mockClear();
  sessionStorageMock.removeItem.mockClear();
  sessionStorageMock.clear.mockClear();
};

// Mock query implementations
export const mockSuccessfulQuery = (data: any) => {
  mockUseQuery.mockReturnValue({
    data,
    isLoading: false,
    isError: false,
    error: null,
    refetch: jest.fn(),
  });
};

export const mockLoadingQuery = () => {
  mockUseQuery.mockReturnValue({
    data: undefined,
    isLoading: true,
    isError: false,
    error: null,
    refetch: jest.fn(),
  });
};

export const mockErrorQuery = (error: any) => {
  mockUseQuery.mockReturnValue({
    data: undefined,
    isLoading: false,
    isError: true,
    error,
    refetch: jest.fn(),
  });
};

export const mockSuccessfulMutation = () => {
  mockUseMutation.mockReturnValue({
    mutate: jest.fn(),
    mutateAsync: jest.fn(),
    isLoading: false,
    isError: false,
    error: null,
    data: undefined,
    reset: jest.fn(),
  });
};

export const mockLoadingMutation = () => {
  mockUseMutation.mockReturnValue({
    mutate: jest.fn(),
    mutateAsync: jest.fn(),
    isLoading: true,
    isError: false,
    error: null,
    data: undefined,
    reset: jest.fn(),
  });
};
