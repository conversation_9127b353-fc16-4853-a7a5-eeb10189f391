import React, { useState } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Globe, Code, Zap, Settings, AlertTriangle, RefreshCw } from 'lucide-react';

const AdvancedSettings: React.FC = () => {
  const [webhookUrl, setWebhookUrl] = useState('');
  const [customEndpoint, setCustomEndpoint] = useState('');

  // Fetch advanced settings
  const { data: settings, isLoading } = useQuery({
    queryKey: ['advanced-settings'],
    queryFn: async () => {
      // Mock API call
      return {
        webhook_url: '',
        custom_api_endpoint: '',
        rate_limit_per_minute: 60,
        enable_debug_mode: false,
        cache_duration_minutes: 5,
        auto_logout_minutes: 30,
        session_timeout_minutes: 120,
        enable_experimental_features: false,
        api_version: 'v1',
        data_sync_interval_seconds: 30,
      };
    },
  });

  const [localSettings, setLocalSettings] = useState<any>(null);

  React.useEffect(() => {
    if (settings && !localSettings) {
      setLocalSettings(settings);
      setWebhookUrl(settings.webhook_url || '');
      setCustomEndpoint(settings.custom_api_endpoint || '');
    }
  }, [settings, localSettings]);

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (data: any) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return data;
    },
  });

  // Test webhook mutation
  const testWebhookMutation = useMutation({
    mutationFn: async (url: string) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true, response_time: 150 };
    },
  });

  // Clear cache mutation
  const clearCacheMutation = useMutation({
    mutationFn: async () => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      return { success: true };
    },
    onSuccess: () => {
      alert('Cache cleared successfully');
    },
  });

  const handleInputChange = (field: string, value: any) => {
    setLocalSettings((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleToggle = (field: string) => {
    setLocalSettings((prev: any) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleSave = () => {
    const updatedSettings = {
      ...localSettings,
      webhook_url: webhookUrl,
      custom_api_endpoint: customEndpoint,
    };
    updateSettingsMutation.mutate(updatedSettings);
  };

  const handleTestWebhook = () => {
    if (!webhookUrl.trim()) {
      alert('Please enter a webhook URL first');
      return;
    }
    testWebhookMutation.mutate(webhookUrl);
  };

  if (isLoading || !localSettings) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
            <div className="h-20 bg-surface-tertiary rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* API Configuration */}
      <div>
        <div className="flex items-center mb-4">
          <Code className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">API Configuration</h4>
        </div>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Webhook URL
            </label>
            <div className="flex space-x-2">
              <Input
                value={webhookUrl}
                onChange={(e) => setWebhookUrl(e.target.value)}
                placeholder="https://your-app.com/webhook"
                className="flex-1"
              />
              <Button
                variant="outline"
                onClick={handleTestWebhook}
                disabled={testWebhookMutation.isPending}
              >
                {testWebhookMutation.isPending ? 'Testing...' : 'Test'}
              </Button>
            </div>
            <p className="text-xs text-text-muted mt-1">
              Receive real-time notifications about trades and portfolio changes
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Custom API Endpoint
            </label>
            <Input
              value={customEndpoint}
              onChange={(e) => setCustomEndpoint(e.target.value)}
              placeholder="https://api.your-domain.com"
            />
            <p className="text-xs text-text-muted mt-1">
              Use a custom API endpoint for enterprise deployments
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Rate Limit (requests/minute)
              </label>
              <Input
                type="number"
                value={localSettings.rate_limit_per_minute}
                onChange={(e) => handleInputChange('rate_limit_per_minute', parseInt(e.target.value))}
                min="1"
                max="1000"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                API Version
              </label>
              <select
                value={localSettings.api_version}
                onChange={(e) => handleInputChange('api_version', e.target.value)}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
              >
                <option value="v1">Version 1 (Stable)</option>
                <option value="v2">Version 2 (Beta)</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Settings */}
      <div>
        <div className="flex items-center mb-4">
          <Zap className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Performance Settings</h4>
        </div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Cache Duration (minutes)
              </label>
              <Input
                type="number"
                value={localSettings.cache_duration_minutes}
                onChange={(e) => handleInputChange('cache_duration_minutes', parseInt(e.target.value))}
                min="1"
                max="60"
              />
              <p className="text-xs text-text-muted mt-1">
                How long to cache market data
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Data Sync Interval (seconds)
              </label>
              <Input
                type="number"
                value={localSettings.data_sync_interval_seconds}
                onChange={(e) => handleInputChange('data_sync_interval_seconds', parseInt(e.target.value))}
                min="5"
                max="300"
              />
              <p className="text-xs text-text-muted mt-1">
                How often to sync portfolio data
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Button
              variant="outline"
              onClick={() => clearCacheMutation.mutate()}
              disabled={clearCacheMutation.isPending}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${clearCacheMutation.isPending ? 'animate-spin' : ''}`} />
              {clearCacheMutation.isPending ? 'Clearing...' : 'Clear Cache'}
            </Button>
          </div>
        </div>
      </div>

      {/* Session Settings */}
      <div>
        <div className="flex items-center mb-4">
          <Settings className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Session Settings</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Auto Logout (minutes)
            </label>
            <Input
              type="number"
              value={localSettings.auto_logout_minutes}
              onChange={(e) => handleInputChange('auto_logout_minutes', parseInt(e.target.value))}
              min="5"
              max="480"
            />
            <p className="text-xs text-text-muted mt-1">
              Automatically log out after inactivity
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-2">
              Session Timeout (minutes)
            </label>
            <Input
              type="number"
              value={localSettings.session_timeout_minutes}
              onChange={(e) => handleInputChange('session_timeout_minutes', parseInt(e.target.value))}
              min="30"
              max="1440"
            />
            <p className="text-xs text-text-muted mt-1">
              Maximum session duration
            </p>
          </div>
        </div>
      </div>

      {/* Developer Options */}
      <div>
        <div className="flex items-center mb-4">
          <Globe className="w-5 h-5 text-primary mr-2" />
          <h4 className="text-lg font-medium text-text-primary">Developer Options</h4>
        </div>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
            <div>
              <p className="font-medium text-text-primary">Debug Mode</p>
              <p className="text-sm text-text-secondary">
                Enable detailed logging and error reporting
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localSettings.enable_debug_mode}
                onChange={() => handleToggle('enable_debug_mode')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>

          <div className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
            <div>
              <p className="font-medium text-text-primary">Experimental Features</p>
              <p className="text-sm text-text-secondary">
                Enable beta features and new functionality
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={localSettings.enable_experimental_features}
                onChange={() => handleToggle('enable_experimental_features')}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-surface-tertiary peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      </div>

      {/* System Information */}
      <div>
        <h4 className="text-lg font-medium text-text-primary mb-4">System Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 bg-surface-secondary rounded-lg">
            <h5 className="font-medium text-text-primary mb-2">Application Version</h5>
            <p className="text-sm text-text-secondary">v2.1.0</p>
          </div>
          <div className="p-4 bg-surface-secondary rounded-lg">
            <h5 className="font-medium text-text-primary mb-2">API Status</h5>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm text-text-secondary">Operational</span>
            </div>
          </div>
          <div className="p-4 bg-surface-secondary rounded-lg">
            <h5 className="font-medium text-text-primary mb-2">Last Update</h5>
            <p className="text-sm text-text-secondary">July 20, 2024</p>
          </div>
          <div className="p-4 bg-surface-secondary rounded-lg">
            <h5 className="font-medium text-text-primary mb-2">Server Region</h5>
            <p className="text-sm text-text-secondary">US East (N. Virginia)</p>
          </div>
        </div>
      </div>

      {/* Warning */}
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mr-3 mt-0.5" />
          <div>
            <h5 className="font-medium text-yellow-800">Advanced Settings Warning</h5>
            <p className="text-sm text-yellow-700 mt-1">
              These settings are for advanced users only. Incorrect configuration may affect platform performance
              or functionality. Please ensure you understand the implications before making changes.
            </p>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          disabled={updateSettingsMutation.isPending}
          className="bg-primary hover:bg-primary-dark"
        >
          {updateSettingsMutation.isPending ? 'Saving...' : 'Save Advanced Settings'}
        </Button>
      </div>
    </div>
  );
};

export default AdvancedSettings;
