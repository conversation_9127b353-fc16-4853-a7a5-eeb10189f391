# ===========================================
# 🐳 TOKENTRACKER V2 DOCKER COMPOSE
# ===========================================
# Production-ready setup following DEPLOYMENT_SCALING.md

version: '3.8'

services:
  # 🗄️ MongoDB Database
  mongodb:
    image: mongo:7
    container_name: tokentracker-v2-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: ${MONGODB_DB_NAME:-tokentracker_v2}
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - tokentracker-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 🔄 Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tokentracker-v2-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redispassword}
    volumes:
      - redis_data:/data
    networks:
      - tokentracker-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 🚀 Main Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: tokentracker-v2-app
    restart: unless-stopped
    ports:
      - "8000:8000"  # Expose FastAPI application
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # Application
      NODE_ENV: ${NODE_ENV:-production}
      APP_PORT: 8000
      
      # Database
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/${MONGODB_DB_NAME:-tokentracker_v2}?authSource=admin
      REDIS_URL: redis://:${REDIS_PASSWORD:-redispassword}@redis:6379
      
      # External APIs (from .env file)
      DUNE_API_KEY: ${DUNE_API_KEY}
      DUNE_QUERY_ID: ${DUNE_QUERY_ID}
      TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
      TELEGRAM_CHANNEL_ID: ${TELEGRAM_CHANNEL_ID}
      SOLANA_RPC_URL: ${SOLANA_RPC_URL}
      JUPITER_API_URL: ${JUPITER_API_URL}
      RAYDIUM_API_URL: ${RAYDIUM_API_URL}
      
      # Security
      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
      
      # Logging
      LOG_LEVEL: ${LOG_LEVEL:-info}
      LOG_FORMAT: json
      
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
      - ./.env:/app/.env:ro
    networks:
      - tokentracker-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.tokentracker-v2.rule=Host(`tokentracker-v2.local`)"
      - "traefik.http.services.tokentracker-v2.loadbalancer.server.port=3000"

  # 📊 Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: tokentracker-v2-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - tokentracker-network
    depends_on:
      - app

  # 📈 Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: tokentracker-v2-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER:-admin}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards:ro
    networks:
      - tokentracker-network
    depends_on:
      - prometheus

  # 🌐 Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: tokentracker-v2-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    environment:
      # Use proper API base URL for Docker environment
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_WS_URL=ws://localhost:8000
      - VITE_APP_NAME=TokenTracker v2
      - VITE_APP_VERSION=2.0.0
      - NODE_ENV=production
    depends_on:
      - app
    networks:
      - tokentracker-network
    healthcheck:
      test: ["CMD", "/usr/local/bin/docker-healthcheck.sh"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 5s

  # 🔄 Background Worker (for async tasks) - DISABLED until worker module is implemented
  # worker:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #     target: production
  #   container_name: tokentracker-v2-worker
  #   restart: unless-stopped
  #   depends_on:
  #     mongodb:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #   environment:
  #     # Same environment as main app
  #     NODE_ENV: ${NODE_ENV:-production}
  #     MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/${MONGODB_DB_NAME:-tokentracker_v2}?authSource=admin
  #     REDIS_URL: redis://:${REDIS_PASSWORD:-redispassword}@redis:6379
  #     DUNE_API_KEY: ${DUNE_API_KEY}
  #     DUNE_QUERY_ID: ${DUNE_QUERY_ID}
  #     TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN}
  #     TELEGRAM_CHANNEL_ID: ${TELEGRAM_CHANNEL_ID}
  #     LOG_LEVEL: ${LOG_LEVEL:-info}
  #   volumes:
  #     - ./logs:/app/logs
  #     - ./data:/app/data
  #     - ./.env:/app/.env:ro
  #   networks:
  #     - tokentracker-network
  #   command: ["python", "-m", "src.worker"]

  # 🌐 Reverse Proxy (optional)
  traefik:
    image: traefik:v2.10
    container_name: tokentracker-v2-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    command:
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./config/traefik:/etc/traefik:ro
    networks:
      - tokentracker-network
    profiles:
      - proxy

# 🌐 Networks
networks:
  tokentracker-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 💾 Volumes
volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# 🏷️ Labels for organization
x-common-labels: &common-labels
  project: "tokentracker-v2"
  environment: "${NODE_ENV:-production}"
  version: "2.0.0"
