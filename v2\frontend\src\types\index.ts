// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// User types
export interface User extends BaseEntity {
  email: string;
  username: string;
  first_name?: string;
  last_name?: string;
  is_active: boolean;
  is_verified: boolean;
  role: 'user' | 'admin';
  preferences?: UserPreferences;
}

export interface UserPreferences {
  theme: 'dark' | 'light';
  notifications: boolean;
  default_portfolio?: string;
  timezone: string;
}

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials extends LoginCredentials {
  username: string;
  first_name?: string;
  last_name?: string;
}

export interface AuthToken {
  access_token: string;
  token_type: 'bearer';
  expires_in: number;
  refresh_token: string;
}

export interface AuthResponse {
  user: User;
  token: AuthToken;
}

// Portfolio types
export interface Portfolio extends BaseEntity {
  name: string;
  description?: string;
  initial_balance: number;
  current_balance: number;
  total_pnl: number;
  total_pnl_percentage: number;
  daily_pnl: number;
  daily_pnl_percentage: number;
  active_positions: number;
  total_trades: number;
  win_rate: number;
  sharpe_ratio: number;
  max_drawdown: number;
  is_active: boolean;
  risk_level: 'low' | 'medium' | 'high';
  positions?: Position[];
}

export interface Position extends BaseEntity {
  portfolio_id: string;
  token_address: string;
  token_symbol: string;
  token_name: string;
  quantity: number;
  average_price: number;
  current_price: number;
  market_value: number;
  unrealized_pnl: number;
  unrealized_pnl_percentage: number;
  position_type: 'long' | 'short';
  entry_date: string;
}

// Trading Signal types
export interface TradingSignal extends BaseEntity {
  token_address: string;
  token_symbol: string;
  token_name: string;
  signal_type: 'buy' | 'sell' | 'hold';
  confidence: number;
  price_target: number;
  target_price: number; // Alias for price_target
  current_price: number;
  stop_loss: number;
  take_profit: number;
  risk_reward_ratio: number;
  expected_return: number;
  actual_return?: number;
  risk_score: number;
  suggested_quantity: number;
  analysis: string;
  status: 'active' | 'executed' | 'expired' | 'cancelled';
  expires_at: string;
  generated_by: string;
}

export interface SignalAnalysis {
  technical_indicators: TechnicalIndicators;
  market_sentiment: number;
  volume_analysis: VolumeAnalysis;
  price_action: PriceAction;
  risk_assessment: RiskAssessment;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: number;
  bollinger_bands: {
    upper: number;
    middle: number;
    lower: number;
  };
  moving_averages: {
    sma_20: number;
    sma_50: number;
    ema_12: number;
    ema_26: number;
  };
}

export interface VolumeAnalysis {
  volume_24h: number;
  volume_change: number;
  volume_profile: 'high' | 'medium' | 'low';
}

export interface PriceAction {
  support_levels: number[];
  resistance_levels: number[];
  trend_direction: 'bullish' | 'bearish' | 'sideways';
  momentum: number;
}

export interface RiskAssessment {
  volatility: number;
  liquidity_score: number;
  market_cap_rank: number;
  risk_score: number;
}

// Trade types
export interface Trade extends BaseEntity {
  portfolio_id: string;
  signal_id?: string;
  token_address: string;
  token_symbol: string;
  token_name: string;
  trade_type: 'buy' | 'sell';
  quantity: number;
  price: number;
  total_value: number;
  fees: number;
  net_value: number;
  status: 'pending' | 'executed' | 'failed' | 'cancelled';
  execution_time?: string;
  executed_at?: string; // Alias for execution_time
  pnl?: number;
  pnl_percentage?: number;
  notes?: string;
}

// Chart data types
export interface TimeSeriesData {
  timestamp: string;
  value: number;
  volume?: number;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label: string;
  data: number[];
  borderColor?: string;
  backgroundColor?: string;
  fill?: boolean;
}

// Performance metrics
export interface PerformanceMetrics {
  total_return: number;
  total_return_percentage: number;
  annualized_return: number;
  volatility: number;
  sharpe_ratio: number;
  max_drawdown: number;
  win_rate: number;
  profit_factor: number;
  average_win: number;
  average_loss: number;
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
}

// Dashboard types
export interface DashboardData {
  portfolio_summary: PortfolioSummary;
  recent_signals: TradingSignal[];
  recent_trades: Trade[];
  performance_chart: TimeSeriesData[];
  market_overview: MarketOverview;
}

export interface PortfolioSummary {
  total_value: number;
  daily_pnl: number;
  daily_pnl_percentage: number;
  total_pnl: number;
  total_pnl_percentage: number;
  active_positions: number;
  active_signals: number;
  portfolio_count: number;
}

export interface MarketOverview {
  total_market_cap: number;
  market_cap_change: number;
  bitcoin_dominance: number;
  fear_greed_index: number;
  trending_tokens: TrendingToken[];
}

export interface TrendingToken {
  address: string;
  symbol: string;
  name: string;
  price: number;
  price_change_24h: number;
  volume_24h: number;
  market_cap: number;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: 'success' | 'error';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    page_size: number;
    total_pages: number;
    total_items: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

// Filter and sort types
export interface FilterConfig {
  [key: string]: string | number | boolean | string[] | number[];
}

export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

export interface PaginationConfig {
  page: number;
  page_size: number;
}

// Notification types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary';
}

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  data: unknown;
  timestamp: string;
}

export interface PortfolioUpdate {
  portfolio_id: string;
  current_balance: number;
  daily_pnl: number;
  daily_pnl_percentage: number;
  positions: Position[];
}

export interface SignalUpdate {
  signal: TradingSignal;
  action: 'created' | 'updated' | 'executed' | 'expired';
}

export interface TradeUpdate {
  trade: Trade;
  action: 'created' | 'executed' | 'failed' | 'cancelled';
}
