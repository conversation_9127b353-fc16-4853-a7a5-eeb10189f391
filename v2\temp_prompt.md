"""""""""""""""""
consider all rules in .INSTRUCTIONS
****************************
## 📋 **Project Overview**

You are tasked with building a **professional, responsive frontend** for TokenTracker v2, an advanced cryptocurrency trading automation platform. The backend is fully operational with a comprehensive REST API, real-time capabilities, and enterprise-grade infrastructure.

## 🏗️ **Current Backend Infrastructure**

### **✅ Operational Services:**
- **Main API**: `http://localhost:8000` (FastAPI with full OpenAPI documentation)
- **API Documentation**: `http://localhost:8000/docs` (Interactive Swagger UI)
- **Database**: MongoDB on `localhost:27017`
- **Cache**: Redis on `localhost:6379`
- **Monitoring**: <PERSON><PERSON> on `http://localhost:3001` (admin/admin)
- **Metrics**: Prometheus on `http://localhost:9090`

### **🔧 Backend Status:**
- ✅ All FastAPI dependency injection issues resolved
- ✅ Content Security Policy configured for external resources
- ✅ CORS enabled for frontend integration
- ✅ JWT authentication system implemented
- ✅ Real-time WebSocket capabilities available
- ✅ Comprehensive API endpoints with proper error handling

## 🎯 **Technical Stack Requirements**

### **Core Technologies:**
```typescript
// Required Stack
- React 18+ with TypeScript
- Tailwind CSS for styling
- Axios for HTTP requests
- React Query/TanStack Query for data fetching
- React Router v6 for navigation
- Chart.js or Recharts for data visualization
- Socket.io-client for real-time updates
- React Hook Form for form management
- Zustand or Redux Toolkit for state management
```

### **Development Tools:**
```json
{
  "build": "Vite",
  "testing": "Jest + React Testing Library",
  "linting": "ESLint + Prettier",
  "typeChecking": "TypeScript strict mode",
  "bundling": "Vite with code splitting"
}
```

## 🌐 **API Integration Specifications**

### **🔑 Authentication System:**
```typescript
// JWT Authentication Flow
interface AuthEndpoints {
  login: "POST /api/v1/web/user/login",
  register: "POST /api/v1/web/user/register", 
  refresh: "POST /api/v1/web/user/refresh",
  profile: "GET /api/v1/web/user",
  logout: "POST /api/v1/web/user/logout"
}

// Token Management
interface AuthToken {
  access_token: string;
  token_type: "bearer";
  expires_in: number;
  refresh_token: string;
}
```

### **📊 Core API Endpoints:**

#### **Dashboard & Portfolio:**
```typescript
interface DashboardAPI {
  // Main dashboard data
  dashboard: "GET /api/v1/web/dashboard",
  realtime: "GET /api/v1/web/dashboard/realtime",
  
  // Portfolio management
  portfolios: "GET /api/v1/trading/portfolios",
  createPortfolio: "POST /api/v1/trading/portfolios",
  portfolioDetails: "GET /api/v1/trading/portfolios/{id}",
  updatePortfolio: "PUT /api/v1/trading/portfolios/{id}",
  
  // Performance tracking
  performance: "GET /api/v1/trading/performance/{portfolio_id}",
  analytics: "GET /api/v1/trading/analytics/{portfolio_id}"
}
```

#### **Trading Signals:**
```typescript
interface SignalsAPI {
  // Signal management
  activeSignals: "GET /api/v1/signals/active",
  signalHistory: "GET /api/v1/signals/history",
  generateSignal: "POST /api/v1/signals/generate",
  signalDetails: "GET /api/v1/signals/{signal_id}",
  
  // Signal configuration
  signalConfig: "GET /api/v1/signals/config",
  updateConfig: "PUT /api/v1/signals/config"
}
```

#### **Charts & Analytics:**
```typescript
interface ChartsAPI {
  // Portfolio charts
  portfolioChart: "GET /api/v1/web/charts/portfolio",
  performanceChart: "GET /api/v1/web/charts/performance", 
  signalsChart: "GET /api/v1/web/charts/signals",
  
  // Market data
  tokenPrices: "GET /api/v1/data/aggregate/{token_address}",
  marketOverview: "GET /api/v1/data/market/overview"
}
```

#### **Trade Management:**
```typescript
interface TradesAPI {
  // Trade history with pagination
  tradeHistory: "GET /api/v1/web/trades?page={page}&page_size={size}",
  tradeDetails: "GET /api/v1/web/trades/{trade_id}",
  tradeAnalytics: "GET /api/v1/web/trades/analytics",
  exportTrades: "GET /api/v1/web/trades/export"
}
```

#### **System Monitoring:**
```typescript
interface MonitoringAPI {
  // Health & status
  systemHealth: "GET /api/v1/monitoring/health",
  systemMetrics: "GET /api/v1/monitoring/metrics",
  alerts: "GET /api/v1/monitoring/alerts",
  
  // Performance monitoring
  performance: "GET /api/v1/monitoring/performance",
  logs: "POST /api/v1/monitoring/logs/search"
}
```

## 🎨 **Design System Requirements**

### **🌙 Dark Theme Specifications:**
```css
/* Primary Color Palette */
:root {
  --bg-primary: #0f172a;      /* Main background */
  --bg-secondary: #1e293b;    /* Card backgrounds */
  --bg-tertiary: #334155;     /* Hover states */
  
  --text-primary: #f8fafc;    /* Primary text */
  --text-secondary: #cbd5e1;  /* Secondary text */
  --text-muted: #64748b;      /* Muted text */
  
  --accent-green: #10b981;    /* Profit/Buy signals */
  --accent-red: #ef4444;      /* Loss/Sell signals */
  --accent-blue: #3b82f6;     /* Info/Hold signals */
  --accent-yellow: #f59e0b;   /* Warning states */
  
  --border-color: #475569;    /* Borders */
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}
```

### **📱 Responsive Breakpoints:**
```css
/* Mobile-first approach */
@media (min-width: 640px)  { /* sm: tablets */ }
@media (min-width: 768px)  { /* md: small laptops */ }
@media (min-width: 1024px) { /* lg: desktops */ }
@media (min-width: 1280px) { /* xl: large screens */ }
```

## 🏗️ **Application Architecture**

### **📁 Project Structure:**
```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Basic UI elements (Button, Input, etc.)
│   ├── charts/          # Chart components
│   ├── forms/           # Form components
│   └── layout/          # Layout components
├── pages/               # Page components
│   ├── Dashboard/       # Main dashboard
│   ├── Portfolio/       # Portfolio management
│   ├── Signals/         # Trading signals
│   ├── Trades/          # Trade history
│   ├── Analytics/       # Analytics & reports
│   └── Settings/        # User settings
├── services/            # API service layer
│   ├── api.ts          # Axios configuration
│   ├── auth.ts         # Authentication service
│   ├── dashboard.ts    # Dashboard API calls
│   ├── portfolio.ts    # Portfolio API calls
│   ├── signals.ts      # Signals API calls
│   └── websocket.ts    # WebSocket service
├── hooks/               # Custom React hooks
├── store/               # State management
├── types/               # TypeScript interfaces
├── utils/               # Utility functions
└── styles/              # Global styles
```

### **🔌 API Service Layer:**
```typescript
// services/api.ts
import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth tokens
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh logic
      await refreshToken();
    }
    return Promise.reject(error);
  }
);
```

### **🔄 Real-time Integration:**
```typescript
// services/websocket.ts
import { io, Socket } from 'socket.io-client';

class WebSocketService {
  private socket: Socket | null = null;
  
  connect(token: string) {
    this.socket = io('http://localhost:8000', {
      auth: { token },
      transports: ['websocket']
    });
    
    this.socket.on('portfolio_update', this.handlePortfolioUpdate);
    this.socket.on('signal_generated', this.handleNewSignal);
    this.socket.on('trade_executed', this.handleTradeUpdate);
  }
  
  private handlePortfolioUpdate = (data: PortfolioUpdate) => {
    // Update portfolio state
  };
  
  private handleNewSignal = (signal: TradingSignal) => {
    // Show notification and update signals
  };
}
```

## 📱 **Page-by-Page Requirements**

### **🏠 Dashboard Page:**
```typescript
interface DashboardFeatures {
  // Key metrics cards
  portfolioSummary: {
    totalValue: number;
    dailyPnL: number;
    totalPnLPercentage: number;
    activePositions: number;
  };
  
  // Real-time charts
  portfolioPerformanceChart: TimeSeriesChart;
  signalsOverviewChart: DonutChart;
  
  // Recent activity
  recentSignals: TradingSignal[];
  recentTrades: Trade[];
  
  // Quick actions
  quickTradePanel: boolean;
  alertsPanel: boolean;
}
```

### **💼 Portfolio Page:**
```typescript
interface PortfolioFeatures {
  // Portfolio list with cards
  portfolioGrid: Portfolio[];
  
  // Detailed portfolio view
  portfolioDetails: {
    positions: Position[];
    performance: PerformanceMetrics;
    riskMetrics: RiskAnalysis;
    historicalChart: TimeSeriesChart;
  };
  
  // Portfolio management
  createPortfolioModal: boolean;
  editPortfolioModal: boolean;
  portfolioSettings: PortfolioConfig;
}
```

### **📊 Signals Page:**
```typescript
interface SignalsFeatures {
  // Active signals table
  activeSignalsTable: {
    data: TradingSignal[];
    pagination: PaginationConfig;
    filters: SignalFilters;
    sorting: SortConfig;
  };
  
  // Signal details modal
  signalDetailsModal: {
    signal: TradingSignal;
    analysis: SignalAnalysis;
    historicalPerformance: PerformanceData;
  };
  
  // Signal configuration
  signalSettings: SignalConfig;
  backtestResults: BacktestData;
}
```

### **📈 Analytics Page:**
```typescript
interface AnalyticsFeatures {
  // Performance analytics
  performanceCharts: {
    portfolioGrowth: LineChart;
    drawdownChart: AreaChart;
    returnsDistribution: HistogramChart;
    correlationMatrix: HeatmapChart;
  };
  
  // Risk analytics
  riskMetrics: {
    sharpeRatio: number;
    maxDrawdown: number;
    volatility: number;
    beta: number;
  };
  
  // Custom reports
  reportBuilder: ReportConfig;
  exportOptions: ExportFormat[];
}
```

## 🔧 **Component Specifications**

### **📊 Chart Components:**
```typescript
// components/charts/PortfolioChart.tsx
interface PortfolioChartProps {
  data: TimeSeriesData[];
  timeframe: '1D' | '1W' | '1M' | '3M' | '1Y';
  chartType: 'line' | 'area' | 'candlestick';
  height?: number;
  showVolume?: boolean;
  indicators?: TechnicalIndicator[];
}

// Real-time updates
const PortfolioChart: React.FC<PortfolioChartProps> = ({ data, timeframe }) => {
  const { data: chartData, isLoading } = useQuery({
    queryKey: ['portfolio-chart', timeframe],
    queryFn: () => fetchPortfolioChart(timeframe),
    refetchInterval: 30000, // 30 seconds
  });
  
  // WebSocket integration for real-time updates
  useWebSocket('portfolio_updates', (update) => {
    // Update chart data in real-time
  });
  
  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <Chart data={chartData} options={chartOptions} />
    </div>
  );
};
```

### **📋 Data Tables:**
```typescript
// components/ui/DataTable.tsx
interface DataTableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  pagination?: PaginationConfig;
  sorting?: SortConfig;
  filtering?: FilterConfig;
  loading?: boolean;
  onRowClick?: (row: T) => void;
}

// Advanced features
const DataTable = <T,>({ data, columns, pagination }: DataTableProps<T>) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [filtering, setFiltering] = useState<FilteringState>({});
  
  return (
    <div className="bg-gray-800 rounded-lg overflow-hidden">
      <div className="p-4 border-b border-gray-700">
        <SearchInput onSearch={setGlobalFilter} />
        <FilterDropdowns filters={filtering} onChange={setFiltering} />
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* Table implementation with sorting, filtering */}
        </table>
      </div>
      
      <Pagination config={pagination} />
    </div>
  );
};
```

### **🔔 Notification System:**
```typescript
// components/ui/NotificationSystem.tsx
interface NotificationProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  actions?: NotificationAction[];
}

// Real-time notifications
const NotificationSystem: React.FC = () => {
  const { notifications, addNotification, removeNotification } = useNotifications();
  
  // WebSocket integration
  useWebSocket('notifications', (notification) => {
    addNotification({
      type: notification.type,
      title: notification.title,
      message: notification.message,
      duration: 5000,
    });
  });
  
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification) => (
        <NotificationCard key={notification.id} {...notification} />
      ))}
    </div>
  );
};
```

## 🔐 **Authentication Implementation**

### **🔑 Auth Context:**
```typescript
// contexts/AuthContext.tsx
interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(
    localStorage.getItem('access_token')
  );
  
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await authService.login(credentials);
      setToken(response.access_token);
      setUser(response.user);
      localStorage.setItem('access_token', response.access_token);
      localStorage.setItem('refresh_token', response.refresh_token);
    } catch (error) {
      throw new Error('Login failed');
    }
  };
  
  // Auto-refresh token logic
  useEffect(() => {
    if (token) {
      const interval = setInterval(refreshToken, 14 * 60 * 1000); // 14 minutes
      return () => clearInterval(interval);
    }
  }, [token]);
  
  return (
    <AuthContext.Provider value={{ user, token, login, logout, isAuthenticated: !!token }}>
      {children}
    </AuthContext.Provider>
  );
};
```

## 📱 **Mobile Responsiveness**

### **🔧 Mobile Navigation:**
```typescript
// components/layout/MobileNavigation.tsx
const MobileNavigation: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <>
      {/* Mobile menu button */}
      <button
        className="md:hidden fixed top-4 left-4 z-50 p-2 bg-gray-800 rounded-lg"
        onClick={() => setIsOpen(!isOpen)}
      >
        <MenuIcon />
      </button>
      
      {/* Slide-out menu */}
      <div className={`
        fixed inset-y-0 left-0 z-40 w-64 bg-gray-900 transform transition-transform
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        md:translate-x-0 md:static md:inset-0
      `}>
        <NavigationMenu />
      </div>
      
      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
};
```

### **📊 Responsive Charts:**
```typescript
// hooks/useResponsiveChart.ts
const useResponsiveChart = () => {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width } = containerRef.current.getBoundingClientRect();
        setDimensions({
          width,
          height: Math.min(width * 0.6, 400), // Responsive height
        });
      }
    };
    
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);
  
  return { dimensions, containerRef };
};
```

## 🚀 **Performance Optimization**

### **⚡ Code Splitting:**
```typescript
// Lazy loading for pages
const Dashboard = lazy(() => import('../pages/Dashboard'));
const Portfolio = lazy(() => import('../pages/Portfolio'));
const Signals = lazy(() => import('../pages/Signals'));
const Analytics = lazy(() => import('../pages/Analytics'));

// Route configuration with suspense
const AppRoutes: React.FC = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/portfolio" element={<Portfolio />} />
      <Route path="/signals" element={<Signals />} />
      <Route path="/analytics" element={<Analytics />} />
    </Routes>
  </Suspense>
);
```

### **🔄 Data Caching:**
```typescript
// React Query configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      refetchOnWindowFocus: false,
    },
  },
});

// Custom hooks with caching
const usePortfolioData = (portfolioId: string) => {
  return useQuery({
    queryKey: ['portfolio', portfolioId],
    queryFn: () => portfolioService.getPortfolio(portfolioId),
    staleTime: 2 * 60 * 1000, // 2 minutes for portfolio data
  });
};
```

## 🐳 **Docker Configuration**

### **📦 Frontend Dockerfile:**
```dockerfile
# Multi-stage build for production
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=builder /app/dist /usr/share/nginx/html

# Custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/health || exit 1

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### **🔧 Docker Compose Integration:**
```yaml
# Add to existing docker-compose.yml
services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tokentracker-v2-frontend
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    depends_on:
      - app
    networks:
      - tokentracker-network
    restart: unless-stopped
```

## 🧪 **Testing Requirements**

### **🔬 Testing Strategy:**
```typescript
// Component testing
describe('Dashboard Component', () => {
  it('should render portfolio summary correctly', async () => {
    const mockData = { totalValue: 10000, dailyPnL: 150 };
    
    render(
      <QueryClient>
        <Dashboard />
      </QueryClient>
    );
    
    await waitFor(() => {
      expect(screen.getByText('$10,000')).toBeInTheDocument();
      expect(screen.getByText('+$150')).toBeInTheDocument();
    });
  });
});

// API service testing
describe('Portfolio Service', () => {
  it('should fetch portfolio data correctly', async () => {
    const mockResponse = { id: '1', name: 'Test Portfolio' };
    jest.spyOn(apiClient, 'get').mockResolvedValue({ data: mockResponse });
    
    const result = await portfolioService.getPortfolio('1');
    expect(result).toEqual(mockResponse);
  });
});
```

## 📋 **Deliverables Checklist**

### **✅ Core Application:**
- [ ] Complete React application with TypeScript
- [ ] Responsive design (mobile, tablet, desktop)
- [ ] Dark theme implementation
- [ ] Authentication system with JWT
- [ ] Real-time WebSocket integration
- [ ] Comprehensive error handling

### **✅ Pages & Features:**
- [ ] Dashboard with real-time metrics
- [ ] Portfolio management interface
- [ ] Trading signals display and management
- [ ] Trade history with advanced filtering
- [ ] Analytics and reporting dashboard
- [ ] User settings and preferences

### **✅ Technical Implementation:**
- [ ] API service layer with proper TypeScript interfaces
- [ ] State management (Zustand/Redux)
- [ ] React Query for data fetching and caching
- [ ] Chart.js/Recharts integration
- [ ] Form validation and management
- [ ] Loading states and skeleton screens

### **✅ Production Ready:**
- [ ] Docker configuration for deployment
- [ ] Environment configuration
- [ ] Performance optimization (code splitting, lazy loading)
- [ ] Comprehensive testing suite
- [ ] Error boundaries and fallback UI
- [ ] Accessibility compliance (WCAG 2.1)

## 🎯 **Success Criteria**

The frontend should provide:
1. **Professional Trading Interface**: Clean, intuitive design suitable for cryptocurrency traders
2. **Real-time Performance**: Sub-second response times with live data updates
3. **Mobile Excellence**: Fully functional on all device sizes
4. **Robust Error Handling**: Graceful degradation and user-friendly error messages
5. **Seamless Integration**: Perfect compatibility with the existing TokenTracker v2 API
6. **Production Ready**: Deployable alongside the current Docker infrastructure

**Timeline Expectation**: 2-3 weeks for full implementation with testing and optimization.

---

