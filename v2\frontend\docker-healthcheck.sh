#!/bin/sh

# Health check script for Docker container
# Checks if nginx is running and serving content

# Check if nginx is running
if ! pgrep nginx > /dev/null; then
    echo "Nginx is not running"
    exit 1
fi

# Check if the health endpoint responds
if ! wget --no-verbose --tries=1 --spider http://localhost/health; then
    echo "Health endpoint is not responding"
    exit 1
fi

echo "Health check passed"
exit 0
