import React from 'react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { Portfolio } from '@/types';

interface PortfolioListProps {
  portfolios: Portfolio[];
  selectedPortfolio: Portfolio | null;
  onPortfolioSelect: (portfolio: Portfolio) => void;
  onDeletePortfolio: (portfolioId: string) => void;
  isLoading: boolean;
}

const PortfolioList: React.FC<PortfolioListProps> = ({
  portfolios,
  selectedPortfolio,
  onPortfolioSelect,
  onDeletePortfolio,
  isLoading,
}) => {
  if (isLoading) {
    return (
      <Card className="p-4">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Portfolios</h3>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="h-20 bg-surface-tertiary rounded-lg"></div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  if (portfolios.length === 0) {
    return (
      <Card className="p-4">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Portfolios</h3>
        <div className="text-center py-8">
          <p className="text-text-muted mb-4">No portfolios found</p>
          <p className="text-sm text-text-secondary">Create your first portfolio to get started</p>
        </div>
      </Card>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  return (
    <Card className="p-4">
      <h3 className="text-lg font-semibold text-text-primary mb-4">Portfolios</h3>
      <div className="space-y-3">
        {portfolios.map((portfolio) => (
          <div
            key={portfolio.id}
            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
              selectedPortfolio?.id === portfolio.id
                ? 'border-primary bg-primary/5'
                : 'border-surface-tertiary hover:border-surface-quaternary bg-surface-secondary'
            }`}
            onClick={() => onPortfolioSelect(portfolio)}
          >
            <div className="flex justify-between items-start mb-2">
              <h4 className="font-semibold text-text-primary truncate">{portfolio.name}</h4>
              <div className="flex items-center space-x-1">
                <span
                  className={`inline-block w-2 h-2 rounded-full ${
                    portfolio.is_active ? 'bg-green-500' : 'bg-gray-400'
                  }`}
                />
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeletePortfolio(portfolio.id);
                  }}
                  className="text-text-muted hover:text-red-500 transition-colors"
                  title="Delete portfolio"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Balance</span>
                <span className="text-sm font-medium text-text-primary">
                  {formatCurrency(portfolio.current_balance)}
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Daily P&L</span>
                <span
                  className={`text-sm font-medium ${
                    portfolio.daily_pnl >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {formatCurrency(portfolio.daily_pnl)} ({formatPercentage(portfolio.daily_pnl_percentage)})
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Total P&L</span>
                <span
                  className={`text-sm font-medium ${
                    portfolio.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {formatCurrency(portfolio.total_pnl)} ({formatPercentage(portfolio.total_pnl_percentage)})
                </span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Positions</span>
                <span className="text-sm text-text-primary">{portfolio.active_positions}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-text-secondary">Risk Level</span>
                <span
                  className={`text-xs px-2 py-1 rounded-full font-medium ${
                    portfolio.risk_level === 'low'
                      ? 'bg-green-100 text-green-800'
                      : portfolio.risk_level === 'medium'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {portfolio.risk_level.toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default PortfolioList;
