import React, { forwardRef } from 'react';
import { clsx } from 'clsx';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  helperText?: string;
}

/**
 * Reusable input component with validation and icons
 */
const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ label, error, icon, rightIcon, helperText, className, ...props }, ref) => {
    const inputClasses = clsx(
      'w-full px-4 py-3 rounded-lg border transition-colors duration-200',
      'bg-bg-secondary text-text-primary placeholder-text-muted',
      'focus:outline-none focus:ring-2 focus:ring-accent-blue focus:border-transparent',
      {
        'border-border': !error,
        'border-accent-red focus:ring-accent-red': error,
        'pl-11': icon,
        'pr-11': rightIcon,
      },
      className
    );

    return (
      <div className="space-y-1">
        {label && (
          <label className="block text-sm font-medium text-text-primary">
            {label}
          </label>
        )}
        
        <div className="relative">
          {icon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-text-muted">{icon}</span>
            </div>
          )}
          
          <input
            ref={ref}
            className={inputClasses}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {rightIcon}
            </div>
          )}
        </div>
        
        {error && (
          <p className="text-sm text-accent-red">{error}</p>
        )}
        
        {helperText && !error && (
          <p className="text-sm text-text-muted">{helperText}</p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
