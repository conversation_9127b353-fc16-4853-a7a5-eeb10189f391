import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { Trade } from '@/types';
import { TradesService } from '@/services/trades';

interface TradeDetailsProps {
  isOpen: boolean;
  onClose: () => void;
  trade: Trade;
}

const TradeDetails: React.FC<TradeDetailsProps> = ({
  isOpen,
  onClose,
  trade,
}) => {
  const [notes, setNotes] = useState(trade.notes || '');
  const [isEditingNotes, setIsEditingNotes] = useState(false);

  const queryClient = useQueryClient();

  // Fetch trade execution details
  const { data: executionData, isLoading: executionLoading } = useQuery({
    queryKey: ['trade-execution', trade.id],
    queryFn: () => TradesService.getTradeExecution(trade.id),
    enabled: isOpen && trade.status === 'executed',
  });

  // Update notes mutation
  const updateNotesMutation = useMutation({
    mutationFn: (newNotes: string) => TradesService.updateTradeNotes(trade.id, newNotes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trades'] });
      setIsEditingNotes(false);
    },
  });

  // Cancel trade mutation
  const cancelTradeMutation = useMutation({
    mutationFn: () => TradesService.cancelTrade(trade.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trades'] });
      onClose();
    },
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'executed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleSaveNotes = () => {
    updateNotesMutation.mutate(notes);
  };

  const handleCancelTrade = () => {
    if (window.confirm('Are you sure you want to cancel this trade?')) {
      cancelTradeMutation.mutate();
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Trade Details" size="lg">
      <div className="space-y-6">
        {/* Trade Header */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-bold text-text-primary">
              {trade.token_symbol} - {trade.trade_type.toUpperCase()}
            </h3>
            <p className="text-text-muted">{trade.token_name || 'Unknown Token'}</p>
          </div>
          <div className="flex items-center space-x-3">
            <span
              className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(trade.status)}`}
            >
              {trade.status.toUpperCase()}
            </span>
            {trade.status === 'pending' && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancelTrade}
                disabled={cancelTradeMutation.isPending}
                className="text-red-600 border-red-600 hover:bg-red-50"
              >
                Cancel Trade
              </Button>
            )}
          </div>
        </div>

        {/* Trade Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="p-4">
            <h4 className="text-md font-semibold text-text-primary mb-3">Trade Information</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-text-secondary">Trade ID:</span>
                <span className="font-mono text-sm text-text-primary">{trade.id.slice(0, 8)}...</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Portfolio:</span>
                <span className="text-text-primary">{trade.portfolio_id?.slice(0, 8)}...</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Signal ID:</span>
                <span className="font-mono text-sm text-text-primary">
                  {trade.signal_id ? trade.signal_id.slice(0, 8) + '...' : 'Manual'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Created:</span>
                <span className="text-text-primary">{formatDate(trade.created_at)}</span>
              </div>
              {trade.executed_at && (
                <div className="flex justify-between">
                  <span className="text-text-secondary">Executed:</span>
                  <span className="text-text-primary">{formatDate(trade.executed_at)}</span>
                </div>
              )}
            </div>
          </Card>

          <Card className="p-4">
            <h4 className="text-md font-semibold text-text-primary mb-3">Financial Details</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-text-secondary">Quantity:</span>
                <span className="text-text-primary font-medium">{trade.quantity.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Price:</span>
                <span className="text-text-primary font-medium">{formatCurrency(trade.price)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Total Value:</span>
                <span className="text-text-primary font-medium">{formatCurrency(trade.total_value)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-text-secondary">Fees:</span>
                <span className="text-text-primary">{formatCurrency(trade.fees || 0)}</span>
              </div>
              {trade.pnl !== undefined && (
                <>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">P&L:</span>
                    <span
                      className={`font-bold ${
                        trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {formatCurrency(trade.pnl)}
                    </span>
                  </div>
                  {trade.pnl_percentage !== undefined && (
                    <div className="flex justify-between">
                      <span className="text-text-secondary">P&L %:</span>
                      <span
                        className={`font-bold ${
                          trade.pnl_percentage >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        {formatPercentage(trade.pnl_percentage)}
                      </span>
                    </div>
                  )}
                </>
              )}
            </div>
          </Card>
        </div>

        {/* Execution Details */}
        {executionData && (
          <Card className="p-4">
            <h4 className="text-md font-semibold text-text-primary mb-3">Execution Details</h4>
            {executionLoading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-surface-tertiary rounded mb-2"></div>
                <div className="h-4 bg-surface-tertiary rounded w-3/4"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Order ID:</span>
                    <span className="font-mono text-sm text-text-primary">
                      {executionData.execution_details.order_id}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Exchange:</span>
                    <span className="text-text-primary">{executionData.execution_details.exchange}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Execution Price:</span>
                    <span className="text-text-primary">
                      {formatCurrency(executionData.execution_details.execution_price)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Slippage:</span>
                    <span
                      className={`${
                        executionData.execution_details.slippage > 1 ? 'text-red-600' : 'text-green-600'
                      }`}
                    >
                      {formatPercentage(executionData.execution_details.slippage)}
                    </span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Exchange Fee:</span>
                    <span className="text-text-primary">
                      {formatCurrency(executionData.execution_details.fees_breakdown.exchange_fee)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Network Fee:</span>
                    <span className="text-text-primary">
                      {formatCurrency(executionData.execution_details.fees_breakdown.network_fee)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Platform Fee:</span>
                    <span className="text-text-primary">
                      {formatCurrency(executionData.execution_details.fees_breakdown.platform_fee)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Total Fees:</span>
                    <span className="text-text-primary font-medium">
                      {formatCurrency(executionData.execution_details.fees_breakdown.total_fees)}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </Card>
        )}

        {/* Market Conditions */}
        {executionData?.market_conditions && (
          <Card className="p-4">
            <h4 className="text-md font-semibold text-text-primary mb-3">Market Conditions at Execution</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-text-secondary">Market Price:</span>
                  <span className="text-text-primary">
                    {formatCurrency(executionData.market_conditions.market_price_at_execution)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-text-secondary">24h Volume:</span>
                  <span className="text-text-primary">
                    {formatCurrency(executionData.market_conditions.volume_24h)}
                  </span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-text-secondary">Volatility:</span>
                  <span className="text-text-primary">
                    {formatPercentage(executionData.market_conditions.volatility)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-text-secondary">Liquidity Score:</span>
                  <span className="text-text-primary">
                    {executionData.market_conditions.liquidity_score.toFixed(1)}/10
                  </span>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Notes */}
        <Card className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-md font-semibold text-text-primary">Notes</h4>
            {!isEditingNotes && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditingNotes(true)}
              >
                Edit
              </Button>
            )}
          </div>
          {isEditingNotes ? (
            <div className="space-y-3">
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
                rows={4}
                placeholder="Add notes about this trade..."
              />
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  onClick={handleSaveNotes}
                  disabled={updateNotesMutation.isPending}
                  className="bg-primary hover:bg-primary-dark"
                >
                  {updateNotesMutation.isPending ? 'Saving...' : 'Save'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setNotes(trade.notes || '');
                    setIsEditingNotes(false);
                  }}
                  disabled={updateNotesMutation.isPending}
                >
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-text-primary">
              {notes || 'No notes added for this trade.'}
            </div>
          )}
        </Card>

        {/* Actions */}
        <div className="flex justify-end">
          <Button onClick={onClose} className="bg-primary hover:bg-primary-dark">
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default TradeDetails;
