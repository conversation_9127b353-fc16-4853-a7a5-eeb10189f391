import React from 'react';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '@/test/utils';
import {
  mockSuccessfulQuery,
  mockLoadingQuery,
  mockErrorQuery,
  mockSuccessfulMutation,
  resetAllMocks,
} from '@/test/mocks';
import { createMockUserProfile } from '@/test/factories';
import ProfileSettings from '../components/ProfileSettings';

jest.mock('@tanstack/react-query');

describe('ProfileSettings', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    resetAllMocks();
  });

  describe('Loading State', () => {
    it('should display loading skeleton when profile is loading', () => {
      mockLoadingQuery();

      render(<ProfileSettings />);

      const skeletons = screen.getAllByTestId(/skeleton/i);
      expect(skeletons.length).toBeGreaterThan(0);
    });
  });

  describe('Success State', () => {
    it('should display profile information when data loads successfully', async () => {
      const mockProfile = createMockUserProfile({
        first_name: '<PERSON>',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        location: 'New York, NY',
      });

      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      await waitFor(() => {
        expect(screen.getByDisplayValue('John')).toBeInTheDocument();
        expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
        expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByDisplayValue('+1234567890')).toBeInTheDocument();
        expect(screen.getByDisplayValue('New York, NY')).toBeInTheDocument();
      });
    });

    it('should display profile header with name and email', async () => {
      const mockProfile = createMockUserProfile({
        first_name: 'Jane',
        last_name: 'Smith',
        email: '<EMAIL>',
      });

      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('should display account information', async () => {
      const mockProfile = createMockUserProfile({
        created_at: '2024-01-15T10:30:00Z',
        last_login: '2024-07-24T14:22:00Z',
      });

      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      await waitFor(() => {
        expect(screen.getByText('Member Since')).toBeInTheDocument();
        expect(screen.getByText('Last Login')).toBeInTheDocument();
        expect(screen.getByText('1/15/2024')).toBeInTheDocument();
      });
    });
  });

  describe('Error State', () => {
    it('should display error message when profile loading fails', async () => {
      const mockError = new Error('Failed to fetch profile');
      mockErrorQuery(mockError);

      render(<ProfileSettings />);

      await waitFor(() => {
        expect(screen.getByText(/error loading profile/i)).toBeInTheDocument();
        expect(screen.getByText(/failed to fetch profile/i)).toBeInTheDocument();
      });
    });
  });

  describe('Edit Mode', () => {
    it('should enable edit mode when edit button is clicked', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      await waitFor(() => {
        const editButton = screen.getByRole('button', { name: /edit profile/i });
        expect(editButton).toBeInTheDocument();
      });

      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
      });

      // Check that inputs are enabled
      const firstNameInput = screen.getByDisplayValue(mockProfile.first_name);
      expect(firstNameInput).not.toBeDisabled();
    });

    it('should disable inputs when not in edit mode', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      await waitFor(() => {
        const firstNameInput = screen.getByDisplayValue(mockProfile.first_name);
        expect(firstNameInput).toBeDisabled();
      });
    });

    it('should cancel edit mode and revert changes', async () => {
      const mockProfile = createMockUserProfile({
        first_name: 'John',
      });
      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      // Enter edit mode
      await waitFor(() => {
        const editButton = screen.getByRole('button', { name: /edit profile/i });
        expect(editButton).toBeInTheDocument();
      });

      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      // Make changes
      const firstNameInput = screen.getByDisplayValue('John');
      await user.clear(firstNameInput);
      await user.type(firstNameInput, 'Jane');

      expect(screen.getByDisplayValue('Jane')).toBeInTheDocument();

      // Cancel changes
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);

      await waitFor(() => {
        expect(screen.getByDisplayValue('John')).toBeInTheDocument();
        expect(screen.queryByDisplayValue('Jane')).not.toBeInTheDocument();
      });
    });
  });

  describe('Form Validation', () => {
    it('should validate required fields', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);
      mockSuccessfulMutation();

      render(<ProfileSettings />);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      // Clear required field
      const emailInput = screen.getByDisplayValue(mockProfile.email);
      await user.clear(emailInput);

      // Try to save
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });

    it('should validate email format', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);
      mockSuccessfulMutation();

      render(<ProfileSettings />);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      // Enter invalid email
      const emailInput = screen.getByDisplayValue(mockProfile.email);
      await user.clear(emailInput);
      await user.type(emailInput, 'invalid-email');

      // Try to save
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText(/invalid email format/i)).toBeInTheDocument();
      });
    });
  });

  describe('Save Functionality', () => {
    it('should save profile changes successfully', async () => {
      const mockProfile = createMockUserProfile({
        first_name: 'John',
        last_name: 'Doe',
      });
      mockSuccessfulQuery(mockProfile);
      
      const mockMutation = mockSuccessfulMutation();
      const saveMock = jest.fn();
      mockMutation.mutate = saveMock;

      render(<ProfileSettings />);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      // Make changes
      const firstNameInput = screen.getByDisplayValue('John');
      await user.clear(firstNameInput);
      await user.type(firstNameInput, 'Jane');

      const lastNameInput = screen.getByDisplayValue('Doe');
      await user.clear(lastNameInput);
      await user.type(lastNameInput, 'Smith');

      // Save changes
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      expect(saveMock).toHaveBeenCalledWith(
        expect.objectContaining({
          first_name: 'Jane',
          last_name: 'Smith',
        })
      );
    });

    it('should show loading state while saving', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);
      
      const mockMutation = {
        mutate: jest.fn(),
        isPending: true,
        isError: false,
        error: null,
      };
      mockSuccessfulMutation.mockReturnValue(mockMutation);

      render(<ProfileSettings />);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      await waitFor(() => {
        expect(screen.getByText(/saving.../i)).toBeInTheDocument();
      });
    });

    it('should handle save errors', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);
      
      const mockMutation = {
        mutate: jest.fn(),
        isPending: false,
        isError: true,
        error: new Error('Save failed'),
      };
      mockSuccessfulMutation.mockReturnValue(mockMutation);

      render(<ProfileSettings />);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      await waitFor(() => {
        expect(screen.getByText(/save failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Form Fields', () => {
    it('should handle all form field changes', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      // Test all form fields
      const firstNameInput = screen.getByLabelText(/first name/i);
      await user.clear(firstNameInput);
      await user.type(firstNameInput, 'NewFirstName');

      const lastNameInput = screen.getByLabelText(/last name/i);
      await user.clear(lastNameInput);
      await user.type(lastNameInput, 'NewLastName');

      const phoneInput = screen.getByLabelText(/phone/i);
      await user.clear(phoneInput);
      await user.type(phoneInput, '+9876543210');

      const locationInput = screen.getByLabelText(/location/i);
      await user.clear(locationInput);
      await user.type(locationInput, 'San Francisco, CA');

      const bioTextarea = screen.getByLabelText(/bio/i);
      await user.clear(bioTextarea);
      await user.type(bioTextarea, 'Updated bio text');

      // Verify changes
      expect(screen.getByDisplayValue('NewFirstName')).toBeInTheDocument();
      expect(screen.getByDisplayValue('NewLastName')).toBeInTheDocument();
      expect(screen.getByDisplayValue('+9876543210')).toBeInTheDocument();
      expect(screen.getByDisplayValue('San Francisco, CA')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Updated bio text')).toBeInTheDocument();
    });

    it('should handle timezone selection', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      // Change timezone
      const timezoneSelect = screen.getByLabelText(/timezone/i);
      await user.selectOptions(timezoneSelect, 'America/Los_Angeles');

      expect(timezoneSelect).toHaveValue('America/Los_Angeles');
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      await waitFor(() => {
        expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/phone/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/location/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/bio/i)).toBeInTheDocument();
        expect(screen.getByLabelText(/timezone/i)).toBeInTheDocument();
      });
    });

    it('should support keyboard navigation', async () => {
      const mockProfile = createMockUserProfile();
      mockSuccessfulQuery(mockProfile);

      render(<ProfileSettings />);

      // Enter edit mode
      const editButton = screen.getByRole('button', { name: /edit profile/i });
      await user.click(editButton);

      // Tab through form fields
      const firstNameInput = screen.getByLabelText(/first name/i);
      firstNameInput.focus();

      await user.tab();
      expect(screen.getByLabelText(/last name/i)).toHaveFocus();

      await user.tab();
      expect(screen.getByLabelText(/email/i)).toHaveFocus();
    });
  });
});
