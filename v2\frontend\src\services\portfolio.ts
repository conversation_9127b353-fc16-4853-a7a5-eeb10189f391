import { ApiService } from './api';
import {
  Portfolio,
  Position,
  PerformanceMetrics,
  PaginatedResponse,
  PaginationConfig,
  FilterConfig,
  SortConfig,
} from '@/types';

/**
 * Portfolio service for managing portfolios and positions
 */
export class PortfolioService {
  private static readonly BASE_URL = '/api/v1/trading/portfolios';

  /**
   * Get all portfolios with pagination and filtering
   */
  static async getPortfolios(params?: {
    pagination?: PaginationConfig;
    filters?: FilterConfig;
    sort?: SortConfig;
  }): Promise<PaginatedResponse<Portfolio>> {
    const queryParams = {
      page: params?.pagination?.page || 1,
      page_size: params?.pagination?.page_size || 20,
      ...params?.filters,
      sort_by: params?.sort?.field,
      sort_order: params?.sort?.direction,
    };

    return ApiService.get<PaginatedResponse<Portfolio>>(this.BASE_URL, queryParams);
  }

  /**
   * Get portfolio by ID
   */
  static async getPortfolio(portfolioId: string): Promise<Portfolio> {
    return ApiService.get<Portfolio>(`${this.BASE_URL}/${portfolioId}`);
  }

  /**
   * Create new portfolio
   */
  static async createPortfolio(portfolioData: {
    name: string;
    description?: string;
    initial_balance: number;
    risk_level: 'low' | 'medium' | 'high';
  }): Promise<Portfolio> {
    return ApiService.post<Portfolio>(this.BASE_URL, portfolioData);
  }

  /**
   * Update portfolio
   */
  static async updatePortfolio(
    portfolioId: string,
    portfolioData: Partial<Portfolio>
  ): Promise<Portfolio> {
    return ApiService.put<Portfolio>(`${this.BASE_URL}/${portfolioId}`, portfolioData);
  }

  /**
   * Delete portfolio
   */
  static async deletePortfolio(portfolioId: string): Promise<void> {
    return ApiService.delete<void>(`${this.BASE_URL}/${portfolioId}`);
  }

  /**
   * Get portfolio positions
   */
  static async getPortfolioPositions(portfolioId: string): Promise<Position[]> {
    return ApiService.get<Position[]>(`${this.BASE_URL}/${portfolioId}/positions`);
  }

  /**
   * Get portfolio performance metrics
   */
  static async getPortfolioPerformance(portfolioId: string, params?: {
    timeframe?: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL';
  }): Promise<PerformanceMetrics> {
    return ApiService.get<PerformanceMetrics>(
      `/api/v1/trading/performance/${portfolioId}`,
      params
    );
  }

  /**
   * Get portfolio analytics
   */
  static async getPortfolioAnalytics(portfolioId: string, params?: {
    timeframe?: '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL';
    include_positions?: boolean;
    include_trades?: boolean;
  }): Promise<{
    performance: PerformanceMetrics;
    risk_metrics: {
      volatility: number;
      beta: number;
      alpha: number;
      correlation: number;
    };
    allocation: {
      token_symbol: string;
      percentage: number;
      value: number;
    }[];
  }> {
    return ApiService.get(
      `/api/v1/trading/analytics/${portfolioId}`,
      params
    );
  }

  /**
   * Rebalance portfolio
   */
  static async rebalancePortfolio(
    portfolioId: string,
    rebalanceData: {
      target_allocations: {
        token_address: string;
        target_percentage: number;
      }[];
      rebalance_threshold?: number;
    }
  ): Promise<{
    rebalance_plan: {
      token_address: string;
      current_allocation: number;
      target_allocation: number;
      action: 'buy' | 'sell' | 'hold';
      quantity: number;
    }[];
    estimated_cost: number;
  }> {
    return ApiService.post(
      `${this.BASE_URL}/${portfolioId}/rebalance`,
      rebalanceData
    );
  }

  /**
   * Execute portfolio rebalance
   */
  static async executeRebalance(
    portfolioId: string,
    rebalancePlan: {
      token_address: string;
      action: 'buy' | 'sell';
      quantity: number;
    }[]
  ): Promise<void> {
    return ApiService.post(
      `${this.BASE_URL}/${portfolioId}/rebalance/execute`,
      { rebalance_plan: rebalancePlan }
    );
  }

  /**
   * Get portfolio risk assessment
   */
  static async getPortfolioRisk(portfolioId: string): Promise<{
    overall_risk_score: number;
    risk_level: 'low' | 'medium' | 'high';
    risk_factors: {
      factor: string;
      score: number;
      description: string;
    }[];
    recommendations: string[];
  }> {
    return ApiService.get(`${this.BASE_URL}/${portfolioId}/risk`);
  }

  /**
   * Clone portfolio
   */
  static async clonePortfolio(
    portfolioId: string,
    cloneData: {
      name: string;
      description?: string;
      copy_positions?: boolean;
    }
  ): Promise<Portfolio> {
    return ApiService.post(
      `${this.BASE_URL}/${portfolioId}/clone`,
      cloneData
    );
  }

  /**
   * Export portfolio data
   */
  static async exportPortfolio(
    portfolioId: string,
    format: 'csv' | 'json' | 'pdf'
  ): Promise<Blob> {
    const response = await fetch(
      `${this.BASE_URL}/${portfolioId}/export?format=${format}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('access_token')}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error('Export failed');
    }

    return response.blob();
  }
}
