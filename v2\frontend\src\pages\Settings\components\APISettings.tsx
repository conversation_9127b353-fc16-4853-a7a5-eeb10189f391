import React, { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Key, Eye, EyeOff, Copy, Trash2, Plus, Shield } from 'lucide-react';

const APISettings: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newApiKey, setNewApiKey] = useState({
    name: '',
    permissions: [] as string[],
  });

  const queryClient = useQueryClient();

  // Fetch API keys
  const { data: apiKeys, isLoading } = useQuery({
    queryKey: ['api-keys'],
    queryFn: async () => {
      // Mock API call
      return [
        {
          id: '1',
          name: 'Trading Bot',
          key: 'tk_live_1234567890abcdef',
          permissions: ['read', 'trade'],
          created_at: '2024-07-01T10:30:00Z',
          last_used: '2024-07-24T14:22:00Z',
          is_active: true,
        },
        {
          id: '2',
          name: 'Portfolio Tracker',
          key: 'tk_live_abcdef1234567890',
          permissions: ['read'],
          created_at: '2024-06-15T09:15:00Z',
          last_used: '2024-07-23T16:45:00Z',
          is_active: true,
        },
      ];
    },
  });

  // Create API key mutation
  const createApiKeyMutation = useMutation({
    mutationFn: async (data: any) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return {
        id: Date.now().toString(),
        name: data.name,
        key: `tk_live_${Math.random().toString(36).substring(2, 18)}`,
        secret: `sk_${Math.random().toString(36).substring(2, 32)}`,
        permissions: data.permissions,
        created_at: new Date().toISOString(),
        last_used: null,
        is_active: true,
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api-keys'] });
      setShowCreateForm(false);
      setNewApiKey({ name: '', permissions: [] });
    },
  });

  // Delete API key mutation
  const deleteApiKeyMutation = useMutation({
    mutationFn: async (keyId: string) => {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      return keyId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['api-keys'] });
    },
  });

  const handleCreateApiKey = () => {
    if (!newApiKey.name.trim()) {
      alert('Please enter a name for the API key');
      return;
    }
    if (newApiKey.permissions.length === 0) {
      alert('Please select at least one permission');
      return;
    }
    createApiKeyMutation.mutate(newApiKey);
  };

  const handleDeleteApiKey = (keyId: string, keyName: string) => {
    if (window.confirm(`Are you sure you want to delete the API key "${keyName}"? This action cannot be undone.`)) {
      deleteApiKeyMutation.mutate(keyId);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const togglePermission = (permission: string) => {
    setNewApiKey(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const permissions = [
    { id: 'read', label: 'Read', description: 'View portfolio and trade data' },
    { id: 'trade', label: 'Trade', description: 'Execute trades and manage positions' },
    { id: 'withdraw', label: 'Withdraw', description: 'Withdraw funds (high risk)' },
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse">
            <div className="h-20 bg-surface-tertiary rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-lg font-medium text-text-primary">API Keys</h4>
          <p className="text-sm text-text-secondary">
            Manage API keys for programmatic access to your account
          </p>
        </div>
        <Button
          onClick={() => setShowCreateForm(true)}
          className="bg-primary hover:bg-primary-dark"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create API Key
        </Button>
      </div>

      {/* Security Warning */}
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start">
          <Shield className="w-5 h-5 text-yellow-600 mr-3 mt-0.5" />
          <div>
            <h5 className="font-medium text-yellow-800">Security Notice</h5>
            <p className="text-sm text-yellow-700 mt-1">
              API keys provide programmatic access to your account. Keep them secure and never share them publicly.
              Only grant the minimum permissions necessary for your use case.
            </p>
          </div>
        </div>
      </div>

      {/* Create API Key Form */}
      {showCreateForm && (
        <div className="p-6 bg-surface-secondary rounded-lg border border-surface-tertiary">
          <h5 className="text-lg font-medium text-text-primary mb-4">Create New API Key</h5>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Name
              </label>
              <Input
                value={newApiKey.name}
                onChange={(e) => setNewApiKey(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter a descriptive name for this API key"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-3">
                Permissions
              </label>
              <div className="space-y-3">
                {permissions.map((permission) => (
                  <div key={permission.id} className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id={permission.id}
                      checked={newApiKey.permissions.includes(permission.id)}
                      onChange={() => togglePermission(permission.id)}
                      className="mt-1 rounded border-surface-tertiary text-primary focus:ring-primary"
                    />
                    <div>
                      <label htmlFor={permission.id} className="font-medium text-text-primary cursor-pointer">
                        {permission.label}
                      </label>
                      <p className="text-sm text-text-secondary">{permission.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={handleCreateApiKey}
                disabled={createApiKeyMutation.isPending}
                className="bg-primary hover:bg-primary-dark"
              >
                {createApiKeyMutation.isPending ? 'Creating...' : 'Create API Key'}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowCreateForm(false);
                  setNewApiKey({ name: '', permissions: [] });
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* API Keys List */}
      <div className="space-y-4">
        {apiKeys?.map((apiKey) => (
          <div key={apiKey.id} className="p-6 bg-surface-secondary rounded-lg border border-surface-tertiary">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h5 className="font-medium text-text-primary">{apiKey.name}</h5>
                <div className="flex items-center space-x-4 mt-1 text-sm text-text-secondary">
                  <span>Created: {new Date(apiKey.created_at).toLocaleDateString()}</span>
                  {apiKey.last_used && (
                    <span>Last used: {new Date(apiKey.last_used).toLocaleDateString()}</span>
                  )}
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    apiKey.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {apiKey.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDeleteApiKey(apiKey.id, apiKey.name)}
                className="text-red-600 border-red-600 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-1">
                  API Key
                </label>
                <div className="flex items-center space-x-2">
                  <code className="flex-1 px-3 py-2 bg-surface-tertiary rounded font-mono text-sm">
                    {apiKey.key}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(apiKey.key)}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Permissions
                </label>
                <div className="flex flex-wrap gap-2">
                  {apiKey.permissions.map((permission) => (
                    <span
                      key={permission}
                      className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium"
                    >
                      {permission}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}

        {(!apiKeys || apiKeys.length === 0) && !showCreateForm && (
          <div className="text-center py-12">
            <Key className="w-12 h-12 text-text-muted mx-auto mb-4" />
            <h5 className="text-lg font-medium text-text-primary mb-2">No API Keys</h5>
            <p className="text-text-secondary mb-4">
              You haven't created any API keys yet. Create one to get started with programmatic access.
            </p>
            <Button
              onClick={() => setShowCreateForm(true)}
              className="bg-primary hover:bg-primary-dark"
            >
              Create Your First API Key
            </Button>
          </div>
        )}
      </div>

      {/* API Documentation */}
      <div className="p-6 bg-surface-secondary rounded-lg border border-surface-tertiary">
        <h5 className="font-medium text-text-primary mb-3">API Documentation</h5>
        <p className="text-sm text-text-secondary mb-4">
          Learn how to use the TokenTracker API to integrate with your applications.
        </p>
        <div className="space-y-2">
          <a
            href="/docs/api"
            className="block text-sm text-primary hover:text-primary-dark"
          >
            → API Reference Documentation
          </a>
          <a
            href="/docs/quickstart"
            className="block text-sm text-primary hover:text-primary-dark"
          >
            → Quick Start Guide
          </a>
          <a
            href="/docs/examples"
            className="block text-sm text-primary hover:text-primary-dark"
          >
            → Code Examples
          </a>
        </div>
      </div>
    </div>
  );
};

export default APISettings;
