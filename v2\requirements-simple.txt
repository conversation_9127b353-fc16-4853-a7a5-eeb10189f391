# 🚀 TokenTracker V2 Dependencies - Simplified
# Core framework dependencies without version pinning to avoid conflicts

# 🌐 Web Framework & API
fastapi
uvicorn[standard]
pydantic[email]
pydantic-settings

# 🗄️ Database & Caching
pymongo
motor
redis
beanie

# 🌐 HTTP & API Clients
httpx
aiohttp
websockets
requests

# 📊 Data Processing & Analysis
pandas
numpy
scikit-learn
scipy

# 📈 Financial & Trading
solana
solders
anchorpy

# 📱 Notifications
python-telegram-bot
discord-webhook
slack-sdk

# ⏰ Scheduling & Background Tasks
apscheduler
celery

# 🔐 Security & Authentication
cryptography
passlib[bcrypt]
python-jose[cryptography]
python-multipart
PyJWT

# 📝 Logging & Monitoring
structlog
prometheus-client
sentry-sdk[fastapi]

# 🧪 Testing
pytest
pytest-asyncio
pytest-cov
pytest-mock
factory-boy

# 🔧 Development & Utilities
python-dotenv
python-dateutil
pytz
click

# 📊 Visualization & Reporting
rich
plotly
matplotlib

# 🤖 AI & Machine Learning
openai
anthropic

# 🔄 Async & Concurrency
asyncio-mqtt
aiofiles
aiocache
aiosmtplib

# 📋 Data Validation & Serialization
jinja2
marshmallow
cerberus

# 🔍 Data Processing & Utilities
jsonschema
python-slugify
arrow
humanize
tenacity

# ⚙️ Configuration Management
dynaconf
python-decouple

# 🔧 System & Performance
psutil
memory-profiler

# 🔒 Environment Security
keyring
