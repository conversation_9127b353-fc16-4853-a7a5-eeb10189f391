import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';

import { SignalsService } from '@/services/signals';
import { TradingSignal } from '@/types';
import ActiveSignals from './components/ActiveSignals';
import SignalHistory from './components/SignalHistory';
import SignalPerformance from './components/SignalPerformance';
import SignalConfiguration from './components/SignalConfiguration';
import GenerateSignalModal from './components/GenerateSignalModal';
import BacktestModal from './components/BacktestModal';

/**
 * Signals page component
 */
const Signals: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'active' | 'history' | 'performance' | 'config'>('active');
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [showBacktestModal, setShowBacktestModal] = useState(false);
  const [selectedSignal, setSelectedSignal] = useState<TradingSignal | null>(null);

  const queryClient = useQueryClient();

  // Fetch active signals
  const { data: activeSignalsData, isLoading: activeSignalsLoading, error: activeSignalsError } = useQuery({
    queryKey: ['signals', 'active'],
    queryFn: () => SignalsService.getActiveSignals(),
  });

  // Fetch signal performance
  const { data: performanceData, isLoading: performanceLoading } = useQuery({
    queryKey: ['signals', 'performance'],
    queryFn: () => SignalsService.getSignalPerformance(),
  });

  // Generate signal mutation
  const generateSignalMutation = useMutation({
    mutationFn: SignalsService.generateSignal,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['signals'] });
      setShowGenerateModal(false);
    },
  });

  const handleGenerateSignal = (signalData: any) => {
    generateSignalMutation.mutate(signalData);
  };

  const handleSignalSelect = (signal: TradingSignal) => {
    setSelectedSignal(signal);
  };

  if (activeSignalsError) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-text-primary">Trading Signals</h1>
          <p className="text-text-secondary mt-1">
            View and manage your trading signals.
          </p>
        </div>
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-4">Error Loading Signals</h2>
          <p className="text-text-muted mb-4">
            {activeSignalsError instanceof Error ? activeSignalsError.message : 'Failed to load signals'}
          </p>
          <Button onClick={() => queryClient.invalidateQueries({ queryKey: ['signals'] })}>
            Retry
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-text-primary">Trading Signals</h1>
          <p className="text-text-secondary mt-1">
            View and manage your trading signals.
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => setShowBacktestModal(true)}
          >
            Backtest Strategy
          </Button>
          <Button
            onClick={() => setShowGenerateModal(true)}
            className="bg-primary hover:bg-primary-dark"
          >
            Generate Signal
          </Button>
        </div>
      </div>

      {/* Performance Summary */}
      {performanceData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Total Signals</div>
            <div className="text-2xl font-bold text-text-primary">
              {performanceData.total_signals}
            </div>
            <div className="text-sm text-text-muted">
              {performanceData.successful_signals} successful
            </div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Success Rate</div>
            <div className="text-2xl font-bold text-green-600">
              {(performanceData.success_rate * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-text-muted">Signal accuracy</div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Average Return</div>
            <div
              className={`text-2xl font-bold ${
                performanceData.average_return >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {performanceData.average_return >= 0 ? '+' : ''}{performanceData.average_return.toFixed(2)}%
            </div>
            <div className="text-sm text-text-muted">Per signal</div>
          </Card>

          <Card className="p-4">
            <div className="text-sm text-text-secondary mb-1">Active Signals</div>
            <div className="text-2xl font-bold text-text-primary">
              {activeSignalsData?.data?.length || 0}
            </div>
            <div className="text-sm text-text-muted">Currently active</div>
          </Card>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="flex space-x-1">
        {[
          { key: 'active', label: 'Active Signals' },
          { key: 'history', label: 'Signal History' },
          { key: 'performance', label: 'Performance' },
          { key: 'config', label: 'Configuration' },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-primary text-white'
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-tertiary'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'active' && (
        <ActiveSignals
          signals={activeSignalsData?.data || []}
          isLoading={activeSignalsLoading}
          onSignalSelect={handleSignalSelect}
        />
      )}

      {activeTab === 'history' && (
        <SignalHistory
          onSignalSelect={handleSignalSelect}
        />
      )}

      {activeTab === 'performance' && (
        <SignalPerformance
          performanceData={performanceData || null}
          isLoading={performanceLoading}
        />
      )}

      {activeTab === 'config' && (
        <SignalConfiguration />
      )}

      {/* Modals */}
      <GenerateSignalModal
        isOpen={showGenerateModal}
        onClose={() => setShowGenerateModal(false)}
        onSubmit={handleGenerateSignal}
        isLoading={generateSignalMutation.isPending}
      />

      <BacktestModal
        isOpen={showBacktestModal}
        onClose={() => setShowBacktestModal(false)}
      />
    </div>
  );
};

export default Signals;
