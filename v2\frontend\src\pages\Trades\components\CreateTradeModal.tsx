import React, { useState } from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

interface CreateTradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (tradeData: {
    portfolio_id: string;
    token_address: string;
    trade_type: 'buy' | 'sell';
    quantity: number;
    price: number;
    notes?: string;
  }) => void;
  isLoading: boolean;
}

const CreateTradeModal: React.FC<CreateTradeModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading,
}) => {
  const [formData, setFormData] = useState({
    portfolio_id: '',
    token_address: '',
    trade_type: 'buy' as 'buy' | 'sell',
    quantity: 0,
    price: 0,
    notes: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    const newErrors: Record<string, string> = {};
    
    if (!formData.portfolio_id.trim()) {
      newErrors.portfolio_id = 'Portfolio ID is required';
    }
    
    if (!formData.token_address.trim()) {
      newErrors.token_address = 'Token address is required';
    }
    
    if (formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }
    
    if (formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0';
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    setErrors({});
    onSubmit({
      portfolio_id: formData.portfolio_id.trim(),
      token_address: formData.token_address.trim(),
      trade_type: formData.trade_type,
      quantity: formData.quantity,
      price: formData.price,
      notes: formData.notes.trim() || undefined,
    });
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({
        portfolio_id: '',
        token_address: '',
        trade_type: 'buy',
        quantity: 0,
        price: 0,
        notes: '',
      });
      setErrors({});
      onClose();
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const totalValue = formData.quantity * formData.price;

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Add Manual Trade">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="portfolio_id" className="block text-sm font-medium text-text-primary mb-2">
            Portfolio ID *
          </label>
          <Input
            id="portfolio_id"
            type="text"
            value={formData.portfolio_id}
            onChange={(e) => handleInputChange('portfolio_id', e.target.value)}
            placeholder="Enter portfolio ID"
            className={errors.portfolio_id ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {errors.portfolio_id && (
            <p className="mt-1 text-sm text-red-600">{errors.portfolio_id}</p>
          )}
        </div>

        <div>
          <label htmlFor="token_address" className="block text-sm font-medium text-text-primary mb-2">
            Token Address *
          </label>
          <Input
            id="token_address"
            type="text"
            value={formData.token_address}
            onChange={(e) => handleInputChange('token_address', e.target.value)}
            placeholder="Enter Solana token address"
            className={errors.token_address ? 'border-red-500' : ''}
            disabled={isLoading}
          />
          {errors.token_address && (
            <p className="mt-1 text-sm text-red-600">{errors.token_address}</p>
          )}
        </div>

        <div>
          <label htmlFor="trade_type" className="block text-sm font-medium text-text-primary mb-2">
            Trade Type *
          </label>
          <select
            id="trade_type"
            value={formData.trade_type}
            onChange={(e) => handleInputChange('trade_type', e.target.value)}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={isLoading}
          >
            <option value="buy">Buy - Purchase tokens</option>
            <option value="sell">Sell - Sell tokens</option>
          </select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="quantity" className="block text-sm font-medium text-text-primary mb-2">
              Quantity *
            </label>
            <Input
              id="quantity"
              type="number"
              value={formData.quantity}
              onChange={(e) => handleInputChange('quantity', parseFloat(e.target.value) || 0)}
              placeholder="Enter quantity"
              min="0"
              step="0.000001"
              className={errors.quantity ? 'border-red-500' : ''}
              disabled={isLoading}
            />
            {errors.quantity && (
              <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>
            )}
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-text-primary mb-2">
              Price (USD) *
            </label>
            <Input
              id="price"
              type="number"
              value={formData.price}
              onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
              placeholder="Enter price per token"
              min="0"
              step="0.000001"
              className={errors.price ? 'border-red-500' : ''}
              disabled={isLoading}
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-600">{errors.price}</p>
            )}
          </div>
        </div>

        {totalValue > 0 && (
          <div className="p-4 bg-surface-tertiary rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-text-primary">Total Value:</span>
              <span className="text-lg font-bold text-text-primary">
                {new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'USD',
                  minimumFractionDigits: 2,
                }).format(totalValue)}
              </span>
            </div>
          </div>
        )}

        <div>
          <label htmlFor="notes" className="block text-sm font-medium text-text-primary mb-2">
            Notes
          </label>
          <textarea
            id="notes"
            value={formData.notes}
            onChange={(e) => handleInputChange('notes', e.target.value)}
            placeholder="Add notes about this trade (optional)"
            rows={3}
            className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            disabled={isLoading}
          />
        </div>

        <div className="bg-surface-tertiary p-4 rounded-lg">
          <h4 className="text-sm font-medium text-text-primary mb-2">Important Notes</h4>
          <div className="space-y-1 text-sm text-text-secondary">
            <p>• Manual trades are recorded for tracking purposes only</p>
            <p>• Ensure the trade details are accurate as they cannot be automatically verified</p>
            <p>• P&L calculations will be based on current market prices</p>
            <p>• This trade will appear in your portfolio and analytics</p>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="bg-primary hover:bg-primary-dark"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Adding...</span>
              </div>
            ) : (
              'Add Trade'
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default CreateTradeModal;
