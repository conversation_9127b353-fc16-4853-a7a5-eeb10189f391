import React from 'react';
import { screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '@/test/utils';
import {
  mockSuccessfulQuery,
  mockLoadingQuery,
  mockErrorQuery,
  mockSuccessfulMutation,
  resetAllMocks,
} from '@/test/mocks';
import {
  createMockTradingSignals,
  createMockPaginatedResponse,
} from '@/test/factories';
import ActiveSignals from '../components/ActiveSignals';

jest.mock('@tanstack/react-query');

describe('ActiveSignals', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    resetAllMocks();
  });

  describe('Loading State', () => {
    it('should display loading skeleton when signals are loading', () => {
      mockLoadingQuery();

      render(<ActiveSignals />);

      const skeletons = screen.getAllByTestId(/skeleton/i);
      expect(skeletons.length).toBeGreaterThan(0);
    });
  });

  describe('Success State', () => {
    it('should display active signals when data loads successfully', async () => {
      const mockSignals = createMockTradingSignals(3);
      const mockResponse = createMockPaginatedResponse(mockSignals);
      
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        mockSignals.forEach(signal => {
          expect(screen.getByText(signal.token_symbol)).toBeInTheDocument();
          expect(screen.getByText(signal.signal_type.toUpperCase())).toBeInTheDocument();
        });
      });
    });

    it('should display signal details correctly', async () => {
      const mockSignals = createMockTradingSignals(1);
      const signal = mockSignals[0];
      const mockResponse = createMockPaginatedResponse(mockSignals);
      
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        expect(screen.getByText(signal.token_symbol)).toBeInTheDocument();
        expect(screen.getByText(signal.signal_type.toUpperCase())).toBeInTheDocument();
        expect(screen.getByText(`${signal.confidence}%`)).toBeInTheDocument();
        expect(screen.getByText(`$${signal.current_price.toFixed(2)}`)).toBeInTheDocument();
        expect(screen.getByText(`$${signal.target_price.toFixed(2)}`)).toBeInTheDocument();
      });
    });

    it('should display confidence level with appropriate color', async () => {
      const mockSignals = [
        { ...createMockTradingSignals(1)[0], confidence: 90 }, // High confidence
        { ...createMockTradingSignals(1)[0], id: 'signal-2', confidence: 60 }, // Medium confidence
        { ...createMockTradingSignals(1)[0], id: 'signal-3', confidence: 40 }, // Low confidence
      ];
      const mockResponse = createMockPaginatedResponse(mockSignals);
      
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        const highConfidence = screen.getByText('90%');
        const mediumConfidence = screen.getByText('60%');
        const lowConfidence = screen.getByText('40%');

        expect(highConfidence).toBeInTheDocument();
        expect(mediumConfidence).toBeInTheDocument();
        expect(lowConfidence).toBeInTheDocument();

        // Check color classes
        expect(highConfidence.closest('div')).toHaveClass('text-green-600');
        expect(mediumConfidence.closest('div')).toHaveClass('text-yellow-600');
        expect(lowConfidence.closest('div')).toHaveClass('text-red-600');
      });
    });

    it('should display buy signals with green styling', async () => {
      const mockSignals = createMockTradingSignals(1);
      mockSignals[0].signal_type = 'buy';
      const mockResponse = createMockPaginatedResponse(mockSignals);
      
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        const buySignal = screen.getByText('BUY');
        expect(buySignal).toBeInTheDocument();
        expect(buySignal.closest('span')).toHaveClass('bg-green-100', 'text-green-800');
      });
    });

    it('should display sell signals with red styling', async () => {
      const mockSignals = createMockTradingSignals(1);
      mockSignals[0].signal_type = 'sell';
      const mockResponse = createMockPaginatedResponse(mockSignals);
      
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        const sellSignal = screen.getByText('SELL');
        expect(sellSignal).toBeInTheDocument();
        expect(sellSignal.closest('span')).toHaveClass('bg-red-100', 'text-red-800');
      });
    });
  });

  describe('Error State', () => {
    it('should display error message when signals loading fails', async () => {
      const mockError = new Error('Failed to fetch signals');
      mockErrorQuery(mockError);

      render(<ActiveSignals />);

      await waitFor(() => {
        expect(screen.getByText(/error loading signals/i)).toBeInTheDocument();
        expect(screen.getByText(/failed to fetch signals/i)).toBeInTheDocument();
      });
    });

    it('should display retry button on error', async () => {
      const mockError = new Error('Network error');
      mockErrorQuery(mockError);

      render(<ActiveSignals />);

      await waitFor(() => {
        const retryButton = screen.getByRole('button', { name: /try again/i });
        expect(retryButton).toBeInTheDocument();
      });
    });
  });

  describe('Empty State', () => {
    it('should display empty state when no active signals exist', async () => {
      const mockResponse = createMockPaginatedResponse([]);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        expect(screen.getByText(/no active signals/i)).toBeInTheDocument();
        expect(screen.getByText(/generate new signals to see them here/i)).toBeInTheDocument();
      });
    });

    it('should show generate signal button in empty state', async () => {
      const mockResponse = createMockPaginatedResponse([]);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        const generateButton = screen.getByRole('button', { name: /generate signals/i });
        expect(generateButton).toBeInTheDocument();
      });
    });
  });

  describe('Signal Actions', () => {
    it('should show execute button for each signal', async () => {
      const mockSignals = createMockTradingSignals(1);
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);
      mockSuccessfulMutation();

      render(<ActiveSignals />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /execute/i })).toBeInTheDocument();
      });
    });

    it('should handle signal execution', async () => {
      const mockSignals = createMockTradingSignals(1);
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);
      
      const mockMutation = mockSuccessfulMutation();
      const executeMock = jest.fn();
      mockMutation.mutate = executeMock;

      render(<ActiveSignals />);

      await waitFor(() => {
        const executeButton = screen.getByRole('button', { name: /execute/i });
        expect(executeButton).toBeInTheDocument();
      });

      const executeButton = screen.getByRole('button', { name: /execute/i });
      await user.click(executeButton);

      expect(executeMock).toHaveBeenCalledWith(mockSignals[0].id);
    });

    it('should show dismiss button for each signal', async () => {
      const mockSignals = createMockTradingSignals(1);
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /dismiss/i })).toBeInTheDocument();
      });
    });

    it('should handle signal dismissal', async () => {
      const mockSignals = createMockTradingSignals(1);
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);
      
      const mockMutation = mockSuccessfulMutation();
      const dismissMock = jest.fn();
      mockMutation.mutate = dismissMock;

      render(<ActiveSignals />);

      await waitFor(() => {
        const dismissButton = screen.getByRole('button', { name: /dismiss/i });
        expect(dismissButton).toBeInTheDocument();
      });

      const dismissButton = screen.getByRole('button', { name: /dismiss/i });
      await user.click(dismissButton);

      expect(dismissMock).toHaveBeenCalledWith(mockSignals[0].id);
    });
  });

  describe('Filtering and Sorting', () => {
    it('should filter signals by signal type', async () => {
      const mockSignals = [
        { ...createMockTradingSignals(1)[0], signal_type: 'buy' as const },
        { ...createMockTradingSignals(1)[0], id: 'signal-2', signal_type: 'sell' as const },
        { ...createMockTradingSignals(1)[0], id: 'signal-3', signal_type: 'hold' as const },
      ];
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        expect(screen.getByText('BUY')).toBeInTheDocument();
        expect(screen.getByText('SELL')).toBeInTheDocument();
        expect(screen.getByText('HOLD')).toBeInTheDocument();
      });

      // Filter by buy signals
      const buyFilter = screen.getByRole('button', { name: /buy/i });
      await user.click(buyFilter);

      await waitFor(() => {
        expect(screen.getByText('BUY')).toBeInTheDocument();
        expect(screen.queryByText('SELL')).not.toBeInTheDocument();
        expect(screen.queryByText('HOLD')).not.toBeInTheDocument();
      });
    });

    it('should sort signals by confidence', async () => {
      const mockSignals = createMockTradingSignals(3);
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        expect(screen.getByText('Confidence')).toBeInTheDocument();
      });

      // Click on confidence header to sort
      const confidenceHeader = screen.getByText('Confidence');
      await user.click(confidenceHeader);

      // Verify sorting indicator appears
      expect(confidenceHeader.closest('th')).toContainHTML('mock-icon');
    });
  });

  describe('Real-time Updates', () => {
    it('should update signal data when new signals arrive', async () => {
      const mockSignals = createMockTradingSignals(2);
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        expect(screen.getAllByText(/signal-/)).toHaveLength(2);
      });

      // Simulate new signal arrival
      const updatedSignals = [...mockSignals, createMockTradingSignals(1)[0]];
      const updatedResponse = createMockPaginatedResponse(updatedSignals);
      mockSuccessfulQuery(updatedResponse);

      // Trigger refetch (this would happen automatically in real app)
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      if (refreshButton) {
        await user.click(refreshButton);
      }
    });
  });

  describe('Signal Expiration', () => {
    it('should highlight signals that are about to expire', async () => {
      const mockSignals = createMockTradingSignals(1);
      // Set expiration to 1 hour from now
      const oneHourFromNow = new Date(Date.now() + 60 * 60 * 1000).toISOString();
      mockSignals[0].expires_at = oneHourFromNow;
      
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        const expirationWarning = screen.getByText(/expires in/i);
        expect(expirationWarning).toBeInTheDocument();
        expect(expirationWarning.closest('div')).toHaveClass('text-orange-600');
      });
    });

    it('should show expired signals with different styling', async () => {
      const mockSignals = createMockTradingSignals(1);
      // Set expiration to past
      const pastTime = new Date(Date.now() - 60 * 60 * 1000).toISOString();
      mockSignals[0].expires_at = pastTime;
      mockSignals[0].status = 'expired';
      
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        const expiredSignal = screen.getByText(/expired/i);
        expect(expiredSignal).toBeInTheDocument();
        expect(expiredSignal.closest('div')).toHaveClass('text-red-600');
      });
    });
  });

  describe('Responsive Design', () => {
    it('should adapt layout for mobile screens', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });

      const mockSignals = createMockTradingSignals(3);
      const mockResponse = createMockPaginatedResponse(mockSignals);
      mockSuccessfulQuery(mockResponse);

      render(<ActiveSignals />);

      await waitFor(() => {
        // On mobile, signals should be displayed as cards
        const container = screen.getByTestId('signals-container');
        expect(container).toBeInTheDocument();
      });
    });
  });
});
