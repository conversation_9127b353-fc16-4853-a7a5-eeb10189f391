"""
Dashboard Service

Provides real-time portfolio view, signal monitoring, and performance charts
for the web interface. Follows clean code principles with <40 line functions
and proper separation of concerns.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

import structlog
from fastapi import <PERSON><PERSON><PERSON><PERSON>xception

from src.shared.types import (
    PortfolioSummary, SignalData, PerformanceMetrics,
    DashboardData, ChartData, RealTimeUpdate
)
from src.features.paper_trading.portfolio_manager import PortfolioManager
from src.features.signal_processing.signal_generator import SignalGenerator
from src.features.paper_trading.performance_tracker import PerformanceTracker
from src.features.data_pipeline.cache_manager import CacheManager

logger = structlog.get_logger(__name__)


@dataclass
class DashboardConfig:
    """Dashboard configuration settings"""
    refresh_interval: int = 30  # seconds
    chart_data_points: int = 100
    signal_history_days: int = 7
    performance_period_days: int = 30
    cache_ttl: int = 60  # seconds


class DashboardService:
    """
    Real-time dashboard service providing portfolio overview,
    signal monitoring, and performance analytics.
    """
    
    def __init__(
        self,
        portfolio_manager: PortfolioManager,
        signal_generator: SignalGenerator,
        performance_tracker: PerformanceTracker,
        cache_manager: CacheManager,
        config: Optional[DashboardConfig] = None
    ):
        self.portfolio_manager = portfolio_manager
        self.signal_generator = signal_generator
        self.performance_tracker = performance_tracker
        self.cache_manager = cache_manager
        self.config = config or DashboardConfig()
        self.logger = logger.bind(service="dashboard")
    
    async def get_dashboard_data(self, user_id: str) -> DashboardData:
        """
        Get comprehensive dashboard data for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Complete dashboard data including portfolio, signals, and performance
        """
        try:
            cache_key = f"dashboard:{user_id}"
            cached_data = await self.cache_manager.get(cache_key)
            
            if cached_data:
                self.logger.info("Dashboard data served from cache", user_id=user_id)
                return DashboardData(**cached_data)
            
            # Fetch all dashboard components concurrently
            portfolio_task = self._get_portfolio_summary(user_id)
            signals_task = self._get_recent_signals(user_id)
            performance_task = self._get_performance_metrics(user_id)
            charts_task = self._get_chart_data(user_id)
            
            portfolio, signals, performance, charts = await asyncio.gather(
                portfolio_task, signals_task, performance_task, charts_task
            )
            
            dashboard_data = DashboardData(
                portfolio=portfolio,
                recent_signals=signals,
                performance=performance,
                charts=charts,
                last_updated=datetime.utcnow()
            )
            
            # Cache the result
            await self.cache_manager.set(
                cache_key, 
                dashboard_data.dict(), 
                ttl=self.config.cache_ttl
            )
            
            self.logger.info("Dashboard data generated", user_id=user_id)
            return dashboard_data
            
        except Exception as e:
            self.logger.error("Failed to get dashboard data", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to load dashboard")
    
    async def _get_portfolio_summary(self, user_id: str) -> PortfolioSummary:
        """Get portfolio summary for dashboard"""
        try:
            portfolio = await self.portfolio_manager.get_portfolio(user_id)
            if not portfolio:
                return PortfolioSummary(
                    total_value=0.0,
                    total_pnl=0.0,
                    total_pnl_percentage=0.0,
                    active_positions=0,
                    available_balance=0.0
                )
            
            # Calculate total PnL percentage
            total_pnl_percentage = 0.0
            if portfolio.initial_balance > 0:
                total_pnl_percentage = float((portfolio.total_pnl / portfolio.initial_balance) * 100)

            return PortfolioSummary(
                total_value=float(portfolio.total_value),
                total_pnl=float(portfolio.total_pnl),
                total_pnl_percentage=total_pnl_percentage,
                active_positions=0,  # We'll need to get this from positions collection
                available_balance=float(portfolio.current_balance)
            )
            
        except Exception as e:
            self.logger.error("Failed to get portfolio summary", user_id=user_id, error=str(e))
            raise
    
    async def _get_recent_signals(self, user_id: str) -> List[SignalData]:
        """Get recent signals for dashboard"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=self.config.signal_history_days)
            
            signals = await self.signal_generator.get_signals_by_date_range(
                start_date=start_date,
                end_date=end_date,
                limit=20
            )
            
            return signals
            
        except Exception as e:
            self.logger.error("Failed to get recent signals", user_id=user_id, error=str(e))
            return []
    
    async def _get_performance_metrics(self, user_id: str) -> PerformanceMetrics:
        """Get performance metrics for dashboard"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=self.config.performance_period_days)
            
            metrics = await self.performance_tracker.calculate_performance_metrics(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error("Failed to get performance metrics", user_id=user_id, error=str(e))
            # Return default metrics on error
            return PerformanceMetrics(
                sharpe_ratio=0.0,
                max_drawdown=0.0,
                win_rate=0.0,
                profit_factor=0.0,
                total_return=0.0
            )
    
    async def _get_chart_data(self, user_id: str) -> Dict[str, ChartData]:
        """Get chart data for dashboard"""
        try:
            # Get portfolio value chart
            portfolio_chart = await self._get_portfolio_chart(user_id)
            
            # Get performance chart
            performance_chart = await self._get_performance_chart(user_id)
            
            # Get signals chart
            signals_chart = await self._get_signals_chart(user_id)
            
            return {
                "portfolio": portfolio_chart,
                "performance": performance_chart,
                "signals": signals_chart
            }
            
        except Exception as e:
            self.logger.error("Failed to get chart data", user_id=user_id, error=str(e))
            return {}
    
    async def _get_portfolio_chart(self, user_id: str) -> ChartData:
        """Get portfolio value chart data"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            # Get daily portfolio snapshots
            snapshots = await self.portfolio_manager.get_portfolio_history(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            labels = [snapshot.date.strftime("%Y-%m-%d") for snapshot in snapshots]
            values = [snapshot.total_value for snapshot in snapshots]
            
            return ChartData(
                labels=labels,
                datasets=[{
                    "label": "Portfolio Value",
                    "data": values,
                    "borderColor": "#3B82F6",
                    "backgroundColor": "rgba(59, 130, 246, 0.1)"
                }]
            )
            
        except Exception as e:
            self.logger.error("Failed to get portfolio chart", user_id=user_id, error=str(e))
            return ChartData(labels=[], datasets=[])
    
    async def _get_performance_chart(self, user_id: str) -> ChartData:
        """Get performance chart data"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=30)
            
            # Get daily performance metrics
            daily_metrics = await self.performance_tracker.get_daily_metrics(
                user_id=user_id,
                start_date=start_date,
                end_date=end_date
            )
            
            labels = [metric.date.strftime("%Y-%m-%d") for metric in daily_metrics]
            returns = [metric.daily_return for metric in daily_metrics]
            
            return ChartData(
                labels=labels,
                datasets=[{
                    "label": "Daily Returns",
                    "data": returns,
                    "borderColor": "#10B981",
                    "backgroundColor": "rgba(16, 185, 129, 0.1)"
                }]
            )
            
        except Exception as e:
            self.logger.error("Failed to get performance chart", user_id=user_id, error=str(e))
            return ChartData(labels=[], datasets=[])
    
    async def _get_signals_chart(self, user_id: str) -> ChartData:
        """Get signals chart data"""
        try:
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=7)
            
            # Get signal counts by day
            signal_counts = await self.signal_generator.get_signal_counts_by_date(
                start_date=start_date,
                end_date=end_date
            )
            
            labels = [count.date.strftime("%Y-%m-%d") for count in signal_counts]
            values = [count.count for count in signal_counts]
            
            return ChartData(
                labels=labels,
                datasets=[{
                    "label": "Signals Generated",
                    "data": values,
                    "borderColor": "#F59E0B",
                    "backgroundColor": "rgba(245, 158, 11, 0.1)"
                }]
            )
            
        except Exception as e:
            self.logger.error("Failed to get signals chart", user_id=user_id, error=str(e))
            return ChartData(labels=[], datasets=[])
    
    async def get_real_time_updates(self, user_id: str) -> RealTimeUpdate:
        """
        Get real-time updates for dashboard.
        
        Args:
            user_id: User identifier
            
        Returns:
            Real-time update data
        """
        try:
            # Get latest portfolio value
            portfolio = await self.portfolio_manager.get_portfolio(user_id)
            current_value = portfolio.total_value if portfolio else 0.0
            
            # Get latest signals count
            today = datetime.utcnow().date()
            signals_today = await self.signal_generator.get_signals_count_for_date(today)
            
            # Get latest performance
            latest_performance = await self.performance_tracker.get_latest_performance(user_id)
            
            return RealTimeUpdate(
                portfolio_value=current_value,
                signals_today=signals_today,
                daily_pnl=latest_performance.daily_pnl if latest_performance else 0.0,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            self.logger.error("Failed to get real-time updates", user_id=user_id, error=str(e))
            raise HTTPException(status_code=500, detail="Failed to get real-time updates")
    
    async def invalidate_cache(self, user_id: str) -> bool:
        """
        Invalidate dashboard cache for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            True if cache was invalidated successfully
        """
        try:
            cache_key = f"dashboard:{user_id}"
            await self.cache_manager.delete(cache_key)
            self.logger.info("Dashboard cache invalidated", user_id=user_id)
            return True
            
        except Exception as e:
            self.logger.error("Failed to invalidate cache", user_id=user_id, error=str(e))
            return False
