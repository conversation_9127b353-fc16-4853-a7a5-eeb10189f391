# ===========================================
# 🐳 TOKENTRACKER V2 DOCKERIGNORE
# ===========================================
# Optimize Docker build context for poor network conditions

# Version control
.git
.gitignore
.gitattributes
.github/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Testing and coverage
.pytest_cache/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
htmlcov/
.tox/
.nox/

# Documentation
docs/
*.md
README*
CHANGELOG*
LICENSE*
CONTRIBUTING*

# Logs and temporary files
*.log
logs/
tmp/
temp/
.tmp/

# Database files
*.db
*.sqlite
*.sqlite3

# Environment files (keep .env.example)
.env
.env.local
.env.development
.env.test
.env.production

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker files (except the one being built)
Dockerfile.*
docker-compose*.yml
.dockerignore

# CI/CD
.travis.yml
.circleci/
.github/workflows/
Jenkinsfile

# Backup files
*.bak
*.backup
*.old

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Large data files that shouldn't be in container
data/
datasets/
models/
*.csv
*.json
*.xml
*.parquet

# Cache directories
.cache/
cache/
.pip/
