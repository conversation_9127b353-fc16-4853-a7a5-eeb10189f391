import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { Portfolio, Position } from '@/types';
import { PortfolioService } from '@/services/portfolio';

interface RebalanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  portfolio: Portfolio;
  positions: Position[];
}

const RebalanceModal: React.FC<RebalanceModalProps> = ({
  isOpen,
  onClose,
  portfolio,
  positions,
}) => {
  const [targetAllocations, setTargetAllocations] = useState<Record<string, number>>({});
  const [rebalanceThreshold, setRebalanceThreshold] = useState(5);
  const [rebalancePlan, setRebalancePlan] = useState<any>(null);
  const [step, setStep] = useState<'configure' | 'preview' | 'executing'>('configure');

  const queryClient = useQueryClient();

  // Calculate rebalance plan
  const calculateRebalanceMutation = useMutation({
    mutationFn: (data: any) => PortfolioService.rebalancePortfolio(portfolio.id, data),
    onSuccess: (data) => {
      setRebalancePlan(data);
      setStep('preview');
    },
  });

  // Execute rebalance
  const executeRebalanceMutation = useMutation({
    mutationFn: (plan: any) => PortfolioService.executeRebalance(portfolio.id, plan),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolio-positions', portfolio.id] });
      queryClient.invalidateQueries({ queryKey: ['portfolio', portfolio.id] });
      onClose();
      setStep('configure');
      setRebalancePlan(null);
    },
  });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  // Initialize target allocations based on current positions
  React.useEffect(() => {
    if (positions.length > 0 && Object.keys(targetAllocations).length === 0) {
      const totalValue = positions.reduce((sum, pos) => sum + pos.market_value, 0);
      const allocations: Record<string, number> = {};
      
      positions.forEach(position => {
        allocations[position.token_address] = (position.market_value / totalValue) * 100;
      });
      
      setTargetAllocations(allocations);
    }
  }, [positions, targetAllocations]);

  const handleAllocationChange = (tokenAddress: string, percentage: number) => {
    setTargetAllocations(prev => ({
      ...prev,
      [tokenAddress]: Math.max(0, Math.min(100, percentage))
    }));
  };

  const handleCalculateRebalance = () => {
    const totalAllocation = Object.values(targetAllocations).reduce((sum, val) => sum + val, 0);
    
    if (Math.abs(totalAllocation - 100) > 0.1) {
      alert('Total allocation must equal 100%');
      return;
    }

    const rebalanceData = {
      target_allocations: Object.entries(targetAllocations).map(([token_address, target_percentage]) => ({
        token_address,
        target_percentage,
      })),
      rebalance_threshold: rebalanceThreshold,
    };

    calculateRebalanceMutation.mutate(rebalanceData);
  };

  const handleExecuteRebalance = () => {
    if (!rebalancePlan) return;
    
    setStep('executing');
    const planToExecute = rebalancePlan.rebalance_plan
      .filter((item: any) => item.action !== 'hold')
      .map((item: any) => ({
        token_address: item.token_address,
        action: item.action,
        quantity: item.quantity,
      }));

    executeRebalanceMutation.mutate(planToExecute);
  };

  const handleClose = () => {
    if (step !== 'executing') {
      setStep('configure');
      setRebalancePlan(null);
      onClose();
    }
  };

  const totalAllocation = Object.values(targetAllocations).reduce((sum, val) => sum + val, 0);

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Portfolio Rebalancing" size="lg">
      <div className="space-y-6">
        {step === 'configure' && (
          <>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-4">Configure Target Allocation</h3>
              <p className="text-text-secondary mb-4">
                Set your desired allocation percentages for each position. The total must equal 100%.
              </p>
            </div>

            <div className="space-y-4">
              {positions.map((position) => {
                const currentAllocation = (position.market_value / positions.reduce((sum, p) => sum + p.market_value, 0)) * 100;
                const targetAllocation = targetAllocations[position.token_address] || 0;
                
                return (
                  <div key={position.id} className="p-4 border border-surface-tertiary rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <span className="font-medium text-text-primary">{position.token_symbol}</span>
                        <span className="text-sm text-text-muted ml-2">{position.token_name}</span>
                      </div>
                      <div className="text-sm text-text-secondary">
                        Current: {formatPercentage(currentAllocation)}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex-1">
                        <input
                          type="range"
                          min="0"
                          max="100"
                          step="0.1"
                          value={targetAllocation}
                          onChange={(e) => handleAllocationChange(position.token_address, parseFloat(e.target.value))}
                          className="w-full"
                        />
                      </div>
                      <div className="w-20">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          value={targetAllocation.toFixed(1)}
                          onChange={(e) => handleAllocationChange(position.token_address, parseFloat(e.target.value) || 0)}
                          className="w-full px-2 py-1 border border-surface-tertiary rounded text-sm bg-surface-secondary text-text-primary"
                        />
                      </div>
                      <span className="text-sm text-text-secondary">%</span>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="p-4 bg-surface-tertiary rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium text-text-primary">Total Allocation:</span>
                <span className={`font-bold ${Math.abs(totalAllocation - 100) < 0.1 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatPercentage(totalAllocation)}
                </span>
              </div>
              {Math.abs(totalAllocation - 100) > 0.1 && (
                <p className="text-sm text-red-600">
                  Total allocation must equal 100%. Current difference: {formatPercentage(totalAllocation - 100)}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Rebalance Threshold (%)
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={rebalanceThreshold}
                onChange={(e) => setRebalanceThreshold(parseInt(e.target.value) || 5)}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
              />
              <p className="text-sm text-text-muted mt-1">
                Only rebalance positions that differ from target by more than this percentage.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                onClick={handleCalculateRebalance}
                disabled={Math.abs(totalAllocation - 100) > 0.1 || calculateRebalanceMutation.isPending}
                className="bg-primary hover:bg-primary-dark"
              >
                {calculateRebalanceMutation.isPending ? 'Calculating...' : 'Calculate Rebalance'}
              </Button>
            </div>
          </>
        )}

        {step === 'preview' && rebalancePlan && (
          <>
            <div>
              <h3 className="text-lg font-semibold text-text-primary mb-4">Rebalance Preview</h3>
              <p className="text-text-secondary mb-4">
                Review the proposed changes before executing the rebalance.
              </p>
            </div>

            <div className="p-4 bg-surface-tertiary rounded-lg mb-4">
              <div className="text-sm text-text-secondary mb-1">Estimated Cost</div>
              <div className="text-xl font-bold text-text-primary">
                {formatCurrency(rebalancePlan.estimated_cost)}
              </div>
            </div>

            <div className="space-y-3">
              {rebalancePlan.rebalance_plan.map((item: any, index: number) => {
                const position = positions.find(p => p.token_address === item.token_address);
                return (
                  <div key={index} className="p-4 border border-surface-tertiary rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-medium text-text-primary">
                          {position?.token_symbol || 'Unknown'}
                        </span>
                        <span
                          className={`ml-2 px-2 py-1 text-xs rounded-full ${
                            item.action === 'buy'
                              ? 'bg-green-100 text-green-800'
                              : item.action === 'sell'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {item.action.toUpperCase()}
                        </span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-text-primary">
                          Quantity: {item.quantity.toLocaleString()}
                        </div>
                        <div className="text-xs text-text-secondary">
                          {formatPercentage(item.current_allocation)} → {formatPercentage(item.target_allocation)}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex justify-end space-x-3">
              <Button variant="outline" onClick={() => setStep('configure')}>
                Back
              </Button>
              <Button
                onClick={handleExecuteRebalance}
                className="bg-primary hover:bg-primary-dark"
              >
                Execute Rebalance
              </Button>
            </div>
          </>
        )}

        {step === 'executing' && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-text-primary mb-2">Executing Rebalance</h3>
            <p className="text-text-secondary">
              Please wait while we execute your rebalance plan...
            </p>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default RebalanceModal;
