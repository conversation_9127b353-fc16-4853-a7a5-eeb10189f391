import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test setup...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Setup test database/environment
    console.log('📊 Setting up test database...');
    
    // In a real application, you might:
    // 1. Create a test database
    // 2. Seed it with test data
    // 3. Start mock services
    // 4. Configure test environment variables

    // Mock API setup - create test data
    await setupTestData(page);

    // Verify application is accessible
    console.log('🔍 Verifying application accessibility...');
    const baseURL = config.projects[0].use?.baseURL || 'http://localhost:3000';
    
    await page.goto(baseURL);
    await page.waitForLoadState('networkidle');
    
    // Check if the app loads correctly
    const title = await page.title();
    if (!title.includes('TokenTracker')) {
      throw new Error('Application failed to load properly');
    }

    console.log('✅ E2E test setup completed successfully');

  } catch (error) {
    console.error('❌ E2E test setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestData(page: any) {
  // Mock authentication state
  await page.addInitScript(() => {
    // Set up mock authentication
    localStorage.setItem('auth_token', 'mock-jwt-token-e2e');
    localStorage.setItem('user_id', 'e2e-test-user');
    localStorage.setItem('user_email', '<EMAIL>');
    
    // Mock user preferences
    localStorage.setItem('user_preferences', JSON.stringify({
      theme: 'light',
      language: 'en',
      timezone: 'America/New_York',
    }));
  });

  // Set up mock API responses
  await page.route('**/api/**', async (route: any) => {
    const url = route.request().url();
    const method = route.request().method();
    
    console.log(`🔄 Mocking API call: ${method} ${url}`);
    
    // Mock different endpoints
    if (url.includes('/api/portfolios') && method === 'GET') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            items: [
              {
                id: 'portfolio-1',
                name: 'Main Portfolio',
                description: 'Primary trading portfolio',
                total_value: 25000,
                total_pnl: 5000,
                total_pnl_percentage: 25,
                daily_pnl: 250,
                daily_pnl_percentage: 1.2,
                positions_count: 5,
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-07-24T12:00:00Z',
                is_active: true,
              },
              {
                id: 'portfolio-2',
                name: 'Conservative Portfolio',
                description: 'Low-risk portfolio',
                total_value: 15000,
                total_pnl: 1500,
                total_pnl_percentage: 11,
                daily_pnl: 75,
                daily_pnl_percentage: 0.5,
                positions_count: 3,
                created_at: '2024-02-01T00:00:00Z',
                updated_at: '2024-07-24T12:00:00Z',
                is_active: true,
              },
            ],
            total: 2,
            page: 1,
            page_size: 20,
            total_pages: 1,
          },
        }),
      });
    } else if (url.includes('/api/portfolios/') && url.includes('/positions') && method === 'GET') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            items: [
              {
                id: 'position-1',
                portfolio_id: 'portfolio-1',
                token_address: 'So11111111111111111111111111111111111111112',
                token_symbol: 'SOL',
                token_name: 'Solana',
                quantity: 100,
                average_price: 80,
                current_price: 100,
                market_value: 10000,
                total_invested: 8000,
                pnl: 2000,
                pnl_percentage: 25,
                allocation_percentage: 40,
                created_at: '2024-01-01T00:00:00Z',
                updated_at: '2024-07-24T12:00:00Z',
              },
              {
                id: 'position-2',
                portfolio_id: 'portfolio-1',
                token_address: '******************************************',
                token_symbol: 'ETH',
                token_name: 'Ethereum',
                quantity: 5,
                average_price: 2000,
                current_price: 2500,
                market_value: 12500,
                total_invested: 10000,
                pnl: 2500,
                pnl_percentage: 25,
                allocation_percentage: 50,
                created_at: '2024-01-15T00:00:00Z',
                updated_at: '2024-07-24T12:00:00Z',
              },
            ],
            total: 2,
            page: 1,
            page_size: 20,
            total_pages: 1,
          },
        }),
      });
    } else if (url.includes('/api/signals') && method === 'GET') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            items: [
              {
                id: 'signal-1',
                token_address: 'So11111111111111111111111111111111111111112',
                token_symbol: 'SOL',
                token_name: 'Solana',
                signal_type: 'buy',
                confidence: 85,
                current_price: 100,
                target_price: 120,
                stop_loss: 90,
                expected_return: 20,
                risk_score: 6,
                analysis: 'Strong bullish momentum with high volume',
                created_at: '2024-07-24T10:00:00Z',
                expires_at: '2024-07-25T10:00:00Z',
                status: 'active',
                suggested_quantity: 10,
              },
            ],
            total: 1,
            page: 1,
            page_size: 20,
            total_pages: 1,
          },
        }),
      });
    } else if (url.includes('/api/trades') && method === 'GET') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            items: [
              {
                id: 'trade-1',
                portfolio_id: 'portfolio-1',
                signal_id: 'signal-1',
                token_address: 'So11111111111111111111111111111111111111112',
                token_symbol: 'SOL',
                token_name: 'Solana',
                trade_type: 'buy',
                quantity: 10,
                price: 100,
                total_value: 1000,
                fees: 5,
                status: 'executed',
                executed_at: '2024-07-24T12:00:00Z',
                created_at: '2024-07-24T11:00:00Z',
                pnl: 50,
                pnl_percentage: 5,
                notes: 'Test trade',
              },
            ],
            total: 1,
            page: 1,
            page_size: 20,
            total_pages: 1,
          },
        }),
      });
    } else if (url.includes('/api/user/profile') && method === 'GET') {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {
            id: 'e2e-test-user',
            first_name: 'E2E',
            last_name: 'Test',
            email: '<EMAIL>',
            phone: '+1234567890',
            location: 'Test City',
            bio: 'E2E test user',
            timezone: 'America/New_York',
            language: 'en',
            avatar_url: null,
            created_at: '2024-01-01T00:00:00Z',
            last_login: '2024-07-24T12:00:00Z',
          },
        }),
      });
    } else {
      // Default mock response for unhandled endpoints
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          data: {},
          message: 'Mock response',
        }),
      });
    }
  });
}

export default globalSetup;
