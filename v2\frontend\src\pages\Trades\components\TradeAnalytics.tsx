import React from 'react';
import Card from '@/components/ui/Card';
import { Bar, Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface TradeAnalyticsProps {
  analyticsData: {
    summary: {
      total_trades: number;
      winning_trades: number;
      losing_trades: number;
      win_rate: number;
      total_pnl: number;
      total_pnl_percentage: number;
      average_win: number;
      average_loss: number;
      profit_factor: number;
      largest_win: number;
      largest_loss: number;
    };
    performance_by_month: {
      month: string;
      trades: number;
      pnl: number;
      win_rate: number;
    }[];
    performance_by_token: {
      token_symbol: string;
      trades: number;
      pnl: number;
      win_rate: number;
    }[];
    performance_by_signal_type: {
      signal_type: 'buy' | 'sell' | 'manual';
      trades: number;
      pnl: number;
      win_rate: number;
    }[];
  } | null;
  isLoading: boolean;
}

const TradeAnalytics: React.FC<TradeAnalyticsProps> = ({
  analyticsData,
  isLoading,
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[1, 2].map((i) => (
            <Card key={i} className="p-6">
              <div className="animate-pulse">
                <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
                <div className="h-64 bg-surface-tertiary rounded"></div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold text-text-primary mb-4">No Analytics Data</h3>
        <p className="text-text-muted">
          Analytics data is not available yet. Execute some trades to see performance metrics.
        </p>
      </Card>
    );
  }

  // Monthly performance chart data
  const monthlyPerformanceData = {
    labels: analyticsData.performance_by_month.map(item => item.month),
    datasets: [
      {
        label: 'Monthly P&L ($)',
        data: analyticsData.performance_by_month.map(item => item.pnl),
        backgroundColor: analyticsData.performance_by_month.map(item => 
          item.pnl >= 0 ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)'
        ),
        borderColor: analyticsData.performance_by_month.map(item => 
          item.pnl >= 0 ? 'rgba(16, 185, 129, 1)' : 'rgba(239, 68, 68, 1)'
        ),
        borderWidth: 1,
      },
    ],
  };

  const monthlyPerformanceOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Monthly P&L Performance',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          },
        },
      },
    },
  };

  // Performance by token chart
  const tokenPerformanceData = {
    labels: analyticsData.performance_by_token.slice(0, 10).map(item => item.token_symbol),
    datasets: [
      {
        label: 'P&L by Token ($)',
        data: analyticsData.performance_by_token.slice(0, 10).map(item => item.pnl),
        backgroundColor: analyticsData.performance_by_token.slice(0, 10).map(item => 
          item.pnl >= 0 ? 'rgba(16, 185, 129, 0.8)' : 'rgba(239, 68, 68, 0.8)'
        ),
        borderColor: analyticsData.performance_by_token.slice(0, 10).map(item => 
          item.pnl >= 0 ? 'rgba(16, 185, 129, 1)' : 'rgba(239, 68, 68, 1)'
        ),
        borderWidth: 1,
      },
    ],
  };

  const tokenPerformanceOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Performance by Token (Top 10)',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          },
        },
      },
    },
  };

  // Signal type distribution
  const signalTypeData = {
    labels: analyticsData.performance_by_signal_type.map(item => item.signal_type.toUpperCase()),
    datasets: [
      {
        data: analyticsData.performance_by_signal_type.map(item => item.trades),
        backgroundColor: [
          '#10B981', // Green for buy
          '#EF4444', // Red for sell
          '#6B7280', // Gray for manual
        ],
        borderWidth: 2,
        borderColor: '#1F2937',
      },
    ],
  };

  const signalTypeOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Trades by Signal Type',
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.parsed || 0;
            const total = analyticsData.summary.total_trades;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value} (${percentage}%)`;
          },
        },
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Average Win</div>
          <div className="text-2xl font-bold text-green-600">
            {formatCurrency(analyticsData.summary.average_win)}
          </div>
          <div className="text-sm text-text-muted">Per winning trade</div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Average Loss</div>
          <div className="text-2xl font-bold text-red-600">
            {formatCurrency(Math.abs(analyticsData.summary.average_loss))}
          </div>
          <div className="text-sm text-text-muted">Per losing trade</div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Largest Win</div>
          <div className="text-2xl font-bold text-green-600">
            {formatCurrency(analyticsData.summary.largest_win)}
          </div>
          <div className="text-sm text-text-muted">Best single trade</div>
        </Card>

        <Card className="p-4">
          <div className="text-sm text-text-secondary mb-1">Largest Loss</div>
          <div className="text-2xl font-bold text-red-600">
            {formatCurrency(Math.abs(analyticsData.summary.largest_loss))}
          </div>
          <div className="text-sm text-text-muted">Worst single trade</div>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Performance */}
        <Card className="p-6">
          <div className="h-64">
            <Bar data={monthlyPerformanceData} options={monthlyPerformanceOptions} />
          </div>
        </Card>

        {/* Signal Type Distribution */}
        <Card className="p-6">
          <div className="h-64">
            <Doughnut data={signalTypeData} options={signalTypeOptions} />
          </div>
        </Card>
      </div>

      {/* Token Performance Chart */}
      <Card className="p-6">
        <div className="h-64">
          <Bar data={tokenPerformanceData} options={tokenPerformanceOptions} />
        </div>
      </Card>

      {/* Performance Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance by Token */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Performance by Token</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-surface-tertiary">
                  <th className="text-left py-2 text-sm font-medium text-text-secondary">Token</th>
                  <th className="text-right py-2 text-sm font-medium text-text-secondary">Trades</th>
                  <th className="text-right py-2 text-sm font-medium text-text-secondary">Win Rate</th>
                  <th className="text-right py-2 text-sm font-medium text-text-secondary">P&L</th>
                </tr>
              </thead>
              <tbody>
                {analyticsData.performance_by_token.slice(0, 10).map((item, index) => (
                  <tr key={index} className="border-b border-surface-tertiary">
                    <td className="py-3 text-text-primary font-medium">{item.token_symbol}</td>
                    <td className="py-3 text-right text-text-primary">{item.trades}</td>
                    <td className="py-3 text-right text-text-primary">
                      {(item.win_rate * 100).toFixed(1)}%
                    </td>
                    <td
                      className={`py-3 text-right font-medium ${
                        item.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {formatCurrency(item.pnl)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>

        {/* Performance by Signal Type */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Performance by Signal Type</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-surface-tertiary">
                  <th className="text-left py-2 text-sm font-medium text-text-secondary">Type</th>
                  <th className="text-right py-2 text-sm font-medium text-text-secondary">Trades</th>
                  <th className="text-right py-2 text-sm font-medium text-text-secondary">Win Rate</th>
                  <th className="text-right py-2 text-sm font-medium text-text-secondary">P&L</th>
                </tr>
              </thead>
              <tbody>
                {analyticsData.performance_by_signal_type.map((item, index) => (
                  <tr key={index} className="border-b border-surface-tertiary">
                    <td className="py-3 text-text-primary font-medium">
                      {item.signal_type.toUpperCase()}
                    </td>
                    <td className="py-3 text-right text-text-primary">{item.trades}</td>
                    <td className="py-3 text-right text-text-primary">
                      {(item.win_rate * 100).toFixed(1)}%
                    </td>
                    <td
                      className={`py-3 text-right font-medium ${
                        item.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}
                    >
                      {formatCurrency(item.pnl)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      </div>

      {/* Monthly Performance Table */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Monthly Performance</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-surface-tertiary">
                <th className="text-left py-2 text-sm font-medium text-text-secondary">Month</th>
                <th className="text-right py-2 text-sm font-medium text-text-secondary">Trades</th>
                <th className="text-right py-2 text-sm font-medium text-text-secondary">Win Rate</th>
                <th className="text-right py-2 text-sm font-medium text-text-secondary">P&L</th>
              </tr>
            </thead>
            <tbody>
              {analyticsData.performance_by_month.map((item, index) => (
                <tr key={index} className="border-b border-surface-tertiary">
                  <td className="py-3 text-text-primary font-medium">{item.month}</td>
                  <td className="py-3 text-right text-text-primary">{item.trades}</td>
                  <td className="py-3 text-right text-text-primary">
                    {(item.win_rate * 100).toFixed(1)}%
                  </td>
                  <td
                    className={`py-3 text-right font-medium ${
                      item.pnl >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {formatCurrency(item.pnl)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default TradeAnalytics;
