import { ApiService } from './api';
import {
  DashboardData,
  PortfolioSummary,
  TimeSeriesData,
  MarketOverview,
  TradingSignal,
  Trade,
} from '@/types';

/**
 * Dashboard service for fetching dashboard-related data
 */
export class DashboardService {
  private static readonly BASE_URL = '/api/v1/web/dashboard';

  /**
   * Get complete dashboard data
   */
  static async getDashboardData(): Promise<DashboardData> {
    return ApiService.get<DashboardData>(`${this.BASE_URL}`);
  }

  /**
   * Get real-time dashboard updates
   */
  static async getRealtimeData(): Promise<Partial<DashboardData>> {
    return ApiService.get<Partial<DashboardData>>(`${this.BASE_URL}/realtime`);
  }

  /**
   * Get portfolio summary
   */
  static async getPortfolioSummary(): Promise<PortfolioSummary> {
    return ApiService.get<PortfolioSummary>(`${this.BASE_URL}/portfolio-summary`);
  }

  /**
   * Get portfolio performance chart data
   */
  static async getPortfolioChart(params: {
    timeframe: '1D' | '1W' | '1M' | '3M' | '1Y';
    portfolio_id?: string;
  }): Promise<TimeSeriesData[]> {
    return ApiService.get<TimeSeriesData[]>('/api/v1/web/charts/portfolio', params);
  }

  /**
   * Get performance chart data
   */
  static async getPerformanceChart(params: {
    timeframe: '1D' | '1W' | '1M' | '3M' | '1Y';
    portfolio_id?: string;
  }): Promise<TimeSeriesData[]> {
    return ApiService.get<TimeSeriesData[]>('/api/v1/web/charts/performance', params);
  }

  /**
   * Get signals chart data
   */
  static async getSignalsChart(params: {
    timeframe: '1D' | '1W' | '1M' | '3M' | '1Y';
  }): Promise<TimeSeriesData[]> {
    return ApiService.get<TimeSeriesData[]>('/api/v1/web/charts/signals', params);
  }

  /**
   * Get market overview data
   */
  static async getMarketOverview(): Promise<MarketOverview> {
    return ApiService.get<MarketOverview>('/api/v1/data/market/overview');
  }

  /**
   * Get recent trading signals
   */
  static async getRecentSignals(limit: number = 10): Promise<TradingSignal[]> {
    return ApiService.get<TradingSignal[]>('/api/v1/signals/active', { limit });
  }

  /**
   * Get recent trades
   */
  static async getRecentTrades(limit: number = 10): Promise<Trade[]> {
    return ApiService.get<Trade[]>('/api/v1/web/trades', { 
      page: 1, 
      page_size: limit,
      sort: 'created_at',
      order: 'desc'
    });
  }

  /**
   * Get token price data
   */
  static async getTokenPrices(tokenAddress: string, params?: {
    timeframe?: '1H' | '1D' | '1W' | '1M';
    limit?: number;
  }): Promise<TimeSeriesData[]> {
    return ApiService.get<TimeSeriesData[]>(
      `/api/v1/data/aggregate/${tokenAddress}`,
      params
    );
  }
}
