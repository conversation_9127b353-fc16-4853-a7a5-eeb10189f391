import { test, expect } from '@playwright/test';

// E2E tests for critical trading workflows
test.describe('Trading Workflow E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Mock authentication (in real app, this would be actual login)
    await page.evaluate(() => {
      localStorage.setItem('auth_token', 'mock-jwt-token');
      localStorage.setItem('user_id', 'test-user-123');
    });
    
    // Wait for app to load
    await page.waitForLoadState('networkidle');
  });

  test.describe('Portfolio Management Flow', () => {
    test('should create, view, and manage portfolio', async ({ page }) => {
      // Navigate to portfolios page
      await page.click('[data-testid="nav-portfolios"]');
      await expect(page).toHaveURL('/portfolios');

      // Create new portfolio
      await page.click('[data-testid="create-portfolio-btn"]');
      await page.fill('[data-testid="portfolio-name"]', 'Test Portfolio');
      await page.fill('[data-testid="portfolio-description"]', 'E2E Test Portfolio');
      await page.click('[data-testid="create-portfolio-submit"]');

      // Verify portfolio was created
      await expect(page.locator('[data-testid="portfolio-card"]')).toContainText('Test Portfolio');

      // View portfolio details
      await page.click('[data-testid="portfolio-card"]:has-text("Test Portfolio")');
      await expect(page.locator('[data-testid="portfolio-overview"]')).toBeVisible();

      // Check portfolio metrics
      await expect(page.locator('[data-testid="total-value"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-pnl"]')).toBeVisible();
      await expect(page.locator('[data-testid="daily-pnl"]')).toBeVisible();
    });

    test('should view and manage portfolio positions', async ({ page }) => {
      // Navigate to portfolio positions
      await page.click('[data-testid="nav-portfolios"]');
      await page.click('[data-testid="portfolio-card"]:first-child');
      await page.click('[data-testid="positions-tab"]');

      // Verify positions table is displayed
      await expect(page.locator('[data-testid="positions-table"]')).toBeVisible();
      await expect(page.locator('[data-testid="position-row"]').first()).toBeVisible();

      // Check position details
      const firstPosition = page.locator('[data-testid="position-row"]').first();
      await expect(firstPosition.locator('[data-testid="token-symbol"]')).toBeVisible();
      await expect(firstPosition.locator('[data-testid="quantity"]')).toBeVisible();
      await expect(firstPosition.locator('[data-testid="current-price"]')).toBeVisible();
      await expect(firstPosition.locator('[data-testid="market-value"]')).toBeVisible();
      await expect(firstPosition.locator('[data-testid="pnl"]')).toBeVisible();

      // Test position actions
      await firstPosition.locator('[data-testid="position-actions"]').click();
      await expect(page.locator('[data-testid="buy-more-btn"]')).toBeVisible();
      await expect(page.locator('[data-testid="sell-btn"]')).toBeVisible();
      await expect(page.locator('[data-testid="details-btn"]')).toBeVisible();
    });

    test('should perform portfolio rebalancing', async ({ page }) => {
      // Navigate to portfolio
      await page.click('[data-testid="nav-portfolios"]');
      await page.click('[data-testid="portfolio-card"]:first-child');

      // Open rebalance modal
      await page.click('[data-testid="rebalance-btn"]');
      await expect(page.locator('[data-testid="rebalance-modal"]')).toBeVisible();

      // Set target allocations
      await page.fill('[data-testid="allocation-SOL"]', '40');
      await page.fill('[data-testid="allocation-ETH"]', '35');
      await page.fill('[data-testid="allocation-BTC"]', '25');

      // Generate rebalance plan
      await page.click('[data-testid="generate-plan-btn"]');
      await expect(page.locator('[data-testid="rebalance-plan"]')).toBeVisible();

      // Review plan details
      await expect(page.locator('[data-testid="trades-required"]')).toBeVisible();
      await expect(page.locator('[data-testid="estimated-fees"]')).toBeVisible();
      await expect(page.locator('[data-testid="estimated-slippage"]')).toBeVisible();

      // Execute rebalancing (with confirmation)
      await page.click('[data-testid="execute-rebalance-btn"]');
      await page.click('[data-testid="confirm-rebalance-btn"]');

      // Verify success message
      await expect(page.locator('[data-testid="rebalance-success"]')).toBeVisible();
    });
  });

  test.describe('Trading Signals Flow', () => {
    test('should view and execute trading signals', async ({ page }) => {
      // Navigate to signals page
      await page.click('[data-testid="nav-signals"]');
      await expect(page).toHaveURL('/signals');

      // Verify active signals are displayed
      await expect(page.locator('[data-testid="active-signals"]')).toBeVisible();
      await expect(page.locator('[data-testid="signal-card"]').first()).toBeVisible();

      // Check signal details
      const firstSignal = page.locator('[data-testid="signal-card"]').first();
      await expect(firstSignal.locator('[data-testid="token-symbol"]')).toBeVisible();
      await expect(firstSignal.locator('[data-testid="signal-type"]')).toBeVisible();
      await expect(firstSignal.locator('[data-testid="confidence"]')).toBeVisible();
      await expect(firstSignal.locator('[data-testid="current-price"]')).toBeVisible();
      await expect(firstSignal.locator('[data-testid="target-price"]')).toBeVisible();

      // Execute signal
      await firstSignal.locator('[data-testid="execute-signal-btn"]').click();
      await expect(page.locator('[data-testid="execute-signal-modal"]')).toBeVisible();

      // Configure trade parameters
      await page.fill('[data-testid="trade-quantity"]', '10');
      await page.selectOption('[data-testid="portfolio-select"]', 'portfolio-1');

      // Confirm execution
      await page.click('[data-testid="confirm-execute-btn"]');
      await expect(page.locator('[data-testid="execution-success"]')).toBeVisible();
    });

    test('should generate new trading signals', async ({ page }) => {
      // Navigate to signals page
      await page.click('[data-testid="nav-signals"]');

      // Open signal generation modal
      await page.click('[data-testid="generate-signals-btn"]');
      await expect(page.locator('[data-testid="generate-signals-modal"]')).toBeVisible();

      // Configure signal parameters
      await page.selectOption('[data-testid="signal-type"]', 'buy');
      await page.fill('[data-testid="min-confidence"]', '70');
      await page.check('[data-testid="include-sol"]');
      await page.check('[data-testid="include-eth"]');
      await page.check('[data-testid="include-btc"]');

      // Generate signals
      await page.click('[data-testid="generate-btn"]');
      await expect(page.locator('[data-testid="generating-signals"]')).toBeVisible();

      // Verify new signals are displayed
      await expect(page.locator('[data-testid="new-signals"]')).toBeVisible();
      await expect(page.locator('[data-testid="signal-card"]')).toHaveCount(3);
    });

    test('should perform signal backtesting', async ({ page }) => {
      // Navigate to signals page
      await page.click('[data-testid="nav-signals"]');
      await page.click('[data-testid="signal-performance-tab"]');

      // Open backtest modal
      await page.click('[data-testid="backtest-btn"]');
      await expect(page.locator('[data-testid="backtest-modal"]')).toBeVisible();

      // Configure backtest parameters
      await page.fill('[data-testid="start-date"]', '2024-01-01');
      await page.fill('[data-testid="end-date"]', '2024-06-30');
      await page.fill('[data-testid="initial-capital"]', '10000');
      await page.selectOption('[data-testid="strategy"]', 'momentum');

      // Run backtest
      await page.click('[data-testid="run-backtest-btn"]');
      await expect(page.locator('[data-testid="backtest-running"]')).toBeVisible();

      // Verify backtest results
      await expect(page.locator('[data-testid="backtest-results"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-return"]')).toBeVisible();
      await expect(page.locator('[data-testid="win-rate"]')).toBeVisible();
      await expect(page.locator('[data-testid="max-drawdown"]')).toBeVisible();
      await expect(page.locator('[data-testid="sharpe-ratio"]')).toBeVisible();
    });
  });

  test.describe('Trade Management Flow', () => {
    test('should create and manage manual trades', async ({ page }) => {
      // Navigate to trades page
      await page.click('[data-testid="nav-trades"]');
      await expect(page).toHaveURL('/trades');

      // Create new trade
      await page.click('[data-testid="create-trade-btn"]');
      await expect(page.locator('[data-testid="create-trade-modal"]')).toBeVisible();

      // Fill trade details
      await page.selectOption('[data-testid="portfolio-select"]', 'portfolio-1');
      await page.selectOption('[data-testid="token-select"]', 'SOL');
      await page.selectOption('[data-testid="trade-type"]', 'buy');
      await page.fill('[data-testid="quantity"]', '10');
      await page.fill('[data-testid="price"]', '100');

      // Submit trade
      await page.click('[data-testid="submit-trade-btn"]');
      await expect(page.locator('[data-testid="trade-success"]')).toBeVisible();

      // Verify trade appears in history
      await expect(page.locator('[data-testid="trade-row"]').first()).toContainText('SOL');
      await expect(page.locator('[data-testid="trade-row"]').first()).toContainText('BUY');
    });

    test('should filter and search trade history', async ({ page }) => {
      // Navigate to trades page
      await page.click('[data-testid="nav-trades"]');

      // Test search functionality
      await page.fill('[data-testid="search-trades"]', 'SOL');
      await expect(page.locator('[data-testid="trade-row"]')).toHaveCount(2);

      // Clear search
      await page.fill('[data-testid="search-trades"]', '');

      // Test type filter
      await page.click('[data-testid="filter-buy"]');
      await expect(page.locator('[data-testid="trade-row"]:has-text("BUY")')).toHaveCount(3);

      // Test status filter
      await page.click('[data-testid="filter-executed"]');
      await expect(page.locator('[data-testid="trade-row"]:has-text("EXECUTED")')).toHaveCount(2);

      // Test date range filter
      await page.fill('[data-testid="date-from"]', '2024-07-01');
      await page.fill('[data-testid="date-to"]', '2024-07-31');
      await page.click('[data-testid="apply-date-filter"]');
    });

    test('should export trade data', async ({ page }) => {
      // Navigate to trades page
      await page.click('[data-testid="nav-trades"]');

      // Open export modal
      await page.click('[data-testid="export-btn"]');
      await expect(page.locator('[data-testid="export-modal"]')).toBeVisible();

      // Configure export
      await page.selectOption('[data-testid="export-format"]', 'csv');
      await page.check('[data-testid="include-pnl"]');
      await page.check('[data-testid="include-fees"]');

      // Start export
      const downloadPromise = page.waitForEvent('download');
      await page.click('[data-testid="export-download-btn"]');
      const download = await downloadPromise;

      // Verify download
      expect(download.suggestedFilename()).toContain('trades');
      expect(download.suggestedFilename()).toContain('.csv');
    });
  });

  test.describe('Settings and Profile Flow', () => {
    test('should update user profile settings', async ({ page }) => {
      // Navigate to settings
      await page.click('[data-testid="nav-settings"]');
      await expect(page).toHaveURL('/settings');

      // Edit profile
      await page.click('[data-testid="edit-profile-btn"]');

      // Update profile information
      await page.fill('[data-testid="first-name"]', 'John');
      await page.fill('[data-testid="last-name"]', 'Doe');
      await page.fill('[data-testid="phone"]', '+1234567890');
      await page.fill('[data-testid="location"]', 'San Francisco, CA');
      await page.selectOption('[data-testid="timezone"]', 'America/Los_Angeles');

      // Save changes
      await page.click('[data-testid="save-profile-btn"]');
      await expect(page.locator('[data-testid="profile-saved"]')).toBeVisible();

      // Verify changes are persisted
      await page.reload();
      await expect(page.locator('[data-testid="first-name"]')).toHaveValue('John');
      await expect(page.locator('[data-testid="last-name"]')).toHaveValue('Doe');
    });

    test('should configure notification preferences', async ({ page }) => {
      // Navigate to settings
      await page.click('[data-testid="nav-settings"]');
      await page.click('[data-testid="notifications-tab"]');

      // Configure email notifications
      await page.check('[data-testid="email-trade-executions"]');
      await page.check('[data-testid="email-portfolio-alerts"]');
      await page.uncheck('[data-testid="email-market-news"]');

      // Configure push notifications
      await page.check('[data-testid="push-price-alerts"]');
      await page.uncheck('[data-testid="push-market-news"]');

      // Set alert thresholds
      await page.fill('[data-testid="portfolio-change-threshold"]', '5');
      await page.fill('[data-testid="position-change-threshold"]', '10');

      // Save preferences
      await page.click('[data-testid="save-notifications-btn"]');
      await expect(page.locator('[data-testid="notifications-saved"]')).toBeVisible();
    });

    test('should manage API keys', async ({ page }) => {
      // Navigate to settings
      await page.click('[data-testid="nav-settings"]');
      await page.click('[data-testid="api-keys-tab"]');

      // Create new API key
      await page.click('[data-testid="create-api-key-btn"]');
      await page.fill('[data-testid="api-key-name"]', 'Trading Bot');
      await page.check('[data-testid="permission-read"]');
      await page.check('[data-testid="permission-trade"]');

      // Generate API key
      await page.click('[data-testid="generate-api-key-btn"]');
      await expect(page.locator('[data-testid="api-key-created"]')).toBeVisible();

      // Verify API key is listed
      await expect(page.locator('[data-testid="api-key-row"]')).toContainText('Trading Bot');

      // Test API key
      await page.click('[data-testid="test-api-key-btn"]');
      await expect(page.locator('[data-testid="api-key-test-success"]')).toBeVisible();

      // Delete API key
      await page.click('[data-testid="delete-api-key-btn"]');
      await page.click('[data-testid="confirm-delete-btn"]');
      await expect(page.locator('[data-testid="api-key-deleted"]')).toBeVisible();
    });
  });

  test.describe('Dashboard Overview Flow', () => {
    test('should display comprehensive dashboard overview', async ({ page }) => {
      // Navigate to dashboard
      await page.click('[data-testid="nav-dashboard"]');
      await expect(page).toHaveURL('/dashboard');

      // Verify key metrics are displayed
      await expect(page.locator('[data-testid="total-portfolio-value"]')).toBeVisible();
      await expect(page.locator('[data-testid="total-pnl"]')).toBeVisible();
      await expect(page.locator('[data-testid="daily-pnl"]')).toBeVisible();
      await expect(page.locator('[data-testid="active-signals-count"]')).toBeVisible();

      // Verify charts are rendered
      await expect(page.locator('[data-testid="performance-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="allocation-chart"]')).toBeVisible();

      // Verify recent activity
      await expect(page.locator('[data-testid="recent-trades"]')).toBeVisible();
      await expect(page.locator('[data-testid="recent-signals"]')).toBeVisible();

      // Test market overview
      await expect(page.locator('[data-testid="market-overview"]')).toBeVisible();
      await expect(page.locator('[data-testid="top-gainers"]')).toBeVisible();
      await expect(page.locator('[data-testid="top-losers"]')).toBeVisible();
    });
  });
});
