import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import { PortfolioService } from '@/services/portfolio';
import { Portfolio as PortfolioType, Position, PerformanceMetrics } from '@/types';
import PortfolioList from './components/PortfolioList';
import PortfolioDetails from './components/PortfolioDetails';
import CreatePortfolioModal from './components/CreatePortfolioModal';
import PortfolioAnalytics from './components/PortfolioAnalytics';
import PositionsList from './components/PositionsList';
import RebalanceModal from './components/RebalanceModal';

/**
 * Portfolio page component
 */
const Portfolio: React.FC = () => {
  const [selectedPortfolio, setSelectedPortfolio] = useState<PortfolioType | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showRebalanceModal, setShowRebalanceModal] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'positions' | 'analytics' | 'risk'>('overview');

  const queryClient = useQueryClient();

  // Fetch portfolios
  const { data: portfoliosData, isLoading: portfoliosLoading, error: portfoliosError } = useQuery({
    queryKey: ['portfolios'],
    queryFn: () => PortfolioService.getPortfolios(),
  });

  // Fetch selected portfolio details
  const { data: portfolioDetails, isLoading: detailsLoading } = useQuery({
    queryKey: ['portfolio', selectedPortfolio?.id],
    queryFn: () => selectedPortfolio ? PortfolioService.getPortfolio(selectedPortfolio.id) : null,
    enabled: !!selectedPortfolio,
  });

  // Fetch portfolio positions
  const { data: positions, isLoading: positionsLoading } = useQuery({
    queryKey: ['portfolio-positions', selectedPortfolio?.id],
    queryFn: () => selectedPortfolio ? PortfolioService.getPortfolioPositions(selectedPortfolio.id) : [],
    enabled: !!selectedPortfolio,
  });

  // Fetch portfolio performance
  const { data: performance, isLoading: performanceLoading } = useQuery({
    queryKey: ['portfolio-performance', selectedPortfolio?.id],
    queryFn: () => selectedPortfolio ? PortfolioService.getPortfolioPerformance(selectedPortfolio.id) : null,
    enabled: !!selectedPortfolio,
  });

  // Fetch portfolio analytics
  const { data: analytics, isLoading: analyticsLoading } = useQuery({
    queryKey: ['portfolio-analytics', selectedPortfolio?.id],
    queryFn: () => selectedPortfolio ? PortfolioService.getPortfolioAnalytics(selectedPortfolio.id) : null,
    enabled: !!selectedPortfolio,
  });

  // Create portfolio mutation
  const createPortfolioMutation = useMutation({
    mutationFn: PortfolioService.createPortfolio,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolios'] });
      setShowCreateModal(false);
    },
  });

  // Delete portfolio mutation
  const deletePortfolioMutation = useMutation({
    mutationFn: PortfolioService.deletePortfolio,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['portfolios'] });
      setSelectedPortfolio(null);
    },
  });

  // Auto-select first portfolio if none selected
  useEffect(() => {
    if (portfoliosData?.items && portfoliosData.items.length > 0 && !selectedPortfolio) {
      setSelectedPortfolio(portfoliosData.items[0]);
    }
  }, [portfoliosData, selectedPortfolio]);

  const handleCreatePortfolio = (portfolioData: any) => {
    createPortfolioMutation.mutate(portfolioData);
  };

  const handleDeletePortfolio = (portfolioId: string) => {
    if (window.confirm('Are you sure you want to delete this portfolio?')) {
      deletePortfolioMutation.mutate(portfolioId);
    }
  };

  const handlePortfolioSelect = (portfolio: PortfolioType) => {
    setSelectedPortfolio(portfolio);
    setActiveTab('overview');
  };

  if (portfoliosError) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-text-primary">Portfolio</h1>
          <p className="text-text-secondary mt-1">
            Manage your trading portfolios and positions.
          </p>
        </div>
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-4">Error Loading Portfolios</h2>
          <p className="text-text-muted mb-4">
            {portfoliosError instanceof Error ? portfoliosError.message : 'Failed to load portfolios'}
          </p>
          <Button onClick={() => queryClient.invalidateQueries({ queryKey: ['portfolios'] })}>
            Retry
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-text-primary">Portfolio</h1>
          <p className="text-text-secondary mt-1">
            Manage your trading portfolios and positions.
          </p>
        </div>
        <Button onClick={() => setShowCreateModal(true)} className="bg-primary hover:bg-primary-dark">
          Create Portfolio
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Portfolio List Sidebar */}
        <div className="lg:col-span-1">
          <PortfolioList
            portfolios={portfoliosData?.items || []}
            selectedPortfolio={selectedPortfolio}
            onPortfolioSelect={handlePortfolioSelect}
            onDeletePortfolio={handleDeletePortfolio}
            isLoading={portfoliosLoading}
          />
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {selectedPortfolio ? (
            <>
              {/* Portfolio Header */}
              <Card className="p-6 mb-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h2 className="text-2xl font-bold text-text-primary">{selectedPortfolio.name}</h2>
                    {selectedPortfolio.description && (
                      <p className="text-text-secondary mt-1">{selectedPortfolio.description}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowRebalanceModal(true)}
                      disabled={!positions || positions.length === 0}
                    >
                      Rebalance
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Export functionality
                        PortfolioService.exportPortfolio(selectedPortfolio.id, 'csv')
                          .then(blob => {
                            const url = window.URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `${selectedPortfolio.name}_portfolio.csv`;
                            a.click();
                          });
                      }}
                    >
                      Export
                    </Button>
                  </div>
                </div>
              </Card>

              {/* Tab Navigation */}
              <div className="flex space-x-1 mb-6">
                {[
                  { key: 'overview', label: 'Overview' },
                  { key: 'positions', label: 'Positions' },
                  { key: 'analytics', label: 'Analytics' },
                  { key: 'risk', label: 'Risk Assessment' },
                ].map((tab) => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key as any)}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      activeTab === tab.key
                        ? 'bg-primary text-white'
                        : 'bg-surface-secondary text-text-secondary hover:bg-surface-tertiary'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>

              {/* Tab Content */}
              {activeTab === 'overview' && (
                <PortfolioDetails
                  portfolio={portfolioDetails || selectedPortfolio}
                  performance={performance}
                  isLoading={detailsLoading || performanceLoading}
                />
              )}

              {activeTab === 'positions' && (
                <PositionsList
                  positions={positions || []}
                  isLoading={positionsLoading}
                />
              )}

              {activeTab === 'analytics' && (
                <PortfolioAnalytics
                  analytics={analytics}
                  isLoading={analyticsLoading}
                />
              )}

              {activeTab === 'risk' && (
                <div>
                  <Card className="p-6">
                    <h3 className="text-lg font-semibold text-text-primary mb-4">Risk Assessment</h3>
                    <p className="text-text-muted">Risk assessment features coming soon...</p>
                  </Card>
                </div>
              )}
            </>
          ) : (
            <Card className="p-8 text-center">
              <h2 className="text-xl font-semibold text-text-primary mb-4">
                No Portfolio Selected
              </h2>
              <p className="text-text-muted mb-4">
                Select a portfolio from the sidebar or create a new one to get started.
              </p>
              <Button onClick={() => setShowCreateModal(true)}>
                Create Your First Portfolio
              </Button>
            </Card>
          )}
        </div>
      </div>

      {/* Modals */}
      <CreatePortfolioModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreatePortfolio}
        isLoading={createPortfolioMutation.isPending}
      />

      {selectedPortfolio && (
        <RebalanceModal
          isOpen={showRebalanceModal}
          onClose={() => setShowRebalanceModal(false)}
          portfolio={selectedPortfolio}
          positions={positions || []}
        />
      )}
    </div>
  );
};

export default Portfolio;
