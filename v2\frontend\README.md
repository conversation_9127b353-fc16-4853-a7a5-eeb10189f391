# 🌐 TokenTracker v2 Frontend

Professional React TypeScript frontend for the TokenTracker v2 cryptocurrency trading automation platform.

## 🚀 Features

- **Modern React 18** with TypeScript and strict type checking
- **Responsive Design** with Tailwind CSS and mobile-first approach
- **Dark Theme** optimized for trading interfaces
- **Real-time Updates** via WebSocket integration
- **Advanced Charts** using Chart.js for portfolio and market data
- **Authentication** with JWT tokens and automatic refresh
- **State Management** using Zustand for optimal performance
- **Data Fetching** with TanStack Query for caching and synchronization
- **Form Handling** with React Hook Form and Zod validation
- **Testing** with Jest and React Testing Library
- **Docker Support** for containerized deployment

## 📁 Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── ui/              # Basic UI elements (Button, Input, etc.)
│   ├── charts/          # Chart components
│   ├── forms/           # Form components
│   ├── auth/            # Authentication components
│   └── layout/          # Layout components
├── pages/               # Page components
│   ├── Dashboard/       # Main dashboard
│   ├── Portfolio/       # Portfolio management
│   ├── Signals/         # Trading signals
│   ├── Trades/          # Trade history
│   ├── Analytics/       # Analytics & reports
│   └── Settings/        # User settings
├── services/            # API service layer
├── hooks/               # Custom React hooks
├── store/               # State management (Zustand)
├── types/               # TypeScript interfaces
├── utils/               # Utility functions
└── styles/              # Global styles
```

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+ 
- Docker (recommended for network-resilient development)

### Local Development

1. **Clone and navigate to frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Copy environment variables:**
   ```bash
   cp .env.example .env
   ```

4. **Start development server:**
   ```bash
   npm run dev
   ```

5. **Open browser:**
   Navigate to `http://localhost:3000`

### Docker Development

1. **Build development image:**
   ```bash
   docker build --target dev -t tokentracker-v2-frontend:dev .
   ```

2. **Run development container:**
   ```bash
   docker run -p 3000:3000 -v $(pwd):/app tokentracker-v2-frontend:dev
   ```

## 🐳 Production Deployment

### Docker Build

```bash
# Build production image
docker build --target production -t tokentracker-v2-frontend:latest .

# Run production container
docker run -p 3000:80 tokentracker-v2-frontend:latest
```

### Docker Compose

```bash
# Start with full stack
docker-compose up frontend

# Or start entire application
docker-compose up
```

## 🧪 Testing

### Run Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Structure

- **Unit Tests**: `src/components/**/__tests__/`
- **Integration Tests**: `src/test/integration/`
- **E2E Tests**: `src/test/e2e/`

## 📊 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
npm run type-check   # Run TypeScript type checking
```

## 🎨 Design System

### Color Palette

```css
/* Primary Colors */
--bg-primary: #0f172a;      /* Main background */
--bg-secondary: #1e293b;    /* Card backgrounds */
--bg-tertiary: #334155;     /* Hover states */

/* Text Colors */
--text-primary: #f8fafc;    /* Primary text */
--text-secondary: #cbd5e1;  /* Secondary text */
--text-muted: #64748b;      /* Muted text */

/* Accent Colors */
--accent-green: #10b981;    /* Profit/Buy signals */
--accent-red: #ef4444;      /* Loss/Sell signals */
--accent-blue: #3b82f6;     /* Info/Hold signals */
--accent-yellow: #f59e0b;   /* Warning states */
```

### Component Guidelines

- **Consistent Spacing**: Use Tailwind's spacing scale
- **Responsive Design**: Mobile-first approach
- **Accessibility**: WCAG 2.1 compliance
- **Performance**: Lazy loading and code splitting

## 🔧 Configuration

### Environment Variables

```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000

# Application Configuration
VITE_APP_NAME=TokenTracker v2
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_WEBSOCKETS=true
```

### Build Configuration

- **Vite**: Modern build tool with HMR
- **TypeScript**: Strict mode enabled
- **Tailwind CSS**: JIT compilation
- **ESLint**: Code quality enforcement
- **Prettier**: Code formatting

## 🔐 Security

- **Content Security Policy**: Configured in nginx
- **HTTPS**: Enforced in production
- **JWT Tokens**: Secure authentication
- **Input Validation**: Zod schema validation
- **XSS Protection**: React's built-in protection

## 📈 Performance

- **Code Splitting**: Lazy-loaded routes
- **Bundle Analysis**: Webpack bundle analyzer
- **Caching**: React Query for API caching
- **Compression**: Gzip compression in nginx
- **CDN Ready**: Static assets optimized

## 🤝 Contributing

1. Follow the established code style
2. Write tests for new features
3. Update documentation
4. Follow conventional commit messages
5. Ensure all checks pass

## 📝 License

This project is part of TokenTracker v2 and follows the same licensing terms.

## 🆘 Troubleshooting

### Common Issues

1. **Port 3000 in use**: Change port in vite.config.ts
2. **API connection issues**: Check VITE_API_BASE_URL
3. **WebSocket connection fails**: Verify VITE_WS_URL
4. **Build fails**: Clear node_modules and reinstall

### Debug Mode

Enable debug mode by setting `VITE_DEBUG_MODE=true` in your `.env` file.

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the backend API documentation
3. Check Docker logs: `docker-compose logs frontend`
