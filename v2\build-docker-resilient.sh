#!/bin/bash

# ===========================================
# 🐳 TOKENTRACKER V2 RESILIENT DOCKER BUILD
# ===========================================
# Build script optimized for poor network conditions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MAX_RETRIES=5
RETRY_DELAY=30
BUILD_TIMEOUT=3600  # 1 hour timeout
DOCKER_BUILDKIT=1

# Default values
TARGET="development"
TAG="tokentracker-v2"
CACHE_FROM=""
NO_CACHE=""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -t, --target TARGET     Build target (development, production, testing, monitoring)"
    echo "  -n, --tag TAG          Docker image tag (default: tokentracker-v2)"
    echo "  -c, --cache-from IMAGE Use cache from existing image"
    echo "  --no-cache             Build without cache"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -t production -n tokentracker-v2:latest"
    echo "  $0 --target development --cache-from tokentracker-v2:cache"
    echo "  $0 --no-cache"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--target)
            TARGET="$2"
            shift 2
            ;;
        -n|--tag)
            TAG="$2"
            shift 2
            ;;
        -c|--cache-from)
            CACHE_FROM="--cache-from $2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate target
case $TARGET in
    development|production|testing|monitoring)
        ;;
    *)
        print_error "Invalid target: $TARGET"
        print_error "Valid targets: development, production, testing, monitoring"
        exit 1
        ;;
esac

print_status "Starting resilient Docker build for TokenTracker V2"
print_status "Target: $TARGET"
print_status "Tag: $TAG"
print_status "Max retries: $MAX_RETRIES"
print_status "Retry delay: ${RETRY_DELAY}s"

# Enable BuildKit for better caching and performance
export DOCKER_BUILDKIT=1

# Function to build with retries
build_with_retries() {
    local attempt=1
    
    while [ $attempt -le $MAX_RETRIES ]; do
        print_status "Build attempt $attempt of $MAX_RETRIES"
        
        # Build command with network resilience options
        if timeout $BUILD_TIMEOUT docker build \
            --target $TARGET \
            --tag $TAG:$TARGET \
            --tag $TAG:latest \
            $CACHE_FROM \
            $NO_CACHE \
            --build-arg BUILDKIT_INLINE_CACHE=1 \
            --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
            --build-arg VERSION="$(git describe --tags --always --dirty 2>/dev/null || echo 'unknown')" \
            --build-arg VCS_REF="$(git rev-parse HEAD 2>/dev/null || echo 'unknown')" \
            --progress=plain \
            --network=host \
            .; then
            
            print_success "Docker build completed successfully!"
            return 0
        else
            print_warning "Build attempt $attempt failed"
            
            if [ $attempt -lt $MAX_RETRIES ]; then
                print_status "Waiting ${RETRY_DELAY} seconds before retry..."
                sleep $RETRY_DELAY
                
                # Clean up any partial builds
                print_status "Cleaning up partial builds..."
                docker system prune -f --filter "until=1h" 2>/dev/null || true
                
                # Increase retry delay for next attempt
                RETRY_DELAY=$((RETRY_DELAY + 10))
            fi
        fi
        
        attempt=$((attempt + 1))
    done
    
    print_error "All build attempts failed!"
    return 1
}

# Pre-build checks
print_status "Performing pre-build checks..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running or not accessible"
    exit 1
fi

# Check available disk space (require at least 2GB)
available_space=$(df . | tail -1 | awk '{print $4}')
required_space=2097152  # 2GB in KB
if [ $available_space -lt $required_space ]; then
    print_warning "Low disk space detected. Available: $(($available_space/1024))MB, Required: 2GB"
    print_status "Cleaning up Docker to free space..."
    docker system prune -f
fi

# Check network connectivity
print_status "Testing network connectivity..."
if ! curl -s --connect-timeout 10 https://pypi.org >/dev/null; then
    print_warning "Network connectivity to PyPI seems slow or unstable"
    print_status "Build will use extended timeouts and retries"
fi

# Start the build process
print_status "Starting Docker build with network resilience features..."
if build_with_retries; then
    print_success "Build completed successfully!"
    
    # Show image information
    print_status "Image information:"
    docker images | grep $TAG | head -5
    
    # Optional: Test the built image
    if [ "$TARGET" = "development" ] || [ "$TARGET" = "production" ]; then
        print_status "Testing built image..."
        if docker run --rm $TAG:$TARGET python -c "import sys; print(f'Python {sys.version}')"; then
            print_success "Image test passed!"
        else
            print_warning "Image test failed, but build was successful"
        fi
    fi
    
else
    print_error "Build failed after $MAX_RETRIES attempts"
    print_error "Check the logs above for details"
    exit 1
fi

print_success "Docker build process completed!"
