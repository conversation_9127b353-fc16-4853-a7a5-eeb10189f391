import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { SignalsService } from '@/services/signals';

const SignalConfiguration: React.FC = () => {
  const [activeSection, setActiveSection] = useState<'technical' | 'risk' | 'filters'>('technical');
  const [hasChanges, setHasChanges] = useState(false);

  const queryClient = useQueryClient();

  // Fetch current configuration
  const { data: config, isLoading, error } = useQuery({
    queryKey: ['signals', 'config'],
    queryFn: SignalsService.getSignalConfig,
  });

  // Local state for configuration
  const [localConfig, setLocalConfig] = useState<any>(null);

  // Update local config when data is loaded
  React.useEffect(() => {
    if (config && !localConfig) {
      setLocalConfig(config);
    }
  }, [config, localConfig]);

  // Update configuration mutation
  const updateConfigMutation = useMutation({
    mutationFn: SignalsService.updateSignalConfig,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['signals', 'config'] });
      setHasChanges(false);
    },
  });

  const handleConfigChange = (section: string, field: string, value: any) => {
    setLocalConfig((prev: any) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleSaveConfig = () => {
    if (localConfig) {
      updateConfigMutation.mutate(localConfig);
    }
  };

  const handleResetConfig = () => {
    setLocalConfig(config);
    setHasChanges(false);
  };

  if (isLoading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-surface-tertiary rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-16 bg-surface-tertiary rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-8 text-center">
        <h3 className="text-lg font-semibold text-red-600 mb-4">Error Loading Configuration</h3>
        <p className="text-text-muted">
          {error instanceof Error ? error.message : 'Failed to load signal configuration'}
        </p>
      </Card>
    );
  }

  if (!localConfig) {
    return null;
  }

  return (
    <div className="space-y-6">
      {/* Save/Reset Actions */}
      {hasChanges && (
        <Card className="p-4 bg-yellow-50 border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Unsaved Changes</h4>
              <p className="text-sm text-yellow-700">
                You have unsaved changes to your signal configuration.
              </p>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetConfig}
                disabled={updateConfigMutation.isPending}
              >
                Reset
              </Button>
              <Button
                size="sm"
                onClick={handleSaveConfig}
                disabled={updateConfigMutation.isPending}
                className="bg-primary hover:bg-primary-dark"
              >
                {updateConfigMutation.isPending ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Section Navigation */}
      <div className="flex space-x-1">
        {[
          { key: 'technical', label: 'Technical Indicators' },
          { key: 'risk', label: 'Risk Parameters' },
          { key: 'filters', label: 'Filters' },
        ].map((section) => (
          <button
            key={section.key}
            onClick={() => setActiveSection(section.key as any)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              activeSection === section.key
                ? 'bg-primary text-white'
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-tertiary'
            }`}
          >
            {section.label}
          </button>
        ))}
      </div>

      {/* Technical Indicators */}
      {activeSection === 'technical' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-6">Technical Indicators</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* RSI Settings */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-text-primary">RSI (Relative Strength Index)</h4>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Period
                </label>
                <Input
                  type="number"
                  value={localConfig.technical_indicators.rsi_period}
                  onChange={(e) => handleConfigChange('technical_indicators', 'rsi_period', parseInt(e.target.value))}
                  min="1"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Overbought Level
                </label>
                <Input
                  type="number"
                  value={localConfig.technical_indicators.rsi_overbought}
                  onChange={(e) => handleConfigChange('technical_indicators', 'rsi_overbought', parseInt(e.target.value))}
                  min="50"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Oversold Level
                </label>
                <Input
                  type="number"
                  value={localConfig.technical_indicators.rsi_oversold}
                  onChange={(e) => handleConfigChange('technical_indicators', 'rsi_oversold', parseInt(e.target.value))}
                  min="0"
                  max="50"
                />
              </div>
            </div>

            {/* MACD Settings */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-text-primary">MACD</h4>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Fast Period
                </label>
                <Input
                  type="number"
                  value={localConfig.technical_indicators.macd_fast}
                  onChange={(e) => handleConfigChange('technical_indicators', 'macd_fast', parseInt(e.target.value))}
                  min="1"
                  max="50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Slow Period
                </label>
                <Input
                  type="number"
                  value={localConfig.technical_indicators.macd_slow}
                  onChange={(e) => handleConfigChange('technical_indicators', 'macd_slow', parseInt(e.target.value))}
                  min="1"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Signal Period
                </label>
                <Input
                  type="number"
                  value={localConfig.technical_indicators.macd_signal}
                  onChange={(e) => handleConfigChange('technical_indicators', 'macd_signal', parseInt(e.target.value))}
                  min="1"
                  max="50"
                />
              </div>
            </div>

            {/* Bollinger Bands Settings */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-text-primary">Bollinger Bands</h4>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Period
                </label>
                <Input
                  type="number"
                  value={localConfig.technical_indicators.bb_period}
                  onChange={(e) => handleConfigChange('technical_indicators', 'bb_period', parseInt(e.target.value))}
                  min="1"
                  max="100"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Standard Deviation
                </label>
                <Input
                  type="number"
                  step="0.1"
                  value={localConfig.technical_indicators.bb_std_dev}
                  onChange={(e) => handleConfigChange('technical_indicators', 'bb_std_dev', parseFloat(e.target.value))}
                  min="0.1"
                  max="5"
                />
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Risk Parameters */}
      {activeSection === 'risk' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-6">Risk Parameters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Max Risk Per Trade (%)
              </label>
              <Input
                type="number"
                step="0.1"
                value={localConfig.risk_parameters.max_risk_per_trade}
                onChange={(e) => handleConfigChange('risk_parameters', 'max_risk_per_trade', parseFloat(e.target.value))}
                min="0.1"
                max="100"
              />
              <p className="text-sm text-text-muted mt-1">
                Maximum percentage of portfolio to risk on a single trade
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Stop Loss Percentage (%)
              </label>
              <Input
                type="number"
                step="0.1"
                value={localConfig.risk_parameters.stop_loss_percentage}
                onChange={(e) => handleConfigChange('risk_parameters', 'stop_loss_percentage', parseFloat(e.target.value))}
                min="0.1"
                max="50"
              />
              <p className="text-sm text-text-muted mt-1">
                Default stop loss percentage for signals
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Take Profit Ratio
              </label>
              <Input
                type="number"
                step="0.1"
                value={localConfig.risk_parameters.take_profit_ratio}
                onChange={(e) => handleConfigChange('risk_parameters', 'take_profit_ratio', parseFloat(e.target.value))}
                min="0.1"
                max="10"
              />
              <p className="text-sm text-text-muted mt-1">
                Ratio of take profit to stop loss (e.g., 2.0 = 2:1 reward:risk)
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Max Open Positions
              </label>
              <Input
                type="number"
                value={localConfig.risk_parameters.max_open_positions}
                onChange={(e) => handleConfigChange('risk_parameters', 'max_open_positions', parseInt(e.target.value))}
                min="1"
                max="100"
              />
              <p className="text-sm text-text-muted mt-1">
                Maximum number of open positions at any time
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Filters */}
      {activeSection === 'filters' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-text-primary mb-6">Signal Filters</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Minimum Market Cap (USD)
              </label>
              <Input
                type="number"
                value={localConfig.filters.min_market_cap}
                onChange={(e) => handleConfigChange('filters', 'min_market_cap', parseInt(e.target.value))}
                min="0"
              />
              <p className="text-sm text-text-muted mt-1">
                Only generate signals for tokens with market cap above this value
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Minimum 24h Volume (USD)
              </label>
              <Input
                type="number"
                value={localConfig.filters.min_volume_24h}
                onChange={(e) => handleConfigChange('filters', 'min_volume_24h', parseInt(e.target.value))}
                min="0"
              />
              <p className="text-sm text-text-muted mt-1">
                Only generate signals for tokens with 24h volume above this value
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Maximum Volatility (%)
              </label>
              <Input
                type="number"
                step="0.1"
                value={localConfig.filters.max_volatility}
                onChange={(e) => handleConfigChange('filters', 'max_volatility', parseFloat(e.target.value))}
                min="0"
                max="1000"
              />
              <p className="text-sm text-text-muted mt-1">
                Exclude tokens with volatility above this percentage
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Blacklisted Tokens
              </label>
              <textarea
                value={localConfig.filters.blacklisted_tokens.join('\n')}
                onChange={(e) => handleConfigChange('filters', 'blacklisted_tokens', e.target.value.split('\n').filter(Boolean))}
                className="w-full px-3 py-2 border border-surface-tertiary rounded-lg bg-surface-secondary text-text-primary"
                rows={4}
                placeholder="Enter token addresses, one per line"
              />
              <p className="text-sm text-text-muted mt-1">
                Token addresses to exclude from signal generation
              </p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default SignalConfiguration;
