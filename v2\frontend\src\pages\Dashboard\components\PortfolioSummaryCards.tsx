import React from 'react';
import { TrendingUp, TrendingDown, Wallet, Target, Activity } from 'lucide-react';
import { PortfolioSummary } from '@/types';
import Card from '@/components/ui/Card';

interface PortfolioSummaryCardsProps {
  data: PortfolioSummary;
}

/**
 * Portfolio summary cards component showing key metrics
 */
const PortfolioSummaryCards: React.FC<PortfolioSummaryCardsProps> = ({ data }) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const cards = [
    {
      title: 'Total Portfolio Value',
      value: formatCurrency(data.total_value),
      icon: Wallet,
      color: 'text-accent-blue',
      bgColor: 'bg-accent-blue/10',
    },
    {
      title: 'Daily P&L',
      value: formatCurrency(data.daily_pnl),
      subtitle: formatPercentage(data.daily_pnl_percentage),
      icon: data.daily_pnl >= 0 ? TrendingUp : TrendingDown,
      color: data.daily_pnl >= 0 ? 'text-accent-green' : 'text-accent-red',
      bgColor: data.daily_pnl >= 0 ? 'bg-accent-green/10' : 'bg-accent-red/10',
    },
    {
      title: 'Total P&L',
      value: formatCurrency(data.total_pnl),
      subtitle: formatPercentage(data.total_pnl_percentage),
      icon: data.total_pnl >= 0 ? TrendingUp : TrendingDown,
      color: data.total_pnl >= 0 ? 'text-accent-green' : 'text-accent-red',
      bgColor: data.total_pnl >= 0 ? 'bg-accent-green/10' : 'bg-accent-red/10',
    },
    {
      title: 'Active Positions',
      value: data.active_positions.toString(),
      subtitle: `${data.portfolio_count} portfolios`,
      icon: Target,
      color: 'text-accent-yellow',
      bgColor: 'bg-accent-yellow/10',
    },
    {
      title: 'Active Signals',
      value: data.active_signals.toString(),
      subtitle: 'Pending execution',
      icon: Activity,
      color: 'text-accent-blue',
      bgColor: 'bg-accent-blue/10',
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      {cards.map((card, index) => (
        <Card key={index} className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-text-muted mb-1">
                {card.title}
              </p>
              <p className="text-2xl font-bold text-text-primary mb-1">
                {card.value}
              </p>
              {card.subtitle && (
                <p className={`text-sm font-medium ${card.color}`}>
                  {card.subtitle}
                </p>
              )}
            </div>
            <div className={`p-3 rounded-lg ${card.bgColor}`}>
              <card.icon className={`w-6 h-6 ${card.color}`} />
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default PortfolioSummaryCards;
