import React, { useState } from 'react';
import { clsx } from 'clsx';
import { ChevronUp, ChevronDown, Search, Filter } from 'lucide-react';
import Button from './Button';
import Input from './Input';
import LoadingSpinner from './LoadingSpinner';

export interface Column<T> {
  key: keyof T | string;
  title: string;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, row: T, index: number) => React.ReactNode;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  onRowClick?: (row: T, index: number) => void;
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (pageSize: number) => void;
  };
  sorting?: {
    field: string;
    direction: 'asc' | 'desc';
    onSortChange: (field: string, direction: 'asc' | 'desc') => void;
  };
  filtering?: {
    onFilterChange: (filters: Record<string, string>) => void;
  };
  emptyMessage?: string;
  className?: string;
}

/**
 * Advanced data table component with sorting, filtering, and pagination
 */
function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  onRowClick,
  pagination,
  sorting,
  filtering,
  emptyMessage = 'No data available',
  className,
}: DataTableProps<T>) {
  const [localFilters, setLocalFilters] = useState<Record<string, string>>({});
  const [showFilters, setShowFilters] = useState(false);

  // Handle sorting
  const handleSort = (column: Column<T>) => {
    if (!column.sortable || !sorting) return;

    const field = column.key as string;
    const newDirection = 
      sorting.field === field && sorting.direction === 'asc' ? 'desc' : 'asc';
    
    sorting.onSortChange(field, newDirection);
  };

  // Handle filtering
  const handleFilterChange = (columnKey: string, value: string) => {
    const newFilters = { ...localFilters, [columnKey]: value };
    setLocalFilters(newFilters);
    filtering?.onFilterChange(newFilters);
  };

  // Get cell value
  const getCellValue = (row: T, column: Column<T>) => {
    const value = row[column.key as keyof T];
    return column.render ? column.render(value, row, 0) : value;
  };

  // Render pagination
  const renderPagination = () => {
    if (!pagination) return null;

    const { page, pageSize, total, onPageChange, onPageSizeChange } = pagination;
    const totalPages = Math.ceil(total / pageSize);
    const startItem = (page - 1) * pageSize + 1;
    const endItem = Math.min(page * pageSize, total);

    return (
      <div className="flex items-center justify-between px-6 py-4 border-t border-border">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-text-muted">Show</span>
          <select
            value={pageSize}
            onChange={(e) => onPageSizeChange(Number(e.target.value))}
            className="px-3 py-1 bg-bg-primary border border-border rounded text-text-primary text-sm"
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
          <span className="text-sm text-text-muted">entries</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-sm text-text-muted">
            Showing {startItem} to {endItem} of {total} entries
          </span>
          
          <div className="flex space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page - 1)}
              disabled={page <= 1}
            >
              Previous
            </Button>
            
            {/* Page numbers */}
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const pageNum = Math.max(1, Math.min(totalPages - 4, page - 2)) + i;
              return (
                <Button
                  key={pageNum}
                  variant={page === pageNum ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => onPageChange(pageNum)}
                >
                  {pageNum}
                </Button>
              );
            })}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page + 1)}
              disabled={page >= totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-bg-secondary rounded-lg border border-border">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className={clsx('bg-bg-secondary rounded-lg border border-border overflow-hidden', className)}>
      {/* Filters */}
      {filtering && (
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-text-primary">Filters</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="w-4 h-4 mr-2" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </Button>
          </div>
          
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {columns
                .filter(column => column.filterable)
                .map(column => (
                  <Input
                    key={column.key as string}
                    placeholder={`Filter by ${column.title}`}
                    value={localFilters[column.key as string] || ''}
                    onChange={(e) => handleFilterChange(column.key as string, e.target.value)}
                    icon={<Search className="w-4 h-4" />}
                  />
                ))}
            </div>
          )}
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-bg-tertiary">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key as string}
                  className={clsx(
                    'px-6 py-4 text-left text-sm font-medium text-text-primary',
                    {
                      'cursor-pointer hover:bg-bg-primary/50': column.sortable,
                      'text-center': column.align === 'center',
                      'text-right': column.align === 'right',
                    }
                  )}
                  style={{ width: column.width }}
                  onClick={() => handleSort(column)}
                >
                  <div className="flex items-center space-x-2">
                    <span>{column.title}</span>
                    {column.sortable && sorting && (
                      <div className="flex flex-col">
                        <ChevronUp
                          className={clsx('w-3 h-3', {
                            'text-accent-blue': sorting.field === column.key && sorting.direction === 'asc',
                            'text-text-muted': !(sorting.field === column.key && sorting.direction === 'asc'),
                          })}
                        />
                        <ChevronDown
                          className={clsx('w-3 h-3 -mt-1', {
                            'text-accent-blue': sorting.field === column.key && sorting.direction === 'desc',
                            'text-text-muted': !(sorting.field === column.key && sorting.direction === 'desc'),
                          })}
                        />
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className="px-6 py-12 text-center text-text-muted"
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              data.map((row, index) => (
                <tr
                  key={index}
                  className={clsx(
                    'border-b border-border transition-colors',
                    {
                      'hover:bg-bg-tertiary cursor-pointer': onRowClick,
                    }
                  )}
                  onClick={() => onRowClick?.(row, index)}
                >
                  {columns.map((column) => (
                    <td
                      key={column.key as string}
                      className={clsx(
                        'px-6 py-4 text-sm text-text-primary',
                        {
                          'text-center': column.align === 'center',
                          'text-right': column.align === 'right',
                        }
                      )}
                    >
                      {getCellValue(row, column)}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {renderPagination()}
    </div>
  );
}

export default DataTable;
